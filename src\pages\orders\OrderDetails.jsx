import React from 'react';
import { useParams } from 'react-router-dom';
import useDataFetching from '@/hooks/useDataFetching';
import Select from 'react-select';
import api from '@/lib/axios';
import { toast } from 'sonner';

const OrderDetails = () => {
  const { orderId } = useParams();

  const { data: order, isLoading, refetch } = useDataFetching({
    queryKey: ['order', orderId],
    endPoint: `/admin/orders/${orderId}`,
  });

  const statusOptions = [
    { value: 'pending', label: 'Pending' },
    { value: 'processing', label: 'Processing' },
    { value: 'shipped', label: 'Shipped' },
    { value: 'delivered', label: 'Delivered' },
    { value: 'cancelled', label: 'Cancelled' },
    { value: 'refunded', label: 'Refunded' },
    { value: 'failed', label: 'Failed' }
  ];

  const handleStatusChange = async (newStatus) => {
    try {
      await api.post(`/admin/orders/${orderId}/update-status`, { status: newStatus });
      refetch();
    } catch (error) {
      toast.error('Failed to update order status');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
      </div>
    );
  }

  const orderData = order?.data;

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-5xl mx-auto bg-white rounded-lg shadow-sm">
        {/* Header */}
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-semibold text-gray-800">
              Order #{orderData?.tracking_number}
            </h1>
            <div className="w-48">
              <Select
                options={statusOptions}
                value={statusOptions.find(option => option.value === orderData?.status)}
                onChange={(selected) => handleStatusChange(selected.value)}
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* Order Information */}
        <div className="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Customer Information */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-700">Customer Information</h2>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center space-x-4">
                <img
                  src={orderData?.user?.profile_picture_url}
                  alt={orderData?.user?.name}
                  className="w-12 h-12 rounded-full object-cover"
                />
                <div>
                  <p className="font-medium text-gray-800">{orderData?.user?.name}</p>
                  <p className="text-sm text-gray-500">Customer ID: {orderData?.user?.id}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Payment Information */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-700">Payment Information</h2>
            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Subtotal:</span>
                <span className="font-medium">${parseFloat(orderData?.subtotal).toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Discount:</span>
                <span className="font-medium">${parseFloat(orderData?.discount).toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Tax:</span>
                <span className="font-medium">${parseFloat(orderData?.tax_amount).toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Shipping:</span>
                <span className="font-medium">${parseFloat(orderData?.shipping_fee).toFixed(2)}</span>
              </div>
              <div className="border-t pt-2 mt-2 flex justify-between">
                <span className="font-semibold">Total:</span>
                <span className="font-semibold">${parseFloat(orderData?.total_amount).toFixed(2)}</span>
              </div>
              {orderData?.coupon_id && (
                <div className="flex justify-between text-sm text-gray-500">
                  <span>Coupon Applied:</span>
                  <span>ID: {orderData.coupon_id}</span>
                </div>
              )}
            </div>
          </div>

          {/* Order Status */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-700">Order Status</h2>
            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Order Status:</span>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  orderData?.status === 'completed' ? 'bg-green-100 text-green-800' :
                  orderData?.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                  orderData?.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {orderData?.status.charAt(0).toUpperCase() + orderData?.status.slice(1)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Payment Status:</span>
                <span className={`px-2 py-1 rounded-full text-xs ${orderData?.payment_status === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                  {orderData?.payment_status.charAt(0).toUpperCase() + orderData?.payment_status.slice(1)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Order Date:</span>
                <span className="font-medium">{new Date(orderData?.created_at).toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Last Updated:</span>
                <span className="font-medium">{new Date(orderData?.updated_at).toLocaleDateString()}</span>
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-700">Additional Information</h2>
            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Total Items:</span>
                <span className="font-medium">{orderData?.total_quantity}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Pre-order:</span>
                <span className="font-medium">{orderData?.is_preorder ? 'Yes' : 'No'}</span>
              </div>
              {orderData?.tenant_id && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Tenant ID:</span>
                  <span className="font-medium">{orderData.tenant_id}</span>
                </div>
              )}
              {orderData?.notes && (
                <div className="pt-2">
                  <span className="text-gray-600 block mb-1">Notes:</span>
                  <p className="text-gray-800">{orderData?.notes}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Order Items */}
        <div className="p-6 border-t">
          <h2 className="text-lg font-semibold text-gray-700 mb-4">Order Items</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subtotal</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {orderData?.order_items?.map((item) => (
                  <tr key={item.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {item.item?.cover_image_url && (
                          <img
                            src={item.item.cover_image_url}
                            alt={item.item.title}
                            className="w-12 h-16 object-cover mr-4"
                          />
                        )}
                        <div>
                          <div className="text-sm font-medium text-gray-900">{item.item?.title}</div>
                          <div className="text-sm text-gray-500">{item.item?.book_type}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      ${parseFloat(item.price).toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.quantity}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      ${parseFloat(item.subtotal).toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        item.status === 'completed' ? 'bg-green-100 text-green-800' :
                        item.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                        item.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderDetails;