import { useEffect, useState, useCallback, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { resetToInitialState, setEbookData } from "../store/ebookSlice";
import { useParams, useSearchParams } from "react-router-dom";
import { toast } from "sonner";
import axios from "axios";

/**
 * Custom hook for fetching ebook data with pagination support
 * Fetches pages in chunks to improve performance for large books
 */
export function useEbook() {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const urlToken = searchParams.get('token');

  const dispatch = useDispatch();
  const reduxToken = useSelector((state) => state.auth?.token);

  // Use URL token if available, otherwise fall back to redux token
  const token = urlToken || reduxToken;

  // State for ebook data and loading status
  const [ebook, setEbook] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // State for pagination
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [initialDataLoaded, setInitialDataLoaded] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMorePages, setHasMorePages] = useState(true);
  const [totalPages, setTotalPages] = useState(0);

  // Pagination settings
  const abortControllerRef = useRef(null);
  const fetchInitiatedRef = useRef(null); // Track current fetch key
  const mountedRef = useRef(true); // Track if component is mounted

  // Store stable references to avoid dependency issues
  const stableRefs = useRef({
    id: null,
    token: null,
    baseApiUrl: import.meta.env.VITE_HOST_URL,
    chunkSize: 50,
    dispatch: null
  });

  // Always update stable refs
  stableRefs.current.id = id;
  stableRefs.current.token = token;
  stableRefs.current.dispatch = dispatch;



  /**
   * Load more pages
   */
  const loadMorePages = useCallback(async () => {
    if (!hasMorePages || isLoadingMore || !mountedRef.current) return;

    setIsLoadingMore(true);

    try {
      const nextPage = currentPage + 1;
      const { id: currentId, token: currentToken, baseApiUrl, chunkSize } = stableRefs.current;

      // Create a new abort controller for this request
      const controller = new AbortController();

      const response = await axios.get(`${baseApiUrl}/api/single-ebook/${currentId}`, {
        headers: {
          Authorization: `Bearer ${currentToken}`,
        },
        params: {
          per_page: chunkSize,
          pagination: true,
          page: nextPage
        },
        signal: controller.signal
      });

      // Check if component is still mounted
      if (!mountedRef.current) return;

      const data = response.data;
      if (!data) return; // Request was cancelled

      // Extract pages from the response - your API returns pages directly in data.pages
      const newPages = data.data?.pages || [];

      // If we got no new pages, we've reached the end
      if (newPages.length === 0) {
        setHasMorePages(false);
        setIsLoadingMore(false);
        return;
      }

      // Update the ebook data with the new pages
      setEbook(prevEbook => {
        if (!prevEbook) return prevEbook;

        // Get existing pages
        const existingPages = prevEbook.pages || [];

        // Merge new pages with existing pages, avoiding duplicates by ID
        const existingPageIds = new Set(existingPages.map(page => page.id));
        const uniqueNewPages = newPages.filter(page => !existingPageIds.has(page.id));

        // Create updated pages array
        const updatedPages = [...existingPages, ...uniqueNewPages];

        // Update the ebook data with the new pages and pagination metadata
        const updatedEbook = {
          ...prevEbook,
          pages: updatedPages,
          current_page: data.data?.current_page || prevEbook.current_page,
          per_page: data.data?.per_page || prevEbook.per_page,
          total_items: data.data?.total_items || prevEbook.total_items,
          total_pages: data.data?.total_pages || prevEbook.total_pages
        };

        // Also update Redux store
        stableRefs.current.dispatch(setEbookData(updatedEbook));

        return updatedEbook;
      });

      // Update pagination state
      setCurrentPage(nextPage);
      setLoadingProgress(Math.min(100, (nextPage / totalPages) * 100));

      // Check if we have more pages to load
      const stillHasMore = nextPage < totalPages;
      setHasMorePages(stillHasMore);

    } catch (error) {
      // Don't handle error if request was aborted or component unmounted
      if (error.name === 'CanceledError' || error.name === 'AbortError' || !mountedRef.current) {
        return;
      }
      console.error('Error loading more pages:', error);
      toast.error('Failed to load more pages');
    } finally {
      if (mountedRef.current) {
        setIsLoadingMore(false);
      }
    }
  }, [currentPage, hasMorePages, isLoadingMore, totalPages]); // Removed external dependencies

  /**
   * Initial fetch of ebook data
   */
  useEffect(() => {
    console.log('useEbook useEffect triggered with:', { id, token: token ? 'present' : 'missing' });

    // Don't fetch if we don't have required data
    if (!id || !token) {
      console.log('Missing id or token, setting loading to false');
      setIsLoading(false);
      return;
    }

    let isCancelled = false;

    const fetchInitialData = async () => {
      // Create a unique key for this fetch attempt
      const fetchKey = `${id}-${token}`;
      console.log('Attempting fetch with key:', fetchKey);

      // Don't fetch if we already have data for this key or if fetch is in progress
      if (fetchInitiatedRef.current === fetchKey) {
        console.log('Fetch already initiated for this key, skipping');
        return;
      }

      // Mark this specific fetch as initiated
      fetchInitiatedRef.current = fetchKey;
      console.log('Starting fetch for:', fetchKey);

      setIsLoading(true);
      setError(null);
      setInitialDataLoaded(false);
      setCurrentPage(1);
      setHasMorePages(true);
      setLoadingProgress(0);

      // Cancel any ongoing requests
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create a new abort controller for this request
      abortControllerRef.current = new AbortController();

      try {
        const { baseApiUrl, chunkSize } = stableRefs.current;

        const response = await axios.get(`${baseApiUrl}/api/single-ebook/${id}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
          params: {
            per_page: chunkSize,
            pagination: true,
            page: 1
          },
          signal: abortControllerRef.current.signal
        });

        // Check if component is still mounted and this fetch wasn't cancelled
        if (!mountedRef.current || isCancelled || fetchInitiatedRef.current !== fetchKey) {
          console.log('Fetch cancelled or component unmounted');
          return;
        }

        const data = response.data;
        if (!data) {
          console.log('No data received from API');
          return; // Request was cancelled
        }

        console.log('API response received:', data);

        // Extract initial data
        const initialData = data.data;

        // Set initial data to state
        setEbook(initialData);
        dispatch(setEbookData(initialData));
        setInitialDataLoaded(true);

        console.log('Ebook data set successfully:', initialData);

        // Get pagination metadata from the response
        const totalItems = initialData.total_items || 0;
        const calculatedTotalPages = initialData.total_pages || Math.ceil(totalItems / chunkSize);
        setTotalPages(calculatedTotalPages);

        // Check if we need to fetch more pages
        setHasMorePages(calculatedTotalPages > 1);
        setCurrentPage(1);
        setLoadingProgress(Math.min(100, (1 / calculatedTotalPages) * 100));

      } catch (error) {
        console.log('Fetch error:', error);
        // Don't handle error if request was aborted or component unmounted
        if (error.name === 'CanceledError' || error.name === 'AbortError' || !mountedRef.current || isCancelled) {
          console.log('Request was cancelled or component unmounted');
          return;
        }

        const errorMessage = error.response?.data?.message || "Failed to fetch ebook data";
        console.log('Setting error:', errorMessage);
        setError(errorMessage);
        toast.error(errorMessage);
        dispatch(resetToInitialState());
        fetchInitiatedRef.current = null; // Reset on error
      } finally {
        console.log('Fetch finally block, setting loading to false');
        if (mountedRef.current && !isCancelled) {
          setIsLoading(false);
        }
      }
    };

    // Small delay to prevent rapid calls
    const timeoutId = setTimeout(fetchInitialData, 50);

    // Cleanup function
    return () => {
      isCancelled = true;
      clearTimeout(timeoutId);
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [id, token, dispatch]); // Direct dependencies

  /**
   * Auto-load more pages continuously until all pages are loaded
   */
  useEffect(() => {
    // Only start loading more pages after initial data is loaded
    if (!initialDataLoaded || !hasMorePages || isLoadingMore) return;

    // Start loading the next chunk of pages with a small delay
    const timer = setTimeout(() => {
      loadMorePages();
    }, 500); // Reduced delay for faster loading

    return () => clearTimeout(timer);
  }, [initialDataLoaded, hasMorePages, isLoadingMore, loadMorePages]); // Removed currentPage from dependencies to prevent unnecessary re-runs

  // Cleanup effect to handle component unmounting
  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      fetchInitiatedRef.current = false;
    };
  }, []);

  /**
   * Refresh ebook data - force re-fetch from server
   */
  const refreshEbookData = useCallback(async () => {
    if (!id || !token) return;

    console.log('Refreshing ebook data...');

    // Reset the fetch initiated flag to allow re-fetch
    fetchInitiatedRef.current = null;

    // Reset state
    setIsLoading(true);
    setError(null);
    setInitialDataLoaded(false);
    setCurrentPage(1);
    setHasMorePages(true);
    setLoadingProgress(0);

    // Cancel any ongoing requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create a new abort controller for this request
    abortControllerRef.current = new AbortController();

    try {
      const { baseApiUrl, chunkSize } = stableRefs.current;

      const response = await axios.get(`${baseApiUrl}/api/single-ebook/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: {
          per_page: chunkSize,
          pagination: true,
          page: 1
        },
        signal: abortControllerRef.current.signal
      });

      // Check if component is still mounted
      if (!mountedRef.current) {
        console.log('Component unmounted during refresh');
        return;
      }

      const data = response.data;
      if (!data) {
        console.log('No data received from API during refresh');
        return;
      }

      console.log('Refresh API response received:', data);

      // Extract initial data
      const initialData = data.data;

      // Set initial data to state
      setEbook(initialData);
      dispatch(setEbookData(initialData));
      setInitialDataLoaded(true);

      console.log('Ebook data refreshed successfully:', initialData);

      // Get pagination metadata from the response
      const totalItems = initialData.total_items || 0;
      const calculatedTotalPages = initialData.total_pages || Math.ceil(totalItems / chunkSize);
      setTotalPages(calculatedTotalPages);

      // Check if we need to fetch more pages
      setHasMorePages(calculatedTotalPages > 1);
      setCurrentPage(1);
      setLoadingProgress(Math.min(100, (1 / calculatedTotalPages) * 100));

    } catch (error) {
      console.log('Refresh error:', error);
      // Don't handle error if request was aborted or component unmounted
      if (error.name === 'CanceledError' || error.name === 'AbortError' || !mountedRef.current) {
        console.log('Refresh request was cancelled or component unmounted');
        return;
      }

      const errorMessage = error.response?.data?.message || "Failed to refresh ebook data";
      console.log('Setting refresh error:', errorMessage);
      setError(errorMessage);
      toast.error(errorMessage);
      dispatch(resetToInitialState());
    } finally {
      console.log('Refresh finally block, setting loading to false');
      if (mountedRef.current) {
        setIsLoading(false);
      }
    }
  }, [id, token, dispatch]);

  /**
   * Silent refresh - updates data without showing loading screen
   */
  const silentRefreshEbookData = useCallback(async () => {
    if (!id || !token) return;

    console.log('Silent refresh of ebook data...');

    try {
      const { baseApiUrl, chunkSize } = stableRefs.current;

      const response = await axios.get(`${baseApiUrl}/api/single-ebook/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: {
          per_page: chunkSize,
          pagination: true,
          page: 1
        }
      });

      // Check if component is still mounted
      if (!mountedRef.current) {
        console.log('Component unmounted during silent refresh');
        return;
      }

      const data = response.data;
      if (!data) {
        console.log('No data received from API during silent refresh');
        return;
      }

      console.log('Silent refresh API response received:', data);

      // Extract initial data
      const initialData = data.data;

      // Update data without changing loading states
      setEbook(initialData);
      dispatch(setEbookData(initialData));

      console.log('Ebook data silently refreshed successfully:', initialData);

      // Update pagination metadata
      const totalItems = initialData.total_items || 0;
      const calculatedTotalPages = initialData.total_pages || Math.ceil(totalItems / chunkSize);
      setTotalPages(calculatedTotalPages);
      setHasMorePages(calculatedTotalPages > 1);

      return initialData; // Return the fresh data

    } catch (error) {
      console.log('Silent refresh error:', error);
      // Don't handle error if request was aborted or component unmounted
      if (error.name === 'CanceledError' || error.name === 'AbortError' || !mountedRef.current) {
        console.log('Silent refresh request was cancelled or component unmounted');
        return;
      }

      const errorMessage = error.response?.data?.message || "Failed to refresh ebook data";
      console.log('Setting silent refresh error:', errorMessage);
      toast.error(errorMessage);
      throw error; // Re-throw so caller can handle
    }
  }, [id, token, dispatch]);

  return {
    ebook,
    isLoading,
    isLoadingMore,
    loadingProgress,
    initialDataLoaded,
    hasMorePages,
    loadMorePages,
    error,
    refreshEbookData,
    silentRefreshEbookData,
  };
}