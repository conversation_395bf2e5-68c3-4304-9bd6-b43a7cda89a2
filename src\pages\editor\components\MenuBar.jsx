import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import api from "@/lib/axios";
import { useParams } from "react-router-dom";
import { Icon } from "@iconify/react";
import { setScale } from "../store/pagesSlice";

export default function MenuBar() {
  const dispatch = useDispatch();
  const scale = useSelector((state) => state.pages.present.scale);
  const [isFileMenuOpen, setIsFileMenuOpen] = useState(false);
  const [isPublished, setIsPublished] = useState(false);
  const { id } = useParams();
  const menuRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsFileMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    const fetchPublishStatus = async () => {
      try {
        const response = await api.get(`admin/ebooks/${id}`);
        setIsPublished(response.data.is_published);
      } catch (error) {
        console.error("Failed to fetch publish status:", error);
      }
    };

    if (id) {
      fetchPublishStatus();
    }
  }, [id]);

  const handlePublish = async () => {
    try {
      const formData = new FormData();
      formData.append("is_published", !isPublished);
      formData.append("_method", "PUT");

      await api.post(`admin/ebooks/${id}`, formData);
      setIsPublished(!isPublished);
      setIsFileMenuOpen(false);
    } catch (error) {
      console.error("Failed to update publish status:", error);
    }
  };

  const handleExport = () => {
    console.log("Export as PDF clicked");
    setIsFileMenuOpen(false);
  };

  const handleScaleChange = (e) => {
    dispatch(setScale(parseFloat(e.target.value)));
  };

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => setIsFileMenuOpen(!isFileMenuOpen)}
        className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-gray-600 dark:text-gray-300 flex items-center gap-2"
      >
        <Icon icon="material-symbols:file" className="text-lg" />
        <span className="text-sm font-medium">View</span>
        <Icon 
          icon="material-symbols:keyboard-arrow-down" 
          className={`text-lg transition-transform ${isFileMenuOpen ? 'rotate-180' : ''}`}
        />
      </button>

      {isFileMenuOpen && (
        <div className="absolute left-0 top-full mt-1 z-50 bg-white dark:bg-gray-800 border dark:border-gray-700 rounded-lg shadow-lg min-w-[200px] py-1 overflow-hidden">
          {/* Menu Items */}
          {/* <button
            onClick={handleExport}
            className="w-full px-4 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
          >
            <Icon icon="material-symbols:download" className="text-lg" />
            <span>Export as PDF</span>
          </button> */}

          {/* <button
            onClick={handlePublish}
            className="w-full px-4 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
          >
            <Icon icon="material-symbols:publish" className="text-lg" />
            <span>{isPublished ? 'Unpublish' : 'Publish'}</span>
          </button> */}

          {/* Divider */}
          <div className="h-[1px] bg-gray-200 dark:bg-gray-700 my-1" />

          {/* Scale Controller */}
          <div className="px-4 py-2">
            <div className="flex items-center gap-2">
              <Icon icon="mdi:magnify" className="text-gray-500" />
              <span className="text-sm text-gray-600 dark:text-gray-400">Zoom</span>
              <span className="ml-auto text-sm text-gray-600 dark:text-gray-400">
                {(scale * 100).toFixed(0)}%
              </span>
            </div>
            <input
              type="range"
              min="0.5"
              max="2"
              step="0.1"
              value={scale}
              onChange={handleScaleChange}
              className="w-full mt-2 accent-blue-500"
            />
          </div>
        </div>
      )}
    </div>
  );
}
