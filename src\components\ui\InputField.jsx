import React, { forwardRef } from "react";
import { useField } from "formik";

const InputField = forwardRef(({ label, required, type, error = '', onInputChange, ...props }, ref) => {
  const [field, meta] = useField(props);
  const isError = meta.touched && meta.error;

  const handleChange = (e) => {
    field.onChange(e);
    if (onInputChange) {
      onInputChange(e.target.value); // Pass the input value to parent
    }
  };

  return (
    <div>
      {label && (
        <label
          htmlFor={props.id || props.name}
          className="block text-[#000000] text-base font-normal mb-2"
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <input
        {...field}
        {...props}
        type={type}
        className={`appearance-none border rounded h-10 w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none ${
          isError ? "border-red-500" : ""
        }`}
        ref={ref}
        onChange={handleChange}
      />
      {error && <span className="text-red-500 text-xs">{error}</span>}
      {isError && <span className="text-red-500 text-xs">{meta.error}</span>}
    </div>
  );
});

export default InputField;
