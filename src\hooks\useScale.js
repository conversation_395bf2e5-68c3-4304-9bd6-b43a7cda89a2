import { useState, useEffect } from "react";

export const useScale = (minScale = 0.5, maxScale = 1, baseWidth = 900) => {
  const [scale, setScale] = useState(1);

  useEffect(() => {
    const handleResize = () => {
      const newScale = Math.max(minScale, Math.min(maxScale, window.innerWidth / baseWidth));
      setScale(newScale);
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, [minScale, maxScale, baseWidth]);

  return scale;
};

export const useZoomableScale = (minScale = 0.5, maxScale = 2, baseWidth = 900) => {
  const [scale, setScale] = useState(1);
  const [manualScale, setManualScale] = useState(1);

  useEffect(() => {
    const handleResize = () => {
      const autoScale = Math.max(
        minScale,
        Math.min(maxScale, window.innerWidth / baseWidth)
      );
      setScale(autoScale * manualScale);
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, [minScale, maxScale, baseWidth, manualScale]);

  const zoomIn = () => {
    setManualScale(prev => Math.min(2, prev + 0.1));
  };

  const zoomOut = () => {
    setManualScale(prev => Math.max(0.5, prev - 0.1));
  };

  const resetZoom = () => {
    setManualScale(1);
  };

  return {
    scale,
    zoomIn,
    zoomOut,
    resetZoom,
    currentZoom: manualScale
  };
};