import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useLocation } from "react-router-dom";
import { Formik } from "formik";
import * as Yup from "yup";
import FormInput from "../components/FormInput";
import { useAuth } from "../hooks/useAuth";
import { Icon } from "@iconify/react/dist/iconify.js";
import { GoogleLogin, GoogleOAuthProvider } from "@react-oauth/google";
import { toast } from "sonner";
import api from "../lib/axios";

const loginSchema = Yup.object().shape({
  email: Yup.string().email("Invalid email").required("Required"),
  password: Yup.string().required("Required"),
});

const Login = () => {
  const { isAuthenticated } = useSelector((state) => state.auth);
  const { login: loginAuth, isLoading, error } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const [showPassword, setShowPassword] = useState(false);

  const handleGoogleLogin = async (response) => {
    try {
      console.log("Google login response:", response);
      const { data } = await api.post("/auth/google/callback", {
        token: response?.credential,
      });
      if (data) {
        loginAuth({
          token: data.token,
          user: data.user
        });

        // Redirect logic
        const queryParams = new URLSearchParams(location.search);
        const redirectPath = queryParams.get("redirect");
        const fromRoute =
          location.state?.from?.pathname || redirectPath || "/admin";
        navigate(fromRoute, { replace: true });
      }
    } catch (error) {
      console.error("Google login failed:", error);
      toast.error("Failed to login with Google. Please try again.");
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      const fromRoute = location.state?.from?.pathname || "/";
      navigate(fromRoute, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full space-y-8 p-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <div>
          <h2 className="text-center text-3xl font-bold text-gray-900 dark:text-white">
            Sign in to your account
          </h2>
        </div>

        <Formik
          initialValues={{ email: "", password: "" }}
          validationSchema={loginSchema}
          onSubmit={loginAuth}
        >
          {({ handleSubmit }) => (
            <form onSubmit={handleSubmit} className="mt-8 space-y-6">
              <FormInput
                label="Email Address"
                name="email"
                type="email"
                placeholder="Enter your email"
              />

              <div className="relative">
                <FormInput
                  label="Password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Enter your password"
                />

                <Icon
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-9 text-gray-600 cursor-pointer hover:text-gray-800 transition-colors"
                  icon={
                    showPassword ? "iconamoon:eye-off-light" : "clarity:eye-line"
                  }
                  width="18"
                  height="18"
                />
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {isLoading ? "Signing in..." : "Sign in"}
              </button>
            </form>
          )}
        </Formik>
        <div className="mt-4 flex justify-center">
          <GoogleOAuthProvider clientId="************-60icmcv2lq8pj7s5ir6i06v787btjqb2.apps.googleusercontent.com">
            <GoogleLogin
              onSuccess={handleGoogleLogin}
              onError={() => {
                console.error("Google login failed");
                toast.error("Failed to login with Google. Please try again.");
              }}
              className="w-full"
            />
          </GoogleOAuthProvider>
        </div>
        <div className="mt-4 text-center">
          <p>
            Don't have an account?{" "}
            <a href="/register" className="text-green-600 hover:text-green-700">
              Sign up
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
