import React from 'react';
import { useDispatch,useSelector } from 'react-redux';
import { addElement } from '../../store/pagesSlice';

const ImageGrid = ({ images, isPicsum = false, isPixabay = false }) => {
  const dispatch = useDispatch();
  const width = useSelector((state) => state.ebook.width) - 60;

  // Adds image directly to the page
  const handleAddImage = (imageUrl) => {
    const img = new Image();
    img.src = imageUrl;
    
    img.onload = () => {
      const originalWidth = img.naturalWidth;
      const originalHeight = img.naturalHeight;
      let finalWidth = originalWidth;
      let finalHeight = originalHeight;

      // If image is wider than page width, scale it down proportionally
      if (originalWidth > width) {
        const scale = width / originalWidth;
        finalWidth = width;
        finalHeight = originalHeight * scale;
      }

      dispatch(
        addElement({
          type: 'image',
          content: imageUrl,
          position: { x: 50, y: 50 },
          size: { width: finalWidth, height: finalHeight },
        })
      );
    };
  };

  // Handles drag start and sets type and content in the dataTransfer object
  const handleDragStart = (e, imageUrl) => {
    e.dataTransfer.setData('type', 'image');
    e.dataTransfer.setData('content', imageUrl);
  };

  return (
    <div className="grid grid-cols-2 gap-2">
      {images.map((image) => {
        const imageUrl = isPicsum
          ? image.download_url
          : isPixabay
          ? image.previewURL
          : 'https://via.placeholder.com/150';

        return (
          <div
            key={image.id}
            className="cursor-pointer border dark:border-gray-600 border-gray-200 rounded overflow-hidden"
            draggable
            onDragStart={(e) => handleDragStart(e, image.largeImageURL)}
            onClick={() => handleAddImage(image.largeImageURL)}
          >
            <img
              src={imageUrl}
              alt={image.tags || 'Image'}
              className="w-full h-full object-cover"
            />
          </div>
        );
      })}
    </div>
  );
};

export default ImageGrid;
