import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { updateElement } from '../../store/pagesSlice';

function FlashcardProperties({ onSubmit, element }) {
  const [frontContent, setFrontContent] = useState(element.content.frontContent || '');
  const [backContent, setBackContent] = useState(element.content.backContent || '');
  const [selectedSide, setSelectedSide] = useState('front');
  const [selectedStyleOption, setSelectedStyleOption] = useState(element.content.styleOption || 'Basic Flip');
  const dispatch = useDispatch(); // Added useDispatch hook

  const handleSubmit = (e) => {
    e.preventDefault();
    dispatch(updateElement({
      id: element.id,
      updates: {
        content: {
          frontContent,
          backContent,
          styleOption: selectedStyleOption,
        },
      },
    }));
  };


  const handleSideChange = (e) => {
    setSelectedSide(e.target.value);
  };

  const handleStyleOptionChange = (e) => {
    setSelectedStyleOption(e.target.value);
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="max-w-md bg-white rounded"
    >
      <div className="mb-4">
        <label
          htmlFor="question"
          className="block text-gray-700 text-sm font-bold mb-2"
        >
          {selectedSide === 'front' ? 'Question' : 'Answer'}
        </label>
        <textarea
          id="question"
          value={selectedSide === 'front' ? frontContent : backContent}
          onChange={(e) => selectedSide === 'front' ? setFrontContent(e.target.value) : setBackContent(e.target.value)}
          className="w-full px-3 py-2 border rounded focus:outline-none focus:ring focus:border-blue-300 resize-y"
          placeholder={selectedSide === 'front' ? 'Enter the question' : 'Enter the answer'}
          required
        />
      </div>
      <div className="mb-4">
        <label
          htmlFor="side"
          className="block text-gray-700 text-sm font-bold mb-2"
        >
          Select Side
        </label>
        <select
          value={selectedSide}
          onChange={handleSideChange}
          className="block w-full rounded-md border-gray-300 shadow-sm text-sm p-1"
        >
          <option value="front">Front</option>
          <option value="back">Back</option>
        </select>
      </div>
      <div className="mb-4">
        <label
          htmlFor="styleOption"
          className="block text-gray-700 text-sm font-bold mb-2"
        >
          Select Style Option
        </label>
        <select
          value={selectedStyleOption}
          onChange={handleStyleOptionChange}
          className="block w-full rounded-md border-gray-300 shadow-sm text-sm p-1"
        >
          <option value="basic-flip">Basic Flip</option>
          <option value="fade-card">Fade Card</option>
          <option value="flip-3d">3D Flip</option>
          <option value="slide-card">Slide Card</option>
          <option value="tilt-card">Tilt Card</option>
        </select>
      </div>
    <button
      type="submit"
      className="mt-4 w-full py-2 px-4 bg-blue-500 text-white font-bold rounded hover:bg-blue-700 transition duration-300"
    >
      Save Changes
    </button>
    </form>
  );
}

export default FlashcardProperties;
