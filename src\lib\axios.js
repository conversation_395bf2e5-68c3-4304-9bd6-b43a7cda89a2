import axios from 'axios';
import { toast } from "sonner";
import store from '../store/store';
import { logout } from '../store/authSlice';
import { API_URL } from '@/config';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
});

// Request interceptor for adding token
api.interceptors.request.use((config) => {
  const token = store.getState().auth.token;
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for handling success/failure
api.interceptors.response.use(
  (response) => {
    // Show toast for successful responses except for GET requests
    if (response?.config?.showToast !== false && response?.config?.method !== 'get') {
      toast.success(response?.data?.message || 'Request successful!');
    }
    return response;
  },
  (error) => {
    // Show error toast for failed requests except for GET requests
    if (error?.config?.showToast !== false && error?.config?.method !== 'get') {
      const errorMessage = error?.response?.data?.message || 'Request failed. Please try again.';
      toast.error(errorMessage);
    }

    // Handle 401 error by dispatching logout action and redirecting to login
    if (error.response?.status === 401) {
      store.dispatch(logout());
      const currentPath = window.location.pathname + window.location.search;
      const loginPath = `/login?redirect=${encodeURIComponent(currentPath)}`;
      window.location.href = loginPath;
    }

    // Handle 403 error by navigating to unauthorized page
    if (error.response?.status === 403) {
      window.location.href = '/unauthorized';
    }

    return Promise.reject(error);
  }
);

export default api;

