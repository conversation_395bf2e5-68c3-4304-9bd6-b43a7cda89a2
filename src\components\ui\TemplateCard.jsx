import React, { useState } from 'react';
import { Eye, FileText, MoreVertical } from 'lucide-react';
import noImage from '@/assets/noImage.png';

const EbookCard = ({ book, onView, menuItems = [], onUse }) => {
  const [showOverlay, setShowOverlay] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const hasActions = Boolean(onView) || (Array.isArray(menuItems) && menuItems.length > 0);

  return (
    <div
      className="relative group bg-white rounded-lg shadow-md overflow-hidden"
      onMouseEnter={() => setShowOverlay(hasActions)}
      onMouseLeave={() => {
        setShowOverlay(false);
        setShowMenu(false);
      }}
    >
      <div className="relative">
        <img
          src={book.cover_image ? `${import.meta.env.VITE_HOST_URL}/storage/${book.cover_image}` :  noImage}
          alt={book.title}
          className="w-full h-[250px] object-cover"
          onClick={onView}
        />
        {showOverlay && (
          <div className="absolute inset-0 bg-black bg-opacity-40 transition-opacity duration-200 flex items-start justify-end p-2">
            <div className="flex space-x-2">
              {onView && (
                <button
                  onClick={onView}
                  className="p-2 bg-white rounded-full hover:bg-gray-100 transition-colors duration-200"
                  aria-label="View"
                >
                  <Eye className="w-5 h-5 text-gray-800" />
                </button>
              )}
              {Array.isArray(menuItems) && menuItems.length > 0 && (
                <div className="relative">
                  <button
                    onClick={() => setShowMenu(!showMenu)}
                    className="p-2 bg-white rounded-full hover:bg-gray-100 transition-colors duration-200"
                    aria-label="More options"
                  >
                    <MoreVertical className="w-5 h-5 text-gray-800" />
                  </button>
                  {showMenu && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                      <div className="py-1">
                        {menuItems.map((item, index) => (
                          <React.Fragment key={index}>
                            <button
                              onClick={() => {
                                item.action();
                                setShowMenu(false);
                              }}
                              className={`flex items-center w-full text-left px-4 py-2 text-sm ${item.danger ? 'text-red-600' : 'text-gray-700'} hover:bg-gray-100`}
                            >
                              {item.icon && <span className="mr-2">{item.icon}</span>}
                              {item.label}
                            </button>
                            {index < menuItems.length - 1 && (
                              <hr className="my-1 border-t border-gray-200 w-full" />
                            )}
                          </React.Fragment>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
      <div className="p-4">
        <h3 className="font-semibold text-gray-800 truncate">{book.title}</h3>
        <p className="text-sm text-gray-600 mt-1">{book.author}</p>
        {onUse && (
          <button
            onClick={onUse}
            className="mt-3 flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FileText className="w-4 h-4 mr-2" />
            Use this template
          </button>
        )}
      </div>
    </div>
  );
};

export default EbookCard;
