import React, { useState, useEffect } from "react";
import useDataFetching from "@/hooks/useDataFetching";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import api from "@/lib/axios";
import { useQueryClient } from "@tanstack/react-query";
import { Edit, Trash } from 'lucide-react';
import Confirm from "@/components/ui/Confirm";
import { useTranslation } from "react-i18next";
import PublishersForm from "./PublishersForm";

const PublishersList = () => {
    const { t } = useTranslation();
    const [state, setState] = useState({
        page: 1,
        pageSize: 10,
        search: "",
        debouncedSearch: "",
        isModalOpen: false,
        isEditMode: false,
        currentPublisher: null,
    });

    const queryClient = useQueryClient();

    const { data: publishersData, isLoading, refetch } = useDataFetching({
        queryKey: ["publisherList", state.page, state.pageSize, state.debouncedSearch],
        endPoint: `admin/publishers?page=${state.page}&per_page=${state.pageSize}&search=${state.debouncedSearch}`,
    });

    useEffect(() => {
        const delayDebounceFn = setTimeout(() => {
            setState((prev) => ({ ...prev, debouncedSearch: state.search }));
        }, 500);

        return () => clearTimeout(delayDebounceFn);
    }, [state.search]);

    const handleModalOpen = (editMode = false, publisher = null) => {
        setState((prev) => ({
            ...prev,
            isModalOpen: true,
            isEditMode: editMode,
            currentPublisher: publisher,
        }));
    };

    const handleModalClose = () => {
        setState((prev) => ({ ...prev, isModalOpen: false, currentPublisher: null }));
    };

    const handleDeleteClick = async (publisherId) => {
        Confirm(async () => {
            try {
                await api.delete(`admin/publishers/${publisherId}`);
                queryClient.invalidateQueries("publisherList");
            } catch (error) {
                console.error(t("publishers.deleteError"), error);
            }
        });
    };

    const handleFormSubmit = async (values, formikHelpers = {}) => {
        const { resetForm } = formikHelpers;
        try {
            const formData = new FormData();
            Object.keys(values).forEach((key) => {
                if (values[key] instanceof FileList) {
                    if (values[key].length > 0) {
                        formData.append(key, values[key][0]);
                    }
                } else {
                    formData.append(key, values[key]);
                }
            });

            if (state.isEditMode) {
                formData.append("_method", "PUT");
                await api.post(`admin/publishers/${state.currentPublisher.id}`, formData, {
                    headers: { "Content-Type": "multipart/form-data" },
                });
            } else {
                await api.post("admin/publishers", formData, {
                    headers: { "Content-Type": "multipart/form-data" },
                });
            }

            queryClient.invalidateQueries("publisherList");
            if (resetForm) resetForm();
            handleModalClose();
        } catch (error) {
            console.error(t("publishers.submitError"), error);
        }
    };

    const columns = [
        {
            Header: t("publishers.name"),
            accessor: "name",
        },
        {
            Header: t("publishers.mobile"),
            accessor: "mobile",
            Cell: ({ row }) => row.original?.mobile || "N/A",
        },
        {
            Header: t("publishers.email"),
            accessor: "user.email",
            Cell: ({ row }) => row.original?.user?.email || "N/A",
        },
        {
            Header: t("publishers.tradeLicense"),
            accessor: "trade_license_number",
            Cell: ({ row }) => row.original?.trade_license_number || "N/A",
        },
        {
            Header: t("publishers.action"),
            accessor: "id",
            Cell: ({ value, row }) => (
                <div className="flex justify-center">
                    <button
                        className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-2 rounded-lg"
                        onClick={() => handleModalOpen(true, row.original)}
                    >
                        <Edit size={16} />
                    </button>
                    <button
                        className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-2 rounded-lg ml-2"
                        onClick={() => handleDeleteClick(value)}
                    >
                        <Trash size={16} />
                    </button>
                </div>
            ),
        },
    ];

    return (
        <>
            <DataTable
                title={t("publishers.title")}
                columns={columns}
                data={publishersData?.data?.data || []}
                fetchData={refetch}
                loading={isLoading}
                totalPages={publishersData?.data?.total_pages || 1}
                currentPage={publishersData?.data?.current_page || 1}
                pageSize={state.pageSize}
                onPageChange={(page) => setState((prev) => ({ ...prev, page }))}
                onPageSizeChange={(pageSize) => setState((prev) => ({ ...prev, pageSize }))}
                onSearch={(search) => setState((prev) => ({ ...prev, search }))}
                buttonLabel={t("publishers.addPublisher")}
                onButtonClick={() => handleModalOpen(false)}
            />

            {state.isModalOpen && (
                <Modal
                    activeModal={state.isModalOpen}
                    onClose={handleModalClose}
                    className="max-w-5xl"
                    title={state.isEditMode ? t("publishers.editPublisher") : t("publishers.addPublisher")}
                >
                    <PublishersForm
                        initialValues={{
                            name: state.currentPublisher?.name || '',
                            mobile: state.currentPublisher?.mobile || '',
                            office_email: state.currentPublisher?.office_email || '',
                            trade_license_number: state.currentPublisher?.trade_license_number || '',
                            nid: state.currentPublisher?.nid || '',
                            nid_image: state.currentPublisher?.nid_image || '',
                            proprietor_photo: state.currentPublisher?.proprietor_photo || '',
                            proprietor_signature: state.currentPublisher?.proprietor_signature || '',
                            present_address: state.currentPublisher?.present_address || '',
                            permanent_address: state.currentPublisher?.permanent_address || '',
                            trade_license_image: state.currentPublisher?.trade_license_image || '',
                            logo: state.currentPublisher?.logo || '',
                            etin: state.currentPublisher?.etin || '',
                            vat_number: state.currentPublisher?.vat_number || '',
                            office_mobile: state.currentPublisher?.office_mobile || '',
                            office_phone: state.currentPublisher?.office_phone || '',
                            office_present_address: state.currentPublisher?.office_present_address || '',
                            office_permanent_address: state.currentPublisher?.office_permanent_address || '',
                            is_active: state.currentPublisher?.is_active ? '1' : '0',
                            email: state.currentPublisher?.user?.email || '',
                        }}
                        onSubmit={handleFormSubmit}
                        isEditMode={state.isEditMode}
                        onClose={handleModalClose}
                    />
                </Modal>
            )}
        </>
    );
};

export default PublishersList;
