// AlignmentProperties.jsx
import React from 'react';
import { AlignLeft, AlignCenter, AlignRight, AlignJustify, ArrowUp, ArrowDown, ArrowLeft, ArrowRight } from 'lucide-react';

const AlignmentProperties = ({ handleAlignment }) => (
  <div className="flex flex-row gap-4 flex-wrap max-w-[300px]">
    <button
      onClick={() => handleAlignment('left')}
     className="p-2 bg-gray-200 rounded hover:bg-gray-300" 
      title="Align Left"
    >
      <AlignLeft size={16} />
    </button>
    <button
      onClick={() => handleAlignment('center')}
     className="p-2 bg-gray-200 rounded hover:bg-gray-300" 
      title="Align Center"
    >
      <AlignCenter size={16} />
    </button>
    <button
      onClick={() => handleAlignment('right')}
     className="p-2 bg-gray-200 rounded hover:bg-gray-300" 
      title="Align Right"
    >
      <AlignRight size={16} />
    </button>
    <button
      onClick={() => handleAlignment('justify')}
     className="p-2 bg-gray-200 rounded hover:bg-gray-300" 
      title="Justify"
    >
      <AlignJustify size={16} />
    </button>
    <button
      onClick={() => handleAlignment('top')}
     className="p-2 bg-gray-200 rounded hover:bg-gray-300" 
      title="Align Top"
    >
      <ArrowUp size={16} />
    </button>
    <button
      onClick={() => handleAlignment('middle')}
     className="p-2 bg-gray-200 rounded hover:bg-gray-300" 
      title="Align Middle"
    >
      <ArrowLeft size={16} />
    </button>
    <button
      onClick={() => handleAlignment('bottom')}
     className="p-2 bg-gray-200 rounded hover:bg-gray-300" 
      title="Align Bottom"
    >
      <ArrowDown size={16} />
    </button>
    <button
      onClick={() => handleAlignment('left')}
     className="p-2 bg-gray-200 rounded hover:bg-gray-300" 
      title="Align Left"
    >
      <ArrowLeft size={16} />
    </button>
    <button
      onClick={() => handleAlignment('right')}
     className="p-2 bg-gray-200 rounded hover:bg-gray-300" 
      title="Align Right"
    >
      <ArrowRight size={16} />
    </button>
  </div>
);

export default AlignmentProperties;
