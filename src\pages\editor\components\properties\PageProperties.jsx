import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Icon } from "@iconify/react";
import { Link2, Unlink } from "lucide-react";
import {
  updatePageStyle,
  updatePageLayout,
  toggleGuide,
} from "../../store/pagesSlice";
import api from "@/lib/axios";

const PageProperties = () => {
  const dispatch = useDispatch();
  const pages = useSelector((state) => state.pages.present.pages);
  const currentPage = useSelector((state) => state.pages.present.currentPage);
  const showGuide = useSelector((state) => state.pages.present.showGuide);

  const id = pages.length ? pages[currentPage]?.id : null;

  const {
    backgroundColor = "#ffffff",
    backgroundImage = "",
    margins = { top: 12.7, bottom: 12.7, left: 12.7, right: 12.7 },
  } = pages[currentPage]?.style || {};
  const { gutter = 50, columns = 2 } = pages[currentPage]?.layout || {};

  const [paperSize, setPaperSize] = useState("Letter");
  const [orientation, setOrientation] = useState("Portrait");
  const [linked, setLinked] = useState(true);
  const [bgColor, setBgColor] = useState(backgroundColor);
  const [bgImage, setBgImage] = useState(backgroundImage);
  const [currentMargins, setCurrentMargins] = useState(margins);
  const [currentGutter, setCurrentGutter] = useState(gutter);
  const [currentColumns, setCurrentColumns] = useState(columns);
  console.log(bgImage, "new data");
  const sizes = {
    Letter: { width: 215.9, height: 279.4 },
    A4: { width: 210, height: 297 },
    A5: { width: 148, height: 210 },
  };

  const currentSize =
    orientation === "Portrait"
      ? sizes[paperSize]
      : { width: sizes[paperSize].height, height: sizes[paperSize].width };

  const handleMarginChange = (e, side) => {
    const { value } = e.target;
    const updatedValue = parseFloat(value) || 0;

    const updatedMargins = { ...currentMargins, [side]: updatedValue };

    if (linked) {
      setCurrentMargins({
        top: updatedValue,
        bottom: updatedValue,
        left: updatedValue,
        right: updatedValue,
      });
      dispatch(
        updatePageStyle({
          id,
          style: { margins: updatedMargins },
        })
      );
    } else {
      setCurrentMargins(updatedMargins);
      dispatch(
        updatePageStyle({
          id,
          style: { margins: updatedMargins },
        })
      );
    }
  };

  const handleBackgroundColorChange = (e) => {
    const color = e.target.value;
    setBgColor(color);
    dispatch(
      updatePageStyle({
        id,
        style: { backgroundColor: color },
      })
    );
  };

  // const handleBackgroundImageChange = (e) => {
  //   const file = e.target.files[0];
  //   if (file) {
  //     const fileUrl = URL.createObjectURL(file);
  //     setBgImage(fileUrl);
  //     dispatch(updatePageStyle({
  //       id,
  //       style: { backgroundImage: `url(${fileUrl})` }
  //     }));
  //   }
  // };

  // const removeBackgroundImage = () => {
  //   setBgImage("");
  //   dispatch(updatePageStyle({
  //     id,
  //     style: { backgroundImage: "" }
  //   }));
  // };

  // Send the image to an API endpoint
  // Assuming the API returns the URL of the uploaded image
  // Update Redux store with the new image URL
  // Call the API to remove the background image
  // // Clear the background image locally

  // const handleBackgroundImageChange = async (e) => {
  //   const file = e.target.files[0];
  //   if (file) {
  //     const fileUrl = URL.createObjectURL(file);
  //     setBgImage(fileUrl);

  //     const formData = new FormData();
  //     formData.append("file", file);
  //     formData.append("type", 'image');

  //     try {
  //       api.post('/admin/attachments', formData)

  //       const imageUrl = response.data.imageUrl;

  //       dispatch(updatePageStyle({
  //         id,
  //         style: { backgroundImage: `url(${imageUrl})` }
  //       }));

  //       // Show success message
  //       alert("Background image added successfully!");
  //     } catch (error) {
  //       console.error("Error uploading image", error);
  //       // Show error message
  //       alert("Error uploading image. Please try again.");
  //     }
  //   }
  // };

  // const handleBackgroundImageChange = async (e) => {
  //   const file = e.target.files[0];
  //   if (file) {
  //     // Create a local URL for the image before uploading
  //     const fileUrl = URL.createObjectURL(file);
  //     setBgImage(fileUrl); // Set the local image URL as a preview

  //     const formData = new FormData();
  //     formData.append("file", file);
  //     formData.append("type", 'image');

  //     try {
  //       // Await the API call to ensure you get the response
  //       const response = await api.post('/admin/attachments', formData);

  //       // Check if the response contains the expected image URL
  //       const imageUrl = response.data.imageUrl;

  //       // Dispatch an action to update the background image in the page style
  //       dispatch(updatePageStyle({
  //         id,
  //         style: { backgroundImage: `url(${imageUrl})` }
  //       }));

  //       // Show success message
  //       alert("Background image added successfully!");
  //     } catch (error) {
  //       console.error("Error uploading image", error);
  //       // Show error message
  //       alert("Error uploading image. Please try again.");
  //     }
  //   }
  // };

  const [attachmentId, setAttachmentId] = useState(null);

  const handleBackgroundImageChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("type", "image");

      try {
        const response = await api.post("/admin/attachments", formData);

        // Extract relevant fields
        const imageData = response.data.data || {};
        const imageUrl = imageData.full_url;
        const attachmentId = imageData.id; // Now we know 'id' represents attachment_id

        console.log("Attachment ID:", attachmentId);
        console.log("Full URL:", imageUrl);

        // Set the attachmentId in state
        setAttachmentId(attachmentId);
        setBgImage(imageUrl);

        // Update the page style with the new background image
        dispatch(
          updatePageStyle({
            id,
            style: { backgroundImage: `url(${imageUrl})` },
          })
        );
      } catch (error) {
        console.error("Error uploading image", error);
        alert("Error uploading image. Please try again.");
      }
    }
  };

  const removeBackgroundImage = async () => {
    try {
      // Get current page style
      const currentStyle = pages[currentPage]?.style || {};

      // Check if we have a background image to remove
      if (
        !currentStyle.backgroundImage ||
        currentStyle.backgroundImage === "none"
      ) {
        console.log("No background image to remove");
        return;
      }

      // Extract attachment ID from the URL if not in state
      const attachmentIdToDelete =
        attachmentId ||
        currentStyle.backgroundImage.match(/attachments\/([^/]+)/)?.[1];

      if (attachmentIdToDelete) {
        await api.delete(`/admin/attachments/${attachmentIdToDelete}`);
      }

      // Update the page style to remove background image
      const updatedStyle = {
        ...currentStyle,
        backgroundImage: "none",
        backgroundPosition: currentStyle.backgroundPosition || "center", // Keep position setting
      };

      dispatch(
        updatePageStyle({
          id,
          style: updatedStyle,
        })
      );

      // Reset local states
      setBgImage(null);
      setAttachmentId(null);
    } catch (error) {
      console.error("Error removing background image:", error);
      // Fallback - just remove the image from UI if API fails
      dispatch(
        updatePageStyle({
          id,
          style: {
            ...pages[currentPage]?.style,
            backgroundImage: "none",
          },
        })
      );
      setBgImage(null);
      setAttachmentId(null);
    }
  };

  const toggleLink = () => setLinked(!linked);

  const handleGutterChange = (e) => {
    const value = parseInt(e.target.value, 10) || 0;
    setCurrentGutter(value);
    dispatch(
      updatePageLayout({
        id,
        layout: { gutter: value },
      })
    );
  };

  const handleColumnsChange = (e) => {
    const value = parseInt(e.target.value, 10) || 1;
    setCurrentColumns(value);
    dispatch(
      updatePageLayout({
        id,
        layout: { columns: value },
      })
    );
  };

  const toggleGuideVisibility = () => dispatch(toggleGuide());

  useEffect(() => {
    setBgColor(backgroundColor); // Sync initial background color from Redux
    setBgImage(backgroundImage); // Sync initial background image from Redux
    setCurrentGutter(gutter); // Sync initial gutter from Redux
    setCurrentColumns(columns); // Sync initial columns from Redux
  }, [backgroundColor, backgroundImage, gutter, columns]);

  return (
    <div className="flex">
      <div>
        <div className="space-y-4 mb-6">
          {/* <div className="flex flex-col">
            <label className="mb-1 dark:text-white">Paper Size:</label>
            <div className="flex gap-2">
              <select
                value={paperSize}
                onChange={(e) => setPaperSize(e.target.value)}
                className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {Object.keys(sizes).map((size) => (
                  <option key={size} value={size}>
                    {size}
                  </option>
                ))}
              </select>
              <button
                onClick={() => setOrientation("Portrait")}
                className={`flex items-center px-3 py-1 text-sm rounded ${orientation === "Portrait" ? "bg-blue-500 dark:text-white" : "bg-gray-200 text-gray-800"} hover:bg-blue-600 focus:outline-none`}
              >
                <Icon icon="cil:mobile-landscape" className="w-4 h-4" />
              </button>
              <button
                onClick={() => setOrientation("Landscape")}
                className={`flex items-center px-3 py-1 text-sm rounded ${orientation === "Landscape" ? "bg-blue-500 dark:text-white" : "bg-gray-200 text-gray-800"} hover:bg-blue-600 focus:outline-none`}
              >
                <Icon icon="cil:mobile-landscape" className="w-4 h-4" style={{ transform: "rotate(90deg)" }} />
              </button>
            </div>
          </div> */}
        </div>

        {/* Margin Inputs */}
        <div className="flex flex-col">
          <label className="mb-1 dark:text-white">Margin:</label>
          <div className="flex items-center justify-between mb-4">
            <div className="grid grid-cols-4 gap-4 w-full">
              <div className="flex flex-col">
                <label className="mb-1 dark:text-white">Top:</label>
                <input
                  type="number"
                  value={currentMargins.top}
                  onChange={(e) => handleMarginChange(e, "top")}
                  className="w-full px-1 py-0.5 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="flex flex-col">
                <label className="mb-1 dark:text-white">Left:</label>
                <input
                  type="number"
                  value={currentMargins.left}
                  onChange={(e) => handleMarginChange(e, "left")}
                  className="w-full px-1 py-0.5 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="flex flex-col">
                <label className="mb-1 dark:text-white">Bottom:</label>
                <input
                  type="number"
                  value={currentMargins.bottom}
                  onChange={(e) => handleMarginChange(e, "bottom")}
                  className="w-full px-1 py-0.5 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="flex flex-col">
                <label className="mb-1 dark:text-white">Right:</label>
                <input
                  type="number"
                  value={currentMargins.right}
                  onChange={(e) => handleMarginChange(e, "right")}
                  className="w-full px-1 py-0.5 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            <div className="ml-4 mt-7">
              <button
                onClick={toggleLink}
                className={`p-1 rounded-full border ${
                  linked ? "text-blue-500" : "text-gray-800"
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              >
                {linked ? (
                  <Link2 className="w-4 h-4" />
                ) : (
                  <Unlink className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Gutter Input */}
        <div className="flex flex-col mb-4">
          <label className="mb-1 dark:text-white">Gutter:</label>
          <input
            type="number"
            value={currentGutter}
            onChange={handleGutterChange}
            className="w-full px-1 py-0.5 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Columns Input */}
        <div className="flex flex-col mb-4">
          <label className="mb-1 dark:text-white">Columns:</label>
          <input
            type="number"
            value={currentColumns}
            onChange={handleColumnsChange}
            className="w-full px-1 py-0.5 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Background Color Input */}
        <div className="grid grid-cols-2 gap-4 mb-4 p-1 items-center">
          <label className="mb-1 dark:text-white">Background Color:</label>
          <input
            type="color"
            value={bgColor}
            onChange={handleBackgroundColorChange}
            className="w-full h-8 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Background Image Input */}
        <div className="flex flex-col mb-4">
          <label className="mb-1 dark:text-white">Background Image:</label>
          <div className="flex items-center gap-2">
            <input
              type="file"
              accept="image/*"
              onChange={handleBackgroundImageChange}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {bgImage && (
              <div className="flex gap-2">
                <button
                  onClick={removeBackgroundImage}
                  className="p-2 text-sm dark:text-white bg-red-500 rounded hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500"
                >
                  <Icon icon="fluent:delete-20-regular" className="w-4 h-4" />
                </button>
                <select
                  onChange={(e) => {
                    dispatch(
                      updatePageStyle({
                        id,
                        style: {
                          ...pages[currentPage]?.style,
                          backgroundPosition: e.target.value,
                        },
                      })
                    );
                  }}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={
                    pages[currentPage]?.style?.backgroundPosition || "center"
                  }
                >
                  <option value="center">Center</option>
                  <option value="top">Top</option>
                  <option value="bottom">Bottom</option>
                  <option value="left">Left</option>
                  <option value="right">Right</option>
                  <option value="top left">Top Left</option>
                  <option value="top right">Top Right</option>
                  <option value="bottom left">Bottom Left</option>
                  <option value="bottom right">Bottom Right</option>
                </select>
              </div>
            )}
          </div>
        </div>

        {/* Guide Toggle */}
        <div className="flex flex-col mb-4">
          <label className="mb-1 dark:text-white">Guide:</label>
          <button
            onClick={toggleGuideVisibility}
            className={`p-2 text-sm ${
              showGuide
                ? "bg-blue-500 dark:bg-blue-600 text-white dark:text-white"
                : "bg-gray-200 dark:bg-gray-700 dark:text-gray-300"
            } dark:hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600`}
          >
            {showGuide ? "Hide Guide" : "Show Guide"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PageProperties;
