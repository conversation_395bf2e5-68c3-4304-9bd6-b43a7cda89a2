// store.js

import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer, createTransform } from 'redux-persist';
import storage from 'redux-persist/lib/storage'; // defaults to localStorage for web
import pagesReducer from '../pages/editor/store/pagesSlice';
import toolsReducer from '../pages/editor/store/toolbarSlice';
import authReducer from './authSlice';
import ebookReducer from '../pages/book-preview/store/ebookSlice';
import scaleReducer from '../pages/book-preview/store/scaleSlice';
import drawingReducer from '../pages/book-preview/store/drawingSlice';

// Transform to persist only the 'present' state from redux-undo
const undoableTransform = createTransform(
  // Transform state on its way to being serialized and persisted.
  (inboundState, key) => {
    // Persist only the 'present' part of the state
    return { present: inboundState.present };
  },
  // Transform state being rehydrated
  (outboundState, key) => {
    // Rehydrate the entire state by setting 'past' and 'future' to empty arrays
    return {
      past: [],
      present: outboundState.present,
      future: [], 
    };
  },
  // Configuration options
  { whitelist: ['pages'] }
);

// Persist configuration for the 'pages' reducer
const pagesPersistConfig = {
  key: 'pages',
  storage,
  transforms: [undoableTransform],
};

// Combine all reducers
const rootReducer = combineReducers({
  pages: pagesReducer,
  toolbar: toolsReducer,
  auth: authReducer, // Use non-persisted auth reducer (now using cookies)
  ebook: ebookReducer,
  scale: scaleReducer,
  drawing: drawingReducer, // Do not persist the drawing reducer
});

// Persist configuration for the root reducer (excluding 'pages', 'drawing', and 'auth')
const rootPersistConfig = {
  key: 'root',
  storage,
  blacklist: ['pages', 'drawing', 'auth'], // Exclude these from root persistence
};

// Wrap the root reducer with `persistReducer` if you want to persist other reducers
const finalRootReducer = persistReducer(rootPersistConfig, rootReducer);

// Configure the store
const store = configureStore({
  reducer: finalRootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore these action types in serializable checks
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
  // Optional: Add enhancers like Redux DevTools here if needed
});

// Create the persistor
export const persistor = persistStore(store);

// Export the store
export default store;
