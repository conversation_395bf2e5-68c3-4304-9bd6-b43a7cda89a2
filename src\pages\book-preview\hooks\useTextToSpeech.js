import { useRef, useEffect } from 'react';
import { setCurrentReadingElementId } from '../store/ebookSlice';

const useTextToSpeech = ({ pitch, rate, volume, voice, dispatch }) => {
  // Initialize with null and set in useEffect to avoid errors in environments without speechSynthesis
  const speechSynthesisRef = useRef(null);

  // Safely initialize speech synthesis
  useEffect(() => {
    try {
      if (typeof window !== 'undefined' && window.speechSynthesis) {
        speechSynthesisRef.current = window.speechSynthesis;
      }
    } catch (error) {
      console.log('Speech synthesis not available in this environment');
    }
  }, []);

  const createUtterance = (text, callback) => {
    try {
      if (!speechSynthesisRef.current) {
        // If speech synthesis is not available, just call the callback
        if (callback) setTimeout(callback, 500);
        return null;
      }

      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = rate || 1;
      utterance.pitch = pitch || 1;
      utterance.volume = volume || 1;

      try {
        const voices = speechSynthesisRef.current.getVoices() || [];
        const selectedVoice = voice ? voices.find(v => v.name === voice) : null;
        if (selectedVoice) utterance.voice = selectedVoice;
      } catch (error) {
        console.log('Error getting voices:', error);
      }

      utterance.onend = () => callback && callback();
      return utterance;
    } catch (error) {
      console.log('Error creating utterance:', error);
      if (callback) setTimeout(callback, 500);
      return null;
    }
  };

  const readElement = (element, callback) => {
    try {
      if (!element || !speechSynthesisRef.current) {
        if (callback) callback();
        return;
      }

      let textToSpeak = '';
      try {
        switch (element.type) {
          case 'text':
            try {
              const tempDiv = document.createElement('div');
              tempDiv.innerHTML = element.content || '';
              textToSpeak = tempDiv.innerText;
            } catch (error) {
              console.log('Error processing text element:', error);
              textToSpeak = '';
            }
            break;
          case 'mcq':
            try {
              textToSpeak = `Question: ${element.content?.question || ''}. Options: ${element.content?.options?.join(', ') || ''}`;
            } catch (error) {
              console.log('Error processing mcq element:', error);
              textToSpeak = '';
            }
            break;
          case 'true-false':
            try {
              textToSpeak = `Question: ${element.content?.question || ''}`;
            } catch (error) {
              console.log('Error processing true-false element:', error);
              textToSpeak = '';
            }
            break;
          case 'video':
            textToSpeak = 'This is a video element.';
            break;
          case 'flashcard':
            textToSpeak = 'This is a flashcard element.';
            break;
          case 'shape':
            textToSpeak = 'This is a shape element.';
            break;
          default:
            textToSpeak = '';
        }
      } catch (error) {
        console.log('Error determining text to speak:', error);
        textToSpeak = '';
      }

      if (textToSpeak) {
        try {
          dispatch(setCurrentReadingElementId(element.id));
          const utterance = createUtterance(textToSpeak, callback);
          if (utterance && speechSynthesisRef.current) {
            speechSynthesisRef.current.speak(utterance);
          } else if (callback) {
            callback();
          }
        } catch (error) {
          console.log('Error speaking text:', error);
          if (callback) callback();
        }
      } else if (callback) {
        callback();
      }
    } catch (error) {
      console.log('Error in readElement:', error);
      if (callback) callback();
    }
  };

  const readPageElements = (data, pageIndex, callback) => {
    try {
      if (!data || !data.pages || !speechSynthesisRef.current) {
        if (callback) callback();
        return;
      }

      const elements = data.pages[pageIndex]?.elements || [];
      let idx = 0;

      const readNext = () => {
        try {
          if (idx >= elements.length) {
            dispatch(setCurrentReadingElementId(null));
            if (callback) callback();
            return;
          }
          readElement(elements[idx], readNext);
          idx += 1;
        } catch (error) {
          console.log('Error in readNext:', error);
          idx += 1;
          // Continue to next element even if there's an error
          setTimeout(readNext, 100);
        }
      };

      readNext();
    } catch (error) {
      console.log('Error in readPageElements:', error);
      if (callback) callback();
    }
  };

  // Return a safe version of speechSynthesis that checks for availability
  const safeSpeechSynthesis = {
    speak: (utterance) => {
      try {
        if (speechSynthesisRef.current && utterance) {
          speechSynthesisRef.current.speak(utterance);
        }
      } catch (error) {
        console.log('Error in speak:', error);
      }
    },
    cancel: () => {
      try {
        if (speechSynthesisRef.current) {
          speechSynthesisRef.current.cancel();
        }
      } catch (error) {
        console.log('Error in cancel:', error);
      }
    },
    pause: () => {
      try {
        if (speechSynthesisRef.current) {
          speechSynthesisRef.current.pause();
        }
      } catch (error) {
        console.log('Error in pause:', error);
      }
    },
    resume: () => {
      try {
        if (speechSynthesisRef.current) {
          speechSynthesisRef.current.resume();
        }
      } catch (error) {
        console.log('Error in resume:', error);
      }
    },
    getVoices: () => {
      try {
        return speechSynthesisRef.current ? speechSynthesisRef.current.getVoices() : [];
      } catch (error) {
        console.log('Error in getVoices:', error);
        return [];
      }
    }
  };

  return { speechSynthesisRef: safeSpeechSynthesis, readPageElements };
};

export default useTextToSpeech;
