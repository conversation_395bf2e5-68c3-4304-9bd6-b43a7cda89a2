import React, { useEffect, useRef, useState } from 'react';
import HTM<PERSON>lipBook from 'react-pageflip';
import HtmlContent from './HtmlContent';
import { useSelector, useDispatch } from 'react-redux';
import { setCurrentReadingElementId } from '../store/ebookSlice';
import { Icon } from '@iconify/react';

const FlipBookContainer = ({ data, setClickedPageId }) => {
  const dispatch = useDispatch();
  const speechSynthesisRef = useRef(window.speechSynthesis);
  const flipBookRef = useRef(null);

  const [currentPage, setCurrentPage] = useState(0);

  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const [scale, setScale] = useState(1);

  const outerPadding = 10;

  useEffect(() => {
    const calculateAndSetScale = () => {
      const pageW = data?.width || 800;
      const pageH = data?.height || 1000;
      if (!pageW || !pageH) return;
      
      const totalHorizontalMargin = 40 + (2 * outerPadding);
      const totalVerticalMargin = 20 + (2 * outerPadding);

      const HEADER_HEIGHT = 60;
      const FOOTER_HEIGHT = 0;

      const availableWidth = window.innerWidth - totalHorizontalMargin;
      const availableHeight = window.innerHeight - HEADER_HEIGHT - FOOTER_HEIGHT - totalVerticalMargin;

      const contentWidth = isMobile ? pageW : pageW * 2;
      const contentHeight = pageH;

      if (contentWidth <= 0 || contentHeight <= 0 || availableWidth <= 0 || availableHeight <= 0) {
        setScale(1);
        return;
      }

      const widthScale = availableWidth / contentWidth;
      const heightScale = availableHeight / contentHeight;

      const newScale = Math.min(widthScale, heightScale);
      const maxScale = 2.5;
      const finalScale = Math.min(newScale, maxScale);

      setScale(finalScale);
    };

    calculateAndSetScale();

    window.addEventListener('resize', calculateAndSetScale);

    return () => {
      window.removeEventListener('resize', calculateAndSetScale);
    };
  }, [data?.width, data?.height, isMobile, outerPadding]);

  const {
    pitch,
    rate,
    volume,
    voice,
    isTextToSpeechEnabled,
    noteTakingLayer,
  } = useSelector((state) => ({
    ...state.ebook.textToSpeechSettings,
    isTextToSpeechEnabled: state.ebook.isTextToSpeechEnabled,
    noteTakingLayer: state.ebook.noteTakingLayer,
  }));

  const createUtterance = (text, callback) => {
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = rate;
    utterance.pitch = pitch;
    utterance.volume = volume;

    const availableVoices = speechSynthesisRef.current.getVoices();
    const selectedVoice = availableVoices.find((v) => v.name === voice);
    if (selectedVoice) {
      utterance.voice = selectedVoice;
    }

    utterance.onend = () => {
      if (callback) callback();
    };
    return utterance;
  };

  const readElement = (element, callback) => {
    let textToSpeak = '';
    switch (element.type) {
      case 'text': {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = element.content;
        textToSpeak = tempDiv.innerText;
        break;
      }
      case 'mcq':
        textToSpeak = `Question: ${element.content.question}. Options: ${element.content.options.join(', ')}`;
        break;
      case 'true-false':
        textToSpeak = `Question: ${element.content.question}`;
        break;
      case 'video':
        textToSpeak = 'This is a video element.';
        break;
      case 'flashcard':
        textToSpeak = 'This is a flashcard element.';
        break;
      case 'shape':
        textToSpeak = 'This is a shape element.';
        break;
      default:
        textToSpeak = '';
    }

    if (textToSpeak) {
      dispatch(setCurrentReadingElementId(element.id));
      const utterance = createUtterance(textToSpeak, callback);
      speechSynthesisRef.current.speak(utterance);
    } else if (callback) {
      callback();
    }
  };

  const readAllElementsOnPage = (pageIndex, onDone) => {
    if (!data?.pages[pageIndex]) {
      onDone?.();
      return;
    }
    const elements = data.pages[pageIndex].elements || [];
    const readNextElement = (idx) => {
      if (idx >= elements.length) {
        onDone?.();
        return;
      }
      readElement(elements[idx], () => readNextElement(idx + 1));
    };
    readNextElement(0);
  };

  const readSinglePage = (pageIndex) => {
    if (noteTakingLayer || pageIndex >= data.pages.length) {
      dispatch(setCurrentReadingElementId(null));
      return;
    }
    speechSynthesisRef.current.cancel();
    readAllElementsOnPage(pageIndex, () => {
      setTimeout(() => {
        if (!noteTakingLayer && !speechSynthesisRef.current.speaking) {
          handleNext();
          setTimeout(() => {
            readSinglePage(pageIndex + 1);
          }, 800);
        }
      }, 500);
    });
  };

  const readDoublePage = (leftPageIndex) => {
    if (noteTakingLayer || leftPageIndex >= data.pages.length) {
      dispatch(setCurrentReadingElementId(null));
      return;
    }
    const rightPageIndex = leftPageIndex + 1;
    speechSynthesisRef.current.cancel();
    readAllElementsOnPage(leftPageIndex, () => {
      if (rightPageIndex < data.pages.length) {
        readAllElementsOnPage(rightPageIndex, () => {
          setTimeout(() => {
            if (!noteTakingLayer && !speechSynthesisRef.current.speaking) {
              handleNext();
              setTimeout(() => {
                readDoublePage(leftPageIndex + 2);
              }, 800);
            }
          }, 500);
        });
      } else {
        setTimeout(() => {
          if (!noteTakingLayer && !speechSynthesisRef.current.speaking) {
            handleNext();
          }
        }, 500);
      }
    });
  };

  const autoReadPages = () => {
    if (noteTakingLayer || !data.pages || data.pages.length === 0) return;
    if (isMobile) {
      readSinglePage(currentPage);
    } else {
      readDoublePage(currentPage);
    }
  };

  useEffect(() => {
    if (noteTakingLayer) {
      speechSynthesisRef.current.cancel();
      dispatch(setCurrentReadingElementId(null));
    } else if (isTextToSpeechEnabled) {
      autoReadPages();
    } else {
      speechSynthesisRef.current.cancel();
      dispatch(setCurrentReadingElementId(null));
    }
    return () => speechSynthesisRef.current.cancel();
  }, [isTextToSpeechEnabled, noteTakingLayer, currentPage, voice]);

  const handlePrevious = () => {
    if (noteTakingLayer) return;
    if (flipBookRef.current) {
      flipBookRef.current.pageFlip().flipPrev();
    }
  };

  const handleNext = () => {
    if (noteTakingLayer) return;
    if (flipBookRef.current) {
      setTimeout(() => {
        if (!noteTakingLayer) {
          flipBookRef.current.pageFlip().flipNext();
        }
      }, 300);
    }
  };

  const onPageChange = (e) => {
    if (!noteTakingLayer) {
      setCurrentPage(e.data);
    } else {
      const currentBookIndex = flipBookRef.current?.pageFlip().getCurrentPageIndex();
      if (currentBookIndex !== undefined && currentBookIndex !== currentPage) {
        flipBookRef.current.pageFlip().flip(currentPage, 'bottom');
      }
    }
  };

  const handleJumpToPage = (pageNumber) => {
    if (noteTakingLayer) return;
    if (flipBookRef.current) {
      const zeroBasedIndex = pageNumber - 1;
      if (zeroBasedIndex >= 0 && zeroBasedIndex < (data?.pages?.length || 0)) {
        flipBookRef.current.pageFlip().flip(zeroBasedIndex);
      }
    }
  };

  const isPrevDisabled = noteTakingLayer || currentPage === 0;
  const pageCount = data?.pages?.length || 0;
  const lastPageIndex = pageCount > 0
    ? isMobile
      ? pageCount - 1
      : pageCount % 2 === 0
        ? pageCount - 2
        : pageCount - 1
    : 0;
  const isNextDisabled = noteTakingLayer || currentPage >= lastPageIndex;

  const flipbookWidth = data?.width || 800;
  const flipbookHeight = data?.height || 1000;
  const unscaledContentWidth = isMobile ? flipbookWidth : flipbookWidth * 2;
  const unscaledContentHeight = flipbookHeight;

  if (!data?.pages) {
    return <div className="loading-placeholder">Loading Ebook Data...</div>;
  }

  return (
    <div className="relative w-full flex justify-center">
      <div
        style={{
          transform: `scale(${scale})`,
          transformOrigin: 'top center',
          width: `${unscaledContentWidth + 2 * outerPadding}px`,
          height: `${unscaledContentHeight + 2 * outerPadding}px`,
          padding: `${outerPadding}px`,
          marginTop: isMobile ? '100px' : `-${outerPadding}px`,
          position: 'relative',
          zIndex: 1,
          transition: 'transform 0.3s ease-out',
          paddingBottom: `${outerPadding + 100}px`
        }}
        className="mx-auto"
      >
        <div
          style={{
            width: `${unscaledContentWidth}px`,
            height: `${unscaledContentHeight}px`,
            pointerEvents: noteTakingLayer ? 'none' : 'auto',
          }}
          className="shadow-lg"
        >
          <HTMLFlipBook
            ref={flipBookRef}
            width={flipbookWidth}
            height={flipbookHeight}
            size="fixed"
            startPage={currentPage}
            className="flipbook"
            disableFlipByClick={isMobile ? true : true}
            useMouseEvents={isMobile ? true : false}
            mobileScrollSupport={!noteTakingLayer}
            showPageCorners={!noteTakingLayer}
            clickEventForward={false}
            usePortrait={isMobile}
            onFlip={onPageChange}
            key={`flipbook-${isMobile}-${noteTakingLayer}`}
          >
            {data?.pages?.map((page, index) => (
              <div
                key={page.id || index}
                className={`page-wrapper bg-white shadow-sm border border-gray-100 overflow-hidden ${
                  index % 2 === 0 ? 'left-page' : 'right-page'
                }`}
                style={{ width: flipbookWidth, height: flipbookHeight }}
                onClick={(e) => {
                  if (noteTakingLayer) {
                    e.stopPropagation();
                    return;
                  }
                  if (setClickedPageId) {
                    setClickedPageId(page.id);
                  }
                }}
              >
                <HtmlContent
                  page={page}
                  index={index}
                  html={page?.content || ''}
                  bg_color={page?.bg_color || 'white'}
                  margin={page?.margin || '0'}
                  width={flipbookWidth}
                  height={flipbookHeight}
                  bookmarks={data?.bookmarks}
                  isMobile={isMobile}
                />
              </div>
            ))}
          </HTMLFlipBook>
        </div>
      </div>

      <div className="md:block hidden">
        <div className="fixed top-1/2 left-3 transform -translate-y-1/2 z-20">
          <button
            aria-label="Previous Page"
            className={`p-3 rounded-full flex items-center justify-center transition-all duration-300 shadow-md ${
              isPrevDisabled
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed opacity-70'
                : 'bg-gray-800 text-white hover:bg-gray-900'
            }`}
            onClick={handlePrevious}
            disabled={isPrevDisabled}
          >
            <Icon icon="mdi:chevron-left" width="26" height="26" />
          </button>
        </div>
        <div className="fixed top-1/2 right-3 transform -translate-y-1/2 z-20">
          <button
            aria-label="Next Page"
            className={`p-3 rounded-full flex items-center justify-center transition-all duration-300 shadow-md ${
              isNextDisabled
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed opacity-70'
                : 'bg-gray-800 text-white hover:bg-gray-900'
            }`}
            onClick={handleNext}
            disabled={isNextDisabled}
          >
            <Icon icon="mdi:chevron-right" width="26" height="26" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default FlipBookContainer;
