import React from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useTranslation } from "react-i18next";
import { TextField } from "@/components/inputs/TextField";
import InputFile from "@/components/ui/InputFile";

const PublishersForm = ({ initialValues, onSubmit, onClose, isEditMode = false }) => {
    const { t } = useTranslation();

    const validationSchema = !isEditMode
        ? Yup.object().shape({
            name: Yup.string()
                .required(t("publishers.form.nameRequired")),
            email: Yup.string()
                .email(t("publishers.form.invalidEmail"))
                .required(t("publishers.form.emailRequired")),
            password: Yup.string()
                .min(8, t("publishers.form.passwordMin"))
                .required(t("publishers.form.passwordRequired")),
            confirmPassword: Yup.string()
                .oneOf([Yup.ref("password"), null], t("publishers.form.passwordMismatch"))
                .required(t("publishers.form.confirmPasswordRequired")),
        })
        : null;

    return (
        <Formik
            initialValues={{ ...initialValues, password: "", confirmPassword: "" }}
            validationSchema={validationSchema}
            onSubmit={(values) => {
                const { confirmPassword, ...submittedValues } = values;
                onSubmit(submittedValues);
            }}
        >
            {({ values, setFieldValue, isSubmitting }) => (
                <Form className="space-y-6" noValidate autoComplete="off">
                    <div className="grid grid-cols-3 gap-6">
                        <div className="space-y-4">
                            <TextField label={t("publishers.form.name")} name="name" type="text" required />
                            <TextField label={t("publishers.form.mobile")} name="mobile" type="text" />
                            <TextField label={t("publishers.form.nid")} name="nid" type="text" />
                            <InputFile
                                name="nid_image"
                                title={t("publishers.form.nidImage")}
                                accept="image/*"
                                placeholder={t("publishers.form.nidImage")}
                                onChange={(event) => setFieldValue("nid_image", event.currentTarget.files)}
                            />
                            <InputFile
                                name="proprietor_photo"
                                title={t("publishers.form.proprietorPhoto")}
                                accept="image/*"
                                placeholder={t("publishers.form.proprietorPhoto")}
                                onChange={(event) => setFieldValue("proprietor_photo", event.currentTarget.files)}
                            />
                            <InputFile
                                name="proprietor_signature"
                                title={t("publishers.form.proprietorSignature")}
                                accept="image/*"
                                placeholder={t("publishers.form.proprietorSignature")}
                                onChange={(event) => setFieldValue("proprietor_signature", event.currentTarget.files)}
                            />
                        </div>

                        <div className="space-y-4">
                            <TextField label={t("publishers.form.presentAddress")} name="present_address" type="text" />
                            <TextField label={t("publishers.form.permanentAddress")} name="permanent_address" type="text" />
                            <TextField label={t("publishers.form.officePresentAddress")} name="office_present_address" type="text" />
                            <TextField label={t("publishers.form.tradeLicenseNumber")} name="trade_license_number" type="text" />
                            <InputFile
                                name="trade_license_image"
                                title={t("publishers.form.tradeLicenseImage")}
                                accept="image/*"
                                placeholder={t("publishers.form.tradeLicenseImage")}
                                onChange={(event) => setFieldValue("trade_license_image", event.currentTarget.files)}
                            />
                            <InputFile
                                name="logo"
                                title={t("publishers.form.logo")}
                                accept="image/*"
                                placeholder={t("publishers.form.logo")}
                                onChange={(event) => setFieldValue("logo", event.currentTarget.files)}
                            />
                        </div>

                        <div className="space-y-4">
                            <TextField label={t("publishers.form.etin")} name="etin" type="text" />
                            <TextField label={t("publishers.form.vatNumber")} name="vat_number" type="text" />
                            <TextField label={t("publishers.form.officeMobile")} name="office_mobile" type="text" />
                            <TextField label={t("publishers.form.officePhone")} name="office_phone" type="text" />
                            <TextField label={t("publishers.form.officeEmail")} name="office_email" type="email" />
                            <TextField label={t("publishers.form.officePermanentAddress")} name="office_permanent_address" type="text" />
                        </div>
                    </div>
                    <div className="border-t border-gray-300 my-6"></div>
                    <div>
                        <h3 className="text-lg font-semibold">{t("publishers.form.publisherLogin")}</h3>
                        <div className="grid grid-cols-3 gap-6 mt-4">
                            <TextField label={t("publishers.form.email")} name="email" type="email" required={true} />
                            <TextField label={t("publishers.form.password")} name="password" type="password" required={true} />
                            <TextField label={t("publishers.form.confirmPassword")} name="confirmPassword" type="password" required={true} />
                        </div>
                    </div>

                    <div className="flex justify-end gap-2 pt-4">
                        <button
                            className="bg-gray-300 hover:bg-gray-600 rounded py-2 px-4"
                            onClick={() => {
                                if (onClose) {
                                    onClose();
                                }
                            }}
                            type="button"
                        >
                            {t("publishers.form.cancel")}
                        </button>
                        <button type="submit" className="bg-blue-500 text-white py-2 px-4 rounded">
                            {isEditMode ? t("publishers.form.edit") : t("publishers.form.add")}
                        </button>
                    </div>
                </Form>
            )}
        </Formik>
    );
};

export default PublishersForm;
