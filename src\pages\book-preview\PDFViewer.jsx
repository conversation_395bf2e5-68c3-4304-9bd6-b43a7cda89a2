import { useState, useEffect, useRef } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";

pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js`;

const LazyPage = ({ pageNumber, containerWidth }) => {
  const ref = useRef(null);
  const [inView, setInView] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        setInView(true);
        observer.unobserve(ref.current);
      }
    });
    if (ref.current) observer.observe(ref.current);

    return () => {
      if (ref.current) observer.unobserve(ref.current);
    };
  }, []);

  // If containerWidth is 0, the Page won't render properly
  // So we ensure it's at least some fallback value (e.g. 600)
  const pageWidth = Math.max(containerWidth * 0.9, 600);

  return (
    <div ref={ref} className="mb-6 w-full flex justify-center">
      {inView && (
        <Page
          pageNumber={pageNumber}
          renderTextLayer={false}
          width={700}
          className="bg-white shadow-md rounded"
        />
      )}
    </div>
  );
};

const PDFViewer = ({ pdfUrl }) => {
  const [numPages, setNumPages] = useState(null);
  const containerRef = useRef(null);
  const [containerWidth, setContainerWidth] = useState(0);

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
      // Quick debug log
      console.log("Container Width:", containerRef.current.offsetWidth);
    }
  }, []);

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
    console.log("PDF loaded. Total pages:", numPages);
  };

  const onDocumentLoadError = (error) => {
    console.error("PDF Load Error:", error);
  };

  return (
    <div className="flex flex-col items-center min-h-screen bg-gray-500 py-4 px-2">
      <div
        ref={containerRef}
        className="w-full max-w-4xl p-0 flex flex-col rounded-md"
      >
        <Document
          file={pdfUrl}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={onDocumentLoadError}
        >
          {Array.from(new Array(numPages || 0), (_, index) => (
            <LazyPage
              key={index}
              pageNumber={index + 1}
              containerWidth={containerWidth}
            />
          ))}
        </Document>
      </div>
    </div>
  );
};

export default PDFViewer;
