import { useField } from 'formik';

export const Checkbox = ({ label, ...props }) => {
  const [field, meta] = useField({ ...props, type: 'checkbox' });
  
  return (
    <div className="mb-4">
      <div className="flex items-center">
        <input
          type="checkbox"
          {...field}
          {...props}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded
                   dark:border-gray-600 dark:bg-gray-700"
        />
        <label className="ml-2 text-sm text-gray-700 dark:text-gray-200">
          {label}
        </label>
      </div>
      {meta.touched && meta.error ? (
        <div className="text-red-600 dark:text-red-400 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};