// Cookie management for auth
import Cookies from 'js-cookie';
import { COOKIE_DOMAIN, COOKIE_OPTIONS } from '@/config';

// Cookie keys
const TOKEN_KEY = 'token';
const USER_KEY = 'user';

// Cookie options are now imported from centralized config

export const setToken = (token) => {
  if (token) {
    Cookies.set(TOKEN_KEY, token, COOKIE_OPTIONS);
  } else {
    Cookies.remove(TOKEN_KEY, {
      path: '/',
      ...(COOKIE_DOMAIN && { domain: COOKIE_DOMAIN })
    });
  }
};

export const getToken = () => Cookies.get(TOKEN_KEY);

export const setUser = (user) => {
  if (user) {
    Cookies.set(USER_KEY, JSON.stringify(user), COOKIE_OPTIONS);
  } else {
    Cookies.remove(USER_KEY, {
      path: '/',
      ...(COOKIE_DOMAIN && { domain: COOKIE_DOMAIN })
    });
  }
};

export const getUser = () => {
  const user = Cookies.get(USER_KEY);
  return user ? JSON.parse(user) : null;
};

export const removeToken = () => Cookies.remove(TOKEN_KEY, {
  path: '/',
  ...(COOKIE_DOMAIN && { domain: COOKIE_DOMAIN })
});

export const removeUser = () => Cookies.remove(USER_KEY, {
  path: '/',
  ...(COOKIE_DOMAIN && { domain: COOKIE_DOMAIN })
});

// Function to load auth from cookies to Redux
export const loadAuthFromCookies = () => {
  const token = getToken();
  const user = getUser();
  
  if (token && user) {
    return {
      isAuthenticated: true,
      token,
      user
    };
  }
  
  return {
    isAuthenticated: false,
    token: null,
    user: null
  };
};
