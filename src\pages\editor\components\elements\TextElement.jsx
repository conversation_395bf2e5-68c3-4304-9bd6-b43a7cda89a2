import React, { useRef } from "react"
import { Editor } from "@tinymce/tinymce-react"
import { useDispatch, useSelector } from "react-redux"
import { updateElement } from "../../store/pagesSlice"
import { EDITOR_CONFIG, HEADING_STYLES } from "./text-editor/constant"
import { setupToolbar } from "./text-editor/EditorToolbar"

const RichTextEditor = ({ element, readonly }) => {
  const currentReadingElementId = useSelector(state => state.ebook.currentReadingElementId)
  const dispatch = useDispatch()
  const editorRef = useRef(null)
  let splitButtonApi = null

  const updateHeight = editor => {
    if (readonly) return
    const body = editor.getBody()
    const newHeight = body ? body.scrollHeight : element.size.height
    if (newHeight !== element.size.height) {
      dispatch(
        updateElement({
          id: element.id,
          updates: { size: { ...element.size, height: newHeight } }
        })
      )
    }
  }

  const handleEditorChange = (content, editor) => {
    if (readonly) return
    updateHeight(editor)
    dispatch(
      updateElement({
        id: element.id,
        updates: { content }
      })
    )
  }

  const getEditorConfig = () => {
    const baseConfig = {
      ...EDITOR_CONFIG,
      inline: true,
      disabled: readonly,
      readonly: readonly, // Add readonly property for TinyMCE 7+
      editable_root: !readonly, // Additional readonly control for TinyMCE 7+
      placeholder: element.placeholder || "",
      content_style: `
        ${HEADING_STYLES}
        ${
          element.style
            ? Object.entries(element.style)
                .map(([key, value]) => key + ": " + value + ";")
                .join(" ")
            : ""
        }
        body {
          margin: 0;
          padding: 0;
          ${readonly ? "cursor: default !important; user-select: text !important;" : ""}
        }
        p, div {
          margin: 0;
          padding: 0;
          line-height: 1.2;
          ${readonly ? "cursor: default !important;" : ""}
        }
          a {
        color: #0066cc;
        text-decoration: underline;
        cursor: pointer !important;
        background-color: transparent !important;
      }
      a:hover {
        text-decoration: none;
      }
        ${readonly ? "* { caret-color: transparent !important; }" : ""}
      `,

      toolbar: readonly
        ? false
        : [
            "undo redo | blocks | customfontsize fontfamily | bold italic underline forecolor | alignleft aligncenter alignright alignjustify | lineheight paragraphspacing | bullist numlist  link unlink | verticaltext horizontaltext | code"
          ].join(" ")
    }
    if (readonly) {
      baseConfig.plugins = []
    }
    return baseConfig
  }

  return (
    <div
      className={currentReadingElementId === element.id ? "border border-blue-500" : ""}
      style={{
        height: element.size.height,
        width: element.size.width,
        ...element.style,
        position: "relative"
      }}
    >
      <Editor
        inline
        disabled={readonly}
        tinymceScriptSrc="/tinymce/tinymce.min.js"
        onInit={(evt, editor) => {
          editorRef.current = editor
          // No need to call setMode in TinyMCE 7+ - readonly is handled by config
          if (!readonly) {
            setupToolbar(editor, splitButtonApi)
          }
        }}
        value={element.content}
        init={getEditorConfig()}
        onEditorChange={handleEditorChange}
      />
    </div>
  )
}

export default RichTextEditor
