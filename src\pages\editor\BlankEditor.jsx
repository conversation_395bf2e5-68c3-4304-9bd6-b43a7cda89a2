import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { TextField, TextArea, Select, SelectWithQuery } from "@/components/inputs";
import { CreatableSelectWithQuery } from '@/components/inputs/CreatableSelectWithQuery';
import api from "@/lib/axios";
import Templates from "./TemplateGallery";
import { useTranslation } from "react-i18next";

const pageDimensions = [
  { value: "standard", label: "Standard (794 x 1123)", width: 794, height: 1123 },
  { value: "a4", label: "A4 (595 x 842)", width: 595, height: 842 },
  { value: "a5", label: "A5 (420 x 595)", width: 420, height: 595 },
  { value: "letter", label: "Letter (612 x 792)", width: 612, height: 792 },
  { value: "legal", label: "Legal (612 x 1008)", width: 612, height: 1008 },
  { value: "tabloid", label: "Tabloid (792 x 1224)", width: 792, height: 1224 },
  { value: "executive", label: "Executive (522 x 756)", width: 522, height: 756 },
  { value: "b5", label: "B5 (516 x 729)", width: 516, height: 729 },
  { value: "custom", label: "Custom Size", width: 0, height: 0 },
];

export default function BlankEditor() {
  const { stepId, bookId } = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const steps = [
    { id: 1, title: "Book Details" },
    { id: 2, title: "Choose Option" },
    { id: 3, title: "Select Template" },
  ];

  const validationSchema = Yup.object().shape({
    title: Yup.string().required("Title is required"),
    width: Yup.number().required("Width is required").positive("Width must be positive"),
    height: Yup.number().required("Height is required").positive("Height must be positive"),
    page_dimension: Yup.string().required("Page dimension is required"),
  });

  const initialValues = {
    title: "",
    description: "",
    is_public: 1,
    category_ids: [],
    author_ids: [],
    status: "published",
    book_type: "ebook",
    cover_image: "",
    page_dimension: "standard",
    width: 794,
    height: 1123,
    orientation: "portrait",
    pdf_file: "",
  };

  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      const formData = new FormData();
      Object.keys(values).forEach((key) => {
        if (key === 'author_ids') {
          values[key].forEach((id) => formData.append('author_ids[]', id));
        } else if (key === 'category_ids') {
          values[key].forEach((id) => formData.append('category_ids[]', id));
        } else {
          formData.append(key, values[key]);
        }
      });

      const response = await api.post("admin/ebooks", formData);
      const newBookId = response.data.data.id;
      navigate(`/editor/2/${newBookId}`);
    } catch (error) {
      console.error("Error creating book:", error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 mt-20">
      <div className="max-w-5xl mx-auto px-6">

        {/* Stepper */}
        <div className="mb-6">
          <div className="flex justify-start items-center gap-4 bg-white p-4 rounded-lg shadow-md">
            {steps.map((step) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={`w-8 h-8 flex items-center justify-center rounded-full text-xs font-bold transition ${
                    parseInt(stepId) >= step.id
                      ? "bg-blue-600 text-white"
                      : "bg-gray-300 text-gray-600"
                  }`}
                >
                  {step.id}
                </div>
                <span
                  className={`ml-3 text-sm font-medium transition ${
                    parseInt(stepId) >= step.id ? "text-blue-600" : "text-gray-500"
                  }`}
                >
                  {step.title}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          {parseInt(stepId) === 1 && (
            <Formik
              initialValues={initialValues}
              validationSchema={validationSchema}
              onSubmit={handleSubmit}
            >
              {({ values, setFieldValue }) => (
                <Form className="space-y-4">
                  <h2 className="text-lg font-semibold text-gray-700">Basic Book Details</h2>
                  <div className="space-y-4">
                    {/* Title and Author in one row */}
                    <div className="grid grid-cols-2 gap-4">
                      <TextField label="Title" name="title" />
                      <CreatableSelectWithQuery
                        label="Select Authors"
                        isMulti={true}
                        name="author_ids"
                        endPoint="authors?pagination=false"
                        createEndpoint="admin/authors"
                        queryKey="authors"
                        dataAccessKey="data"
                      />
                    </div>

                    {/* Description takes full row */}
                    <div className="col-span-2">
                      <TextArea label="Description" name="description" />
                    </div>

                    {/* Rest of the fields in 2-column layout */}
                    <div className="grid grid-cols-2 gap-4">
                      <Select
                        label="Visibility"
                        name="is_public"
                        options={[
                          { value: 1, label: "Public" },
                          { value: 0, label: "Private" },
                        ]}
                      />
                      <SelectWithQuery
                        label="Select Categories"
                        name="category_ids"
                        isMulti={true}
                        endPoint="categories?pagination=false"
                        queryKey="categories"
                        valueKey="id"
                        labelKey="name"
                        dataAccessKey="data"
                        returnSingleValue={false}
                      />
                      <Select
                        label={t("ebook.form.fields.pageDimension")}
                        name="page_dimension"
                        required
                        options={pageDimensions}
                        onChange={(selected) => {
                          setFieldValue("page_dimension", selected.value);
                          if (selected.value !== "custom") {
                            setFieldValue("width", selected.width);
                            setFieldValue("height", selected.height);
                          }
                        }}
                        value={
                          values.page_dimension === "custom"
                            ? pageDimensions.find((dim) => dim.value === "custom")
                            : pageDimensions.find(
                                (dim) =>
                                  dim.width === values.width &&
                                  dim.height === values.height
                              ) || pageDimensions[0]
                        }
                      />
                      {values.page_dimension !== "custom" && (
                        <div className="flex items-center justify-center h-full">
                          <span className="text-gray-500">
                            {values.width} × {values.height}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Custom dimension fields */}
                    {values.page_dimension === "custom" && (
                      <div className="grid grid-cols-2 gap-4">
                        <TextField
                          label={t("ebook.form.fields.width")}
                          name="width"
                          type="number"
                          required
                        />
                        <TextField
                          label={t("ebook.form.fields.height")}
                          name="height"
                          type="number"
                          required
                        />
                      </div>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="mt-4 flex justify-end">
                    <button
                      type="submit"
                      className="px-5 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition"
                    >
                      Next
                    </button>
                  </div>
                </Form>
              )}
            </Formik>
          )}

          {parseInt(stepId) === 2 && (
            <div>
              <h2 className="text-lg font-semibold text-gray-700">Choose a Template or Start Blank</h2>
              <p className="text-sm text-gray-500 mb-4">
                You can choose a template to start with, or begin with a blank template.
              </p>

              <div className="flex flex-col space-y-4">
                <button
                  onClick={() => navigate(`/ebooks/edit/${bookId}`)}
                  className="px-5 py-3 border border-blue-600 text-blue-600 font-semibold rounded-lg hover:bg-blue-50 transition text-sm"
                >
                  Start with a Blank Template
                </button>
                <button
                  onClick={() => navigate(`/editor/3/${bookId}`)}
                  className="px-5 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition text-sm"
                >
                  Choose a Template
                </button>
              </div>
            </div>
          )}

          {parseInt(stepId) === 3 && <Templates bookId={bookId} />}
        </div>
      </div>
    </div>
  );
}
