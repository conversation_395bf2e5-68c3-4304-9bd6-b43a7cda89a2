// src/editor/components/NoteTakingLayer.jsx
import React, { useState, useRef, useEffect } from 'react';
import {
  Stage,
  Layer,
  Line,
  Rect,
  Circle,
  RegularPolygon,
  Text as KonvaText,
  Transformer,
} from 'react-konva';
import { v4 as uuidv4 } from 'uuid';
import {
  MousePointer, // Alternative for Select
  Brush,
  PenTool,      // Alternative for Highlight
  Type,
  Square,
  Circle as CircleIcon,
  Triangle,
  Trash2,
} from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import { setSelectedTool } from '../store/ebookSlice';

/**
 * TransformerComponent attaches a Transformer to a selected shape or text.
 */
const TransformerComponent = ({ selectedNode }) => {
  const trRef = useRef(null);

  useEffect(() => {
    if (trRef.current && selectedNode) {
      trRef.current.nodes([selectedNode]);
      trRef.current.getLayer().batchDraw();
    }
  }, [selectedNode]);

  if (!selectedNode) return null;

  return (
    <Transformer
      ref={trRef}
      rotateEnabled={true}
      enabledAnchors={['top-left', 'top-right', 'bottom-left', 'bottom-right']}
      boundBoxFunc={(oldBox, newBox) => {
        // Limit the size of the shapes
        if (newBox.width < 5 || newBox.height < 5) {
          return oldBox;
        }
        return newBox;
      }}
    />
  );
};

const NoteTakingLayer = ({ width, height, scale }) => {
  // Constants for minimum sizes
  const MIN_SHAPE_WIDTH = 10;
  const MIN_SHAPE_HEIGHT = 10;
  const MIN_TEXT_WIDTH = 50;
  const MIN_TEXT_HEIGHT = 20;

  // Tools: 'select', 'brush', 'highlight', 'rectangle', 'circle', 'triangle', 'text'
  const dispatch = useDispatch();
  const selectedTool = useSelector((state) => state.ebook.selectedTool); // Use Redux state

  const handleToolChange = (tool) => {
    dispatch(setSelectedTool(tool)); // Dispatch action to update selected tool
  };

  // Brush/Highlight settings
  const [strokeColor, setStrokeColor] = useState('#000000');
  const [lineWidth, setLineWidth] = useState(3);

  // Lines (Brush & Highlight)
  const [lines, setLines] = useState([]);
  const [isDrawing, setIsDrawing] = useState(false);

  // Shapes: { id, type, x, y, width, height, radius, fill, stroke, strokeWidth, rotation, selected }
  const [shapes, setShapes] = useState([]);

  // Text Items: { id, type: 'text', x, y, text, isEditing, selected, rotation, width, height }
  const [textItems, setTextItems] = useState([]);

  // Selection
  const [selectedId, setSelectedId] = useState(null);
  const [selectedType, setSelectedType] = useState(null);

  // Shape Fill & Stroke Colors
  const [shapeFillColor, setShapeFillColor] = useState('#ffffff00');
  const [shapeStrokeColor, setShapeStrokeColor] = useState('#000000');

  // Temporary Shape State for Drawing
  const [tempShape, setTempShape] = useState(null);

  // Notification State
  const [notification, setNotification] = useState(null);

  const stageRef = useRef(null);

  /**
   * Get the pointer position adjusted for the stage scale.
   */
  const getPointerPosUnscaled = () => {
    const stage = stageRef.current.getStage();
    const pointer = stage.getPointerPosition();
    if (!pointer) return { x: 0, y: 0 };
    const scaleFactor = stage.scaleX();
    return { x: pointer.x / scaleFactor, y: pointer.y / scaleFactor };
  };

  /**
   * Deselect all elements.
   */
  const clearSelections = () => {
    setSelectedId(null);
    setSelectedType(null);
    setShapes((prev) => prev.map((s) => ({ ...s, selected: false })));
    setTextItems((prev) => prev.map((t) => ({ ...t, selected: false })));
    setLines((prev) => prev.map((l) => ({ ...l, selected: false })));
    setTempShape(null);
  };

  /**
   * Show a temporary notification message.
   */
  const showNotification = (message) => {
    setNotification(message);
    setTimeout(() => {
      setNotification(null);
    }, 2000); // Display for 2 seconds
  };

  /* -------------------------------------------------------------------------- */
  /*                          Brush & Highlight Drawing                         */
  /* -------------------------------------------------------------------------- */

  const handleMouseDown = (e) => {
    const stage = stageRef.current.getStage();
    const pointer = stage.getPointerPosition();
    const { x, y } = getPointerPosUnscaled();

    if (selectedTool === 'brush' || selectedTool === 'highlight') {
      setIsDrawing(true);
      const newId = uuidv4();
      setLines((prev) => [
        ...prev,
        {
          id: newId,
          type: 'line',
          points: [x, y],
          stroke: selectedTool === 'highlight' ? '#FFFF00' : strokeColor,
          strokeWidth: selectedTool === 'highlight' ? 15 : lineWidth,
          opacity: selectedTool === 'highlight' ? 0.3 : 1.0,
          selected: false,
        },
      ]);
    } else if (selectedTool === 'rectangle' || selectedTool === 'circle' || selectedTool === 'triangle') {
      // Start Drawing Shape
      const newId = uuidv4();
      const shapeType = selectedTool;
      const shape = {
        id: newId,
        type: shapeType,
        x,
        y,
        width: 0,
        height: 0,
        radius: 0,
        fill: '#ffffff00',
        stroke: shapeStrokeColor,
        strokeWidth: 2,
        rotation: 0,
        selected: false,
      };
      setTempShape(shape);
    } else if (selectedTool === 'text') {
      // Add Text at Click Position
      const newId = uuidv4();
      setTextItems((prev) => [
        ...prev,
        {
          id: newId,
          type: 'text',
          x,
          y,
          text: 'New Text',
          isEditing: true,
          selected: true,
          rotation: 0,
          width: 150,
          height: 50,
        },
      ]);
      setSelectedId(newId);
      setSelectedType('text');
    } else if (selectedTool === 'select') {
      // Selection is handled by Konva's onClick events
      // No action needed here
    }
  };

  const handleMouseMove = (e) => {
    if (isDrawing && (selectedTool === 'brush' || selectedTool === 'highlight')) {
      const { x, y } = getPointerPosUnscaled();
      setLines((prev) => {
        const lastLine = prev[prev.length - 1];
        return prev.slice(0, -1).concat({
          ...lastLine,
          points: [...lastLine.points, x, y],
        });
      });
    } else if (tempShape && (selectedTool === 'rectangle' || selectedTool === 'circle' || selectedTool === 'triangle')) {
      const { x, y } = getPointerPosUnscaled();
      const newWidth = x - tempShape.x;
      const newHeight = y - tempShape.y;

      if (selectedTool === 'rectangle') {
        setTempShape({
          ...tempShape,
          width: newWidth,
          height: newHeight,
        });
      } else if (selectedTool === 'circle') {
        const radius = Math.sqrt(newWidth * newWidth + newHeight * newHeight);
        setTempShape({
          ...tempShape,
          radius,
        });
      } else if (selectedTool === 'triangle') {
        const radius = Math.sqrt(newWidth * newWidth + newHeight * newHeight);
        setTempShape({
          ...tempShape,
          radius,
        });
      }
    }
  };

  const handleMouseUp = (e) => {
    if (isDrawing && (selectedTool === 'brush' || selectedTool === 'highlight')) {
      setIsDrawing(false);
    } else if (tempShape && (selectedTool === 'rectangle' || selectedTool === 'circle' || selectedTool === 'triangle')) {
      // Finalize Shape
      const finalizedShape = { ...tempShape };
      // Adjust for negative width/height
      if (selectedTool === 'rectangle') {
        if (finalizedShape.width < MIN_SHAPE_WIDTH || finalizedShape.height < MIN_SHAPE_HEIGHT) {
          showNotification('Shape too small! Minimum size not met.');
          setTempShape(null);
          return;
        }
        if (finalizedShape.width < 0) {
          finalizedShape.x += finalizedShape.width;
          finalizedShape.width = Math.abs(finalizedShape.width);
        }
        if (finalizedShape.height < 0) {
          finalizedShape.y += finalizedShape.height;
          finalizedShape.height = Math.abs(finalizedShape.height);
        }
      } else if (selectedTool === 'circle') {
        if (finalizedShape.radius < MIN_SHAPE_WIDTH) { // Using width as radius minimum
          showNotification('Circle too small! Minimum radius not met.');
          setTempShape(null);
          return;
        }
      } else if (selectedTool === 'triangle') {
        if (finalizedShape.radius < MIN_SHAPE_WIDTH) { // Using width as radius minimum
          showNotification('Triangle too small! Minimum size not met.');
          setTempShape(null);
          return;
        }
      }

      setShapes((prev) => [...prev, finalizedShape]);
      setTempShape(null);
    }
  };

  /* -------------------------------------------------------------------------- */
  /*                          Selection & Deletion                              */
  /* -------------------------------------------------------------------------- */

  const handleSelectItem = (id, type) => {
    clearSelections();
    if (type === 'shape') {
      setShapes((prev) =>
        prev.map((s) => (s.id === id ? { ...s, selected: true } : s))
      );
    } else if (type === 'text') {
      setTextItems((prev) =>
        prev.map((t) => (t.id === id ? { ...t, selected: true } : t))
      );
    } else if (type === 'line') {
      setLines((prev) =>
        prev.map((l) => (l.id === id ? { ...l, selected: true } : l))
      );
    }
    setSelectedId(id);
    setSelectedType(type);
  };

  const handleDeleteSelected = () => {
    if (!selectedId || !selectedType) return;
    if (selectedType === 'shape') {
      setShapes((prev) => prev.filter((s) => s.id !== selectedId));
    } else if (selectedType === 'text') {
      setTextItems((prev) => prev.filter((t) => t.id !== selectedId));
    } else if (selectedType === 'line') {
      setLines((prev) => prev.filter((l) => l.id !== selectedId));
    }
    setSelectedId(null);
    setSelectedType(null);
  };

  /* -------------------------------------------------------------------------- */
  /*                             Text Editing                                   */
  /* -------------------------------------------------------------------------- */

  const handleDoubleClickText = (id) => {
    setTextItems((prev) =>
      prev.map((t) =>
        t.id === id ? { ...t, isEditing: true } : { ...t, isEditing: false }
      )
    );
  };

  const handleTextChange = (id, newText) => {
    setTextItems((prev) =>
      prev.map((t) => (t.id === id ? { ...t, text: newText } : t))
    );
  };

  const handleFinishEdit = (id) => {
    setTextItems((prev) =>
      prev.map((t) => (t.id === id ? { ...t, isEditing: false } : t))
    );
  };

  /* -------------------------------------------------------------------------- */
  /*                  Transform & Drag for Shapes and Text                     */
  /* -------------------------------------------------------------------------- */

  const handleShapeDragEnd = (e, shape) => {
    const { x, y } = e.target.position();
    setShapes((prev) =>
      prev.map((s) => (s.id === shape.id ? { ...s, x, y } : s))
    );
  };

  const handleShapeTransform = (e, shape) => {
    const node = e.target;
    const scaleX = node.scaleX();
    const scaleY = node.scaleY();
    const rotation = node.rotation();

    node.scaleX(1);
    node.scaleY(1);

    if (shape.type === 'rectangle') {
      const newWidth = Math.max(5, shape.width * scaleX);
      const newHeight = Math.max(5, shape.height * scaleY);
      setShapes((prev) =>
        prev.map((s) =>
          s.id === shape.id
            ? { ...s, x: node.x(), y: node.y(), width: newWidth, height: newHeight, rotation }
            : s
        )
      );
    } else {
      // Circle and Triangle
      const newRadius = Math.max(5, shape.radius * Math.max(scaleX, scaleY));
      setShapes((prev) =>
        prev.map((s) =>
          s.id === shape.id
            ? { ...s, x: node.x(), y: node.y(), radius: newRadius, rotation }
            : s
        )
      );
    }
  };

  const handleTextDragEnd = (e, text) => {
    const { x, y } = e.target.position();
    setTextItems((prev) =>
      prev.map((t) => (t.id === text.id ? { ...t, x, y } : t))
    );
  };

  const handleTextTransform = (e, text) => {
    const node = e.target;
    const scaleX = node.scaleX();
    const scaleY = node.scaleY();
    const rotation = node.rotation();

    node.scaleX(1);
    node.scaleY(1);

    const newWidth = Math.max(MIN_TEXT_WIDTH, text.width * scaleX);
    const newHeight = Math.max(MIN_TEXT_HEIGHT, text.height * scaleY);

    setTextItems((prev) =>
      prev.map((t) =>
        t.id === text.id
          ? { ...t, x: node.x(), y: node.y(), width: newWidth, height: newHeight, rotation }
          : t
      )
    );
  };

  /* -------------------------------------------------------------------------- */
  /*                          Shape Fill & Stroke Handling                      */
  /* -------------------------------------------------------------------------- */

  useEffect(() => {
    if (selectedType === 'shape') {
      const selectedShape = shapes.find((s) => s.id === selectedId);
      if (selectedShape) {
        setShapeFillColor(selectedShape.fill || '#ffffff00');
        setShapeStrokeColor(selectedShape.stroke || '#000000');
      }
    }
  }, [selectedId, selectedType, shapes]);

  const handleFillColorChange = (color) => {
    setShapeFillColor(color);
    setShapes((prev) =>
      prev.map((s) => (s.id === selectedId ? { ...s, fill: color } : s))
    );
  };

  const handleShapeStrokeColorChange = (color) => {
    setShapeStrokeColor(color);
    setShapes((prev) =>
      prev.map((s) => (s.id === selectedId ? { ...s, stroke: color } : s))
    );
  };

  /* -------------------------------------------------------------------------- */
  /*                             Rendering Components                            */
  /* -------------------------------------------------------------------------- */

  return (
    <>
      {/* Notification */}
      {notification && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 bg-red-100 text-red-700 px-4 py-2 rounded shadow">
          {notification}
        </div>
      )}

      {/* Canvas Container */}
      <div className="absolute inset-0">
        <Stage
          ref={stageRef}
          width={width}
          height={height}
          style={{ width, height }}
          className="pointer-events-auto border"
          onMouseDown={(e) => {
            // Deselect when clicking on empty area
            const clickedOnEmpty = e.target === e.target.getStage();
            if (clickedOnEmpty) {
              clearSelections();
            }
            handleMouseDown(e);
          }}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
        >
          <Layer>
            {/* Lines (Brush & Highlight) */}
            {lines.map((line) => (
              <Line
                key={line.id}
                id={line.id}
                points={line.points}
                stroke={line.stroke}
                strokeWidth={line.strokeWidth}
                opacity={line.opacity}
                tension={0.5}
                lineCap="round"
                lineJoin="round"
                draggable
                onClick={() => handleSelectItem(line.id, 'line')}
                onTap={() => handleSelectItem(line.id, 'line')}
                onDragEnd={(e) => handleShapeDragEnd(e, line)}
                onTransformEnd={(e) => handleShapeTransform(e, line)}
                // Optional: Change color when selected
                stroke={line.selected ? '#FF0000' : line.stroke}
              />
            ))}

            {/* Temporary Shape for Drawing */}
            {tempShape && (selectedTool === 'rectangle' || selectedTool === 'circle' || selectedTool === 'triangle') && (
              <>
                {tempShape.type === 'rectangle' && (
                  <Rect
                    x={tempShape.x}
                    y={tempShape.y}
                    width={tempShape.width}
                    height={tempShape.height}
                    fill={tempShape.fill}
                    stroke={tempShape.stroke}
                    strokeWidth={tempShape.strokeWidth}
                    rotation={tempShape.rotation}
                  />
                )}
                {tempShape.type === 'circle' && (
                  <Circle
                    x={tempShape.x}
                    y={tempShape.y}
                    radius={tempShape.radius}
                    fill={tempShape.fill}
                    stroke={tempShape.stroke}
                    strokeWidth={tempShape.strokeWidth}
                    rotation={tempShape.rotation}
                  />
                )}
                {tempShape.type === 'triangle' && (
                  <RegularPolygon
                    x={tempShape.x}
                    y={tempShape.y}
                    sides={3}
                    radius={tempShape.radius}
                    fill={tempShape.fill}
                    stroke={tempShape.stroke}
                    strokeWidth={tempShape.strokeWidth}
                    rotation={tempShape.rotation}
                  />
                )}
              </>
            )}

            {/* Shapes (Rectangle, Circle, Triangle) */}
            {shapes.map((shape) => {
              let ShapeComponent;
              if (shape.type === 'rectangle') ShapeComponent = Rect;
              else if (shape.type === 'circle') ShapeComponent = Circle;
              else if (shape.type === 'triangle') ShapeComponent = RegularPolygon;

              return (
                <ShapeComponent
                  key={shape.id}
                  id={shape.id}
                  x={shape.x}
                  y={shape.y}
                  width={shape.width}
                  height={shape.height}
                  radius={shape.radius}
                  sides={shape.type === 'triangle' ? 3 : undefined}
                  fill={shape.fill}
                  stroke={shape.stroke}
                  strokeWidth={shape.strokeWidth}
                  rotation={shape.rotation}
                  draggable
                  onClick={() => handleSelectItem(shape.id, 'shape')}
                  onTap={() => handleSelectItem(shape.id, 'shape')}
                  onDragEnd={(e) => handleShapeDragEnd(e, shape)}
                  onTransformEnd={(e) => handleShapeTransform(e, shape)}
                />
              );
            })}

            {/* Text Items */}
            {textItems.map((text) => (
              <KonvaText
                key={text.id}
                id={text.id}
                x={text.x}
                y={text.y}
                text={text.text}
                fontSize={16}
                fill="#000"
                width={text.width}
                height={text.height}
                rotation={text.rotation}
                draggable
                onClick={() => handleSelectItem(text.id, 'text')}
                onTap={() => handleSelectItem(text.id, 'text')}
                onDblClick={() => handleDoubleClickText(text.id)}
                onDragEnd={(e) => handleTextDragEnd(e, text)}
                onTransformEnd={(e) => handleTextTransform(e, text)}
              />
            ))}

            {/* Transformer */}
            {selectedId && (selectedType === 'shape' || selectedType === 'text') && (
              <TransformerComponent
                selectedNode={
                  selectedType === 'shape'
                    ? stageRef.current.getStage().findOne(`#${selectedId}`)
                    : selectedType === 'text'
                    ? stageRef.current.getStage().findOne(`#${selectedId}`)
                    : null
                }
              />
            )}
          </Layer>
        </Stage>

        {/* Text Editing Overlay */}
        {textItems
          .filter((t) => t.isEditing)
          .map((t) => (
            <textarea
              key={t.id}
              style={{
                position: 'absolute',
                top: (t.y + t.height / 2) / scale,
                left: (t.x + t.width / 2) / scale,
                transform: 'translate(-50%, -50%)',
                width: `${t.width}px`,
                border: '1px solid #ccc',
                fontSize: '14px',
                resize: 'none',
                outline: 'none',
                padding: '2px',
              }}
              value={t.text}
              onChange={(e) => handleTextChange(t.id, e.target.value)}
              onBlur={() => handleFinishEdit(t.id)}
              autoFocus
            />
          ))}
      </div>

      {/* Bottom Toolbar */}
      <div className="fixed bottom-0 left-0 right-0 bg-white shadow-md p-3 pointer-events-auto flex flex-row items-center gap-4">
        {/* Select Tool */}
        <button
          className={`flex items-center gap-1 px-3 py-2 rounded border 
            ${selectedTool === 'select' ? 'bg-gray-200' : 'bg-gray-50'}`}
          onClick={() => handleToolChange('select')}
        >
          <MousePointer size={20} />
        </button>

        {/* Brush Tool */}
        <button
          className={`flex items-center gap-1 px-3 py-2 rounded border 
            ${selectedTool === 'brush' ? 'bg-gray-200' : 'bg-gray-50'}`}
          onClick={() => handleToolChange('brush')}
        >
          <Brush size={20} />
        </button>

        {/* Highlight Tool */}
        <button
          className={`flex items-center gap-1 px-3 py-2 rounded border 
            ${selectedTool === 'highlight' ? 'bg-gray-200' : 'bg-gray-50'}`}
          onClick={() => handleToolChange('highlight')}
        >
          <PenTool size={20} />
        </button>

        {/* Shape Tools */}
        <button
          className={`flex items-center gap-1 px-3 py-2 rounded border 
            ${selectedTool === 'rectangle' ? 'bg-gray-200' : 'bg-gray-50'}`}
          onClick={() => handleToolChange('rectangle')}
        >
          <Square size={20} />
        </button>

        <button
          className={`flex items-center gap-1 px-3 py-2 rounded border 
            ${selectedTool === 'circle' ? 'bg-gray-200' : 'bg-gray-50'}`}
          onClick={() => handleToolChange('circle')}
        >
          <CircleIcon size={20} />
        </button>

        <button
          className={`flex items-center gap-1 px-3 py-2 rounded border 
            ${selectedTool === 'triangle' ? 'bg-gray-200' : 'bg-gray-50'}`}
          onClick={() => handleToolChange('triangle')}
        >
          <Triangle size={20} />
        </button>

        {/* Text Tool */}
        <button
          className={`flex items-center gap-1 px-3 py-2 rounded border 
            ${selectedTool === 'text' ? 'bg-gray-200' : 'bg-gray-50'}`}
          onClick={() => handleToolChange('text')}
        >
          <Type size={20} />
        </button>

        {/* Brush/Highlight Color & Width */}
        {(selectedTool === 'brush' || selectedTool === 'highlight') && (
          <>
            <div className="flex items-center gap-1 ml-4">
              <input
                type="color"
                value={strokeColor}
                onChange={(e) => setStrokeColor(e.target.value)}
                className="w-8 h-8 border rounded"
                disabled={selectedTool === 'highlight'}
              />
            </div>
            <div className="flex items-center gap-1">
              <input
                type="number"
                className="border rounded p-1 w-16"
                value={lineWidth}
                onChange={(e) => setLineWidth(Number(e.target.value))}
                disabled={selectedTool === 'highlight'}
              />
            </div>
          </>
        )}

        {/* Fill & Stroke for Shapes */}
        {selectedId && selectedType === 'shape' && (
          <div className="flex items-center gap-2 ml-4">
            <div className="flex items-center gap-1">
              <span className="font-semibold">Fill:</span>
              <input
                type="color"
                className="w-8 h-8 border rounded"
                value={shapeFillColor}
                onChange={(e) => handleFillColorChange(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-1">
              <input
                type="color"
                className="w-8 h-8 border rounded"
                value={shapeStrokeColor}
                onChange={(e) => handleShapeStrokeColorChange(e.target.value)}
              />
            </div>
          </div>
        )}

        {/* Delete Selected */}
        {selectedId && (
          <button
            onClick={handleDeleteSelected}
            className="ml-auto border rounded px-4 py-2 bg-red-100 hover:bg-red-200 flex items-center gap-1"
          >
            <Trash2 size={20} />
          </button>
        )}
      </div>
    </>
  );
};

export default NoteTakingLayer;
