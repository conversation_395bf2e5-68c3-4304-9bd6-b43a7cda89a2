export const shapes = {
    rectangle: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" preserveAspectRatio="xMidYMid meet"><rect width="30" height="30" style="fill:rgb(128,128,128)" /></svg>`,
    circle: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" preserveAspectRatio="xMidYMid meet"><circle cx="15" cy="15" r="12" style="fill:rgb(128,128,128)" /></svg>`,
    star: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" preserveAspectRatio="xMidYMid meet"><polygon points="15,0 18,12 30,12 20,20 23,30 15,23 8,30 11,20 0,12 12,12" style="fill:rgb(128,128,128)" /></svg>`,
    hexagon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" preserveAspectRatio="xMidYMid meet"><polygon points="15,0 30,7.5 30,22.5 15,30 0,22.5 0,7.5" style="fill:rgb(128,128,128)" /></svg>`,
    triangle: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" preserveAspectRatio="xMidYMid meet"><polygon points="15,0 30,30 0,30" style="fill:rgb(128,128,128)" /></svg>`,
  };