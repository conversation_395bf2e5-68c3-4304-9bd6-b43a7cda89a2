import React from "react";
import { Edit, Trash } from "lucide-react";
import noImage from '@/assets/noImage.png';

const EbookCard = ({ book, onCardClick, onEdit, onDelete }) => {
  const showFooter = onEdit || onDelete;
  return (
    <div className="border border-gray-300 bg-white rounded-lg flex flex-col items-start relative dark:border-gray-600 dark:bg-gray-800">
      <div onClick={onCardClick ? () => onCardClick(book) : undefined} className="w-full cursor-pointer">
        <span className="absolute top-0 right-0 m-4 bg-blue-500 text-white text-xs rounded-full px-3 py-1 dark:bg-blue-700 dark:text-gray-100">
          eBook
        </span>
        {book.cover_image_url ? (
          <img
            src={`${import.meta.env.VITE_HOST_URL}/storage/${book.cover_image}`}
            alt={book.title}
            className="w-full h-48 object-cover border-b dark:border-gray-600 rounded-t-lg"
          />
        ) : (
          <img
            src={noImage}
            alt="Placeholder"
            className="w-full h-48 object-cover border-b dark:border-gray-600 rounded-t-lg"
          />
        )}
        <div className="p-4 dark:bg-gray-800">
          <h3 className="text-md font-semibold mb-2 dark:text-gray-200">{book.title}</h3>
          <p className="text-sm text-gray-600 mb-2 line-clamp-1 dark:text-gray-400">{book.author}</p>
          <p className="text-sm text-gray-500 line-clamp-1 dark:text-gray-500">
            {book.description || "No description available."}
          </p>
        </div>
      </div>
      {showFooter && (
        <div className="flex items-center gap-4 mt-auto border-t dark:border-gray-600 p-4 w-full rounded-lg dark:bg-gray-800">
          {onEdit && (
            <button
              className="text-blue-500 hover:border-blue-700 hover:text-blue-700 font-bold rounded dark:text-blue-700 dark:hover:text-blue-800"
              onClick={() => onEdit(book)}
            >
              <Edit size={20} />
            </button>
          )}
          {onDelete && (
            <button
              className="text-red-500 hover:border-red-700 hover:text-red-700 font-bold rounded dark:text-red-700 dark:hover:text-red-800"
              onClick={(e) => onDelete(e, book.id)}
            >
              <Trash size={20} />
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default EbookCard;
