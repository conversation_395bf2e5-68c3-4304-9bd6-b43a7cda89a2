import React, { useCallback, useState, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import Icons from '@/components/ui/Icon';
import Button from './Button';

const Dropzone = ({ onDrop, fileName }) => {
    const [files, setFiles] = useState([]);

    // If fileName is passed, initialize the files state with it
    useEffect(() => {
        if (fileName) {
            setFiles([fileName]);
        }
    }, [fileName]);

    const handleDrop = useCallback((acceptedFiles) => {
        const fileNames = acceptedFiles.map(file => file.name);
        setFiles(fileNames);
        onDrop(acceptedFiles); // Pass the selected file back to Formik
    }, [onDrop]);

    const { getRootProps, getInputProps, isDragActive } = useDropzone({ onDrop: handleDrop });

    return (
        <div className="w-full">
            <div
                {...getRootProps()}
                className={`flex flex-col items-center justify-center w-full h-64 p-6 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 bg-gray-50'
                    }`}
            >
                <input {...getInputProps()} />
                <div className="text-center">
                    {files.length > 0 ? (
                        <>
                            <p className="text-gray-800 font-semibold">
                                Selected file{files.length > 1 ? 's' : ''}: {files.join(', ')}
                            </p>
                            <div className='text-center'>
                                <p className="text-[#697370] mt-2">
                                    Drag & drop the PDF
                                </p>
                                <Button
                                    className="btn flex items-center rounded-lg bg-[#222222] text-base mt-3 font-normal text-white py-2 px-5"
                                >
                                    Replace <Icons icon="heroicons:arrow-up-tray" width={18} className="ml-5" />
                                </Button>
                            </div>
                        </>


                    ) : (
                        <>
                            <p className="text-[#697370]">
                                Drag & drop the PDF
                            </p>
                            <div
                                className="btn flex items-center rounded-lg bg-[#222222] text-base mt-3 font-normal text-white py-2 px-5"
                            >
                                Upload <Icons icon="heroicons:arrow-up-tray" width={18} className="ml-5" />
                            </div>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Dropzone;
