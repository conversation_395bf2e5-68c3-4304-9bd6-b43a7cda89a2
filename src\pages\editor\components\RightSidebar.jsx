import { useState } from "react";
import Tabs from "@/components/ui/Tabs"; // Corrected the import path to match the file name casing
import PageNavigator from "./right-sidebar/PageNavigator";
import Properties from "./Properties";
import Layers from "./right-sidebar/Layers";

export default function RightSidebar({ handlePageSelect, handleDeletePage, handleAddPage, syncWithLatestData }) {
  const [activeTab, setActiveTab] = useState(1);

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  return (
    <div className="h-full w-[350px] dark:bg-gray-700 bg-white flex flex-col overflow-y-auto border-b border-r border-l ml-auto p-4">
      <Tabs
        activeTab={activeTab} // Pass activeTab state
        onTabChange={handleTabChange} // Pass handleTabChange function
        tabs={[
          { label: "Pages", content: <PageNavigator
            handlePageSelect={handlePageSelect}
            handleDeletePage={handleDeletePage}
            handleAddPage={handleAddPage}
            syncWithLatestData={syncWithLatestData}
          /> },
          { label: "Properties", content: <Properties /> },
          { label: "Layers", content: <Layers /> },
        ]}
      />
    </div>
  );
}
