import React from 'react';
import moment from 'moment';

const InquiryDetails = ({ inquiry }) => {
  return (
    <div>
      <div className="mb-2 dark:mb-2">
        <strong className="dark:text-gray-300">Name:</strong> <span className="dark:text-gray-300">{inquiry.name}</span>
      </div>
      <div className="mb-2 dark:mb-2">
        <strong className="dark:text-gray-300">Email:</strong> <span className="dark:text-gray-300">{inquiry.email}</span>
      </div>
      <div className="mb-2 dark:mb-2">
        <strong className="dark:text-gray-300">Subject:</strong> <span className="dark:text-gray-300">{inquiry.subject}</span>
      </div>
      <div className="mb-2 dark:mb-2">
        <strong className="dark:text-gray-300">Status:</strong> <span className="dark:text-gray-300">{inquiry.status}</span>
      </div>
      <div className="mb-2 dark:mb-2">
        <strong className="dark:text-gray-300">Created At:</strong> <span className="dark:text-gray-300">{moment(inquiry.created_at).format("MMMM Do YYYY, h:mm:ss a")}</span>
      </div>
      {/* Add more details as needed */}
    </div>
  );
};

export default InquiryDetails; 