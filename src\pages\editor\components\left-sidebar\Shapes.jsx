import React, { useState } from 'react'
import { Icon } from "@iconify/react";
import Tabs from '@/components/ui/Tabs'
import { useDispatch } from 'react-redux';
import { addElement } from '@/pages/editor/store/pagesSlice';

const Shapes = () => {
  const [activeTab, setActiveTab] = useState(0);
  const dispatch = useDispatch();

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  const handleShapeClick = (shapeType) => {
    dispatch(addElement({
      type: 'shape',
      content: { type: shapeType },
      position: { x: 50, y: 50 },
      style: { fill: '#808080', stroke: '#000000', strokeWidth: 2 },
      size: { width: 100, height: 100 },
    }));
  };

  const handleDragStart = (e, shapeType) => {
    e.dataTransfer.setData('type', 'shape');
    e.dataTransfer.setData('content', JSON.stringify({ type: shapeType }));
    e.dataTransfer.setData('style', JSON.stringify({ fill: '#808080', stroke: '#000000', strokeWidth: 2 }));
    e.dataTransfer.setData('size', JSON.stringify({ width: 100, height: 100 }));
  };

  const tabs = [
    {
      label: "Shapes",
      content: (
        <div className="grid grid-cols-3 gap-4">
          <div
            key="rectangle"
            className="flex flex-col items-center border dark:border-gray-600 p-4 w-full cursor-move"
            onClick={() => handleShapeClick('rectangle')}
            draggable={true}
            onDragStart={(e) => handleDragStart(e, 'rectangle')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
              <rect width="100" height="100" style={{ fill: 'rgb(128,128,128)' }} />
            </svg>
          </div>
          <div
            key="circle"
            className="flex flex-col items-center border dark:border-gray-600 p-4 w-full cursor-move"
            onClick={() => handleShapeClick('circle')}
            draggable={true}
            onDragStart={(e) => handleDragStart(e, 'circle')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
              <circle cx="50" cy="50" r="50" style={{ fill: 'rgb(128,128,128)' }} />
            </svg>
          </div>
          <div
            key="star"
            className="flex flex-col items-center border dark:border-gray-600 p-4 w-full cursor-move"
            onClick={() => handleShapeClick('star')}
            draggable={true}
            onDragStart={(e) => handleDragStart(e, 'star')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
              <polygon points="50,0 60,40 100,40 70,60 80,100 50,75 20,100 30,60 0,40 40,40" style={{ fill: 'rgb(128,128,128)' }} />
            </svg>
          </div>
          <div
            key="hexagon"
            className="flex flex-col items-center border dark:border-gray-600 p-4 w-full cursor-move"
            onClick={() => handleShapeClick('hexagon')}
            draggable={true}
            onDragStart={(e) => handleDragStart(e, 'hexagon')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
              <polygon points="50,0 100,25 100,75 50,100 0,75 0,25" style={{ fill: 'rgb(128,128,128)' }} />
            </svg>
          </div>
          <div
            key="triangle"
            className="flex flex-col items-center border dark:border-gray-600 p-4 w-full cursor-move"
            onClick={() => handleShapeClick('triangle')}
            draggable={true}
            onDragStart={(e) => handleDragStart(e, 'triangle')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
              <polygon points="50,0 100,100 0,100" style={{ fill: 'rgb(128,128,128)' }} />
            </svg>
          </div>
        </div>
      )
    },
    {
      label: "Artwork",
      content: (
        <div className="grid grid-cols-3 gap-4">
          {/* Artwork content can be added here */}
        </div>
      )
    }
  ];

  return (
    <div className="h-full">
      <Tabs tabs={tabs} activeTab={activeTab} onChange={handleTabChange} />
    </div>
  );
};

export default Shapes;