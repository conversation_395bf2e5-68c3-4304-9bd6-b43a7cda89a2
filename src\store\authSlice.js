import { createSlice } from "@reduxjs/toolkit";
import { setToken, setUser, removeToken, removeUser, loadAuthFromCookies } from "../lib/auth";

// Initialize state from cookies if available
const initialState = loadAuthFromCookies();

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    login: (state, action) => {
      state.isAuthenticated = true;
      state.token = action.payload.token;
      state.user = action.payload.user;
      
      // Save to cookies
      setToken(action.payload.token);
      setUser(action.payload.user);
    },
    logout: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.token = null;
      
      // Remove from cookies
      removeToken();
      removeUser();
    },
    updateUser: (state, action) => {
      state.user = action.payload;
      
      // Update user in cookie
      setUser(action.payload);
    },
    // Add a new action to initialize auth state from cookies
    initFromCookies: (state) => {
      const authData = loadAuthFromCookies();
      state.isAuthenticated = authData.isAuthenticated;
      state.token = authData.token;
      state.user = authData.user;
    },
  },
});

export const { login, logout, updateUser, initFromCookies } = authSlice.actions;
export const selectAuth = (state) => state.auth;
export const selectUser = (state) => state.auth.user;
export const selectIsAuthenticated = (state) => state.auth.isAuthenticated;

export default authSlice.reducer;
