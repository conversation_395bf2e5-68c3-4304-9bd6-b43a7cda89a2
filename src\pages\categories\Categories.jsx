import React, { useState, useEffect } from "react";
import useDataFetching from "@/hooks/useDataFetching";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import GenreForm from "./CategoryForm";
import api from "@/lib/axios";
import { useQueryClient } from "@tanstack/react-query";
import { Edit, Trash } from 'lucide-react';
import Confirm from "@/components/ui/Confirm";
import { useTranslation } from "react-i18next";

const Categories = () => {
  const { t } = useTranslation();
  const [state, setState] = useState({
    page: 1,
    pageSize: 10,
    search: "",
    debouncedSearch: "",
    isModalOpen: false,
    isEditMode: false,
    currentCategory: null,
  });

  const queryClient = useQueryClient();

  const { data: categoryData, isLoading, refetch } = useDataFetching({
    queryKey: ["categoryList", state.page, state.pageSize, state.debouncedSearch],
    endPoint: `admin/categories?page=${state.page}&per_page=${state.pageSize}&search=${state.debouncedSearch}`,
  });

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setState((prev) => ({ ...prev, debouncedSearch: state.search }));
    }, 500);

    return () => clearTimeout(delayDebounceFn);
  }, [state.search]);

  const handleModalOpen = (editMode = false, category = null) => {
    setState((prev) => ({
      ...prev,
      isModalOpen: true,
      isEditMode: editMode,
      currentCategory: category,
    }));
  };

  const handleModalClose = () => {
    setState((prev) => ({ ...prev, isModalOpen: false, currentCategory: null }));
  };

  const handleDeleteClick = async (categoryId) => {
    Confirm(async () => {
      try {
        await api.delete(`admin/categories/${categoryId}`);
        queryClient.invalidateQueries("categoryList");
      } catch (error) {
        console.error(t("genresPage.deleteError"), error);
      }
    });
  };

  const handleFormSubmit = async (values, { resetForm }) => {
    const formData = new FormData();
    formData.append("name", values.name);
    const isActiveValue = values.is_active === "1" || values.is_active === "0"
      ? values.is_active
      : state.currentCategory?.is_active ? "1" : "0";
    formData.append("is_active", isActiveValue);
    if (values.thumbnail && typeof values.thumbnail === "object") {
      formData.append("thumbnail", values.thumbnail);
    }

    if (state.isEditMode) {
      formData.append("_method", "put");
    }

    try {
      const url = state.isEditMode
        ? `admin/categories/${state.currentCategory.id}`
        : "admin/categories";
      await api.post(url, formData);
      queryClient.invalidateQueries("categoryList");
      resetForm();
      handleModalClose();
    } catch (error) {
      console.error(t("genresPage.submitError"), error);
    }
  };

  return (
    <>
      <DataTable
        title={t("category.list")}
        columns={[
          {
            Header: t("genresPage.name"),
            accessor: "name",
          },
          {
            Header: t("genresPage.image"),
            accessor: "thumbnail",
            Cell: ({ value }) => (
              <img src={`${import.meta.env.VITE_HOST_URL}/storage/${value}`} alt="thumbnail" className="h-10 w-10 object-cover" />
            ),
          },
          {
            Header: t("genresPage.status"),
            accessor: "is_active",
            Cell: ({ value }) => (
              <span
                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${value ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                  }`}
              >
                {value ? t("genresPage.active") : t("genresPage.inactive")}
              </span>
            ),
          },
          {
            Header: t("genresPage.action"),
            accessor: "id",
            Cell: ({ value, row }) => (
              <div className="flex justify-center">
                <button
                  className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-2 rounded-lg"
                  onClick={() => handleModalOpen(true, row.original)}
                >
                  <Edit size={16} />
                </button>
                <button
                  className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-2 rounded-lg ml-2"
                  onClick={() => handleDeleteClick(value)}
                >
                  <Trash size={16} />
                </button>
              </div>
            ),
          },
        ]}
        data={categoryData?.data?.data || []}
        fetchData={refetch}
        loading={isLoading}
        totalPages={categoryData?.data?.total_pages || 1}
        currentPage={categoryData?.data?.current_page || 1}
        pageSize={state.pageSize}
        onPageChange={(page) => setState((prev) => ({ ...prev, page }))}
        onPageSizeChange={(pageSize) => setState((prev) => ({ ...prev, pageSize }))}
        onSearch={(search) => setState((prev) => ({ ...prev, search }))}
        buttonLabel={t("Add Category")}
        onButtonClick={() => handleModalOpen(false)}
      />

      {state.isModalOpen && (
        <Modal
          activeModal={state.isModalOpen}
          onClose={handleModalClose}
          title={state.isEditMode ? t("genresPage.editGenre") : t("genresPage.addGenre")}
        >
          <GenreForm
            initialValues={{
              name: state.currentCategory?.name || "",
              is_active: state.currentCategory?.is_active ? '1' : '0',
              thumbnail: state.currentCategory?.thumbnail || '',
            }}
            onSubmit={handleFormSubmit}
            isEditMode={state.isEditMode}
          />
        </Modal>
      )}
    </>
  );
};

export default Categories;
