import React, { useState, useEffect } from 'react';
import {
  Bold,
  Italic,
  Underline,
  Strikethrough,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
} from 'lucide-react';

const TextProperties = () => {
  const [fontFamily, setFontFamily] = useState('Arial');
  const [fontSize, setFontSize] = useState('3');
  const [color, setColor] = useState('#000000');
  const [showColorPicker, setShowColorPicker] = useState(false);

  const colorPalette = [
    '#000000', '#666666', '#999999', '#CCCCCC', '#EEEEEE', '#FFFFFF',
    '#FF0000', '#FF9900', '#FFFF00', '#00FF00', '#00FFFF', '#0000FF', '#9900FF', '#FF00FF',
    '#F4CCCC', '#FCE5CD', '#FFF2CC', '#D9EAD3', '#D0E0E3', '#CFE2F3', '#D9D2E9', '#EAD1DC',
    '#EA9999', '#F9CB9C', '#FFE599', '#B6D7A8', '#A2C4C9', '#9FC5E8', '#B4A7D6', '#D5A6BD',
    '#E06666', '#F6B26B', '#FFD966', '#93C47D', '#76A5AF', '#6FA8DC', '#8E7CC3', '#C27BA0',
    '#CC0000', '#E69138', '#F1C232', '#6AA84F', '#45818E', '#3D85C6', '#674EA7', '#A64D79',
    '#990000', '#B45F06', '#BF9000', '#38761D', '#134F5C', '#0B5394', '#351C75', '#741B47',
    '#660000', '#783F04', '#7F6000', '#274E13', '#0C343D', '#073763', '#20124D', '#4C1130',
  ];

  const execCommand = (command, value = null) => {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const savedRange = range.cloneRange();
      document.execCommand(command, false, value);
      selection.removeAllRanges();
      selection.addRange(savedRange);
    }
  };

  const updateSelectionProperties = () => {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const container = range.commonAncestorContainer;
      const element = container.nodeType === 3 ? container.parentNode : container;
      const computedStyle = window.getComputedStyle(element);

      setFontFamily(computedStyle.fontFamily.replace(/['"]+/g, '') || 'Arial');
      setFontSize(document.queryCommandValue('FontSize') || '3');

      const colorValue = computedStyle.color;
      const rgbToHex = (rgb) => {
        const result = rgb.match(/\d+/g);
        return result
          ? '#' +
              result
                .map((x) => parseInt(x).toString(16).padStart(2, '0'))
                .join('')
          : '#000000';
      };
      setColor(rgbToHex(colorValue));
    }
  };

  useEffect(() => {
    document.addEventListener('selectionchange', updateSelectionProperties);
    return () => {
      document.removeEventListener('selectionchange', updateSelectionProperties);
    };
  }, []);

  const handleColorChange = (col) => {
    setColor(col);
    execCommand('ForeColor', col);
    setShowColorPicker(false);
  };

  return (
    <div className="space-y-4">
      <div className="flex space-x-4 items-center">
        {/* Font Family Selector */}
        <div className="flex flex-col">
          <label className="mb-1 dark:text-white">Font Family</label>
          <select
            value={fontFamily}
            onChange={(e) => {
              setFontFamily(e.target.value);
              execCommand('FontName', e.target.value);
            }}
            className="rounded-md border-gray-300 shadow-sm text-sm p-2"
          >
            <option value="Roboto">Roboto</option>
            <option value="Arial">Arial</option>
            <option value="Times New Roman">Times New Roman</option>
            <option value="Georgia">Georgia</option>
            <option value="Courier New">Courier New</option>
            <option value="Verdana">Verdana</option>
            <option value="Helvetica">Helvetica</option>
            <option value="Kalpurush">Kalpurush</option>
            <option value="SolaimanLipi_20-04-07">SolaimanLipi</option>
            <option value="FN Himu Regular">Himu Regular</option>
            <option value="NotoSerifBengali-VariableFont_wdth,wght">NotoSerifBengali</option>
          </select>
        </div>
        {/* Font Size Selector */}
        <div className="flex flex-col">
          <label className="mb-1 dark:text-white">Font Size</label>
          <select
            value={fontSize}
            onChange={(e) => {
              setFontSize(e.target.value);
              execCommand('FontSize', e.target.value);
            }}
            className="rounded-md border-gray-300 shadow-sm text-sm p-2"
          >
            <option value="1">8pt</option>
            <option value="2">10pt</option>
            <option value="3">12pt</option>
            <option value="4">14pt</option>
            <option value="5">18pt</option>
            <option value="6">24pt</option>
            <option value="7">36pt</option>
          </select>
        </div>
      </div>

      {/* Text Color Selector */}
      <div className="flex flex-col">
        <label className="mb-1 dark:text-white">Text Color</label>
        <div className="relative inline-block">
          <button
            type="button"
            onClick={() => setShowColorPicker(!showColorPicker)}
            className="flex items-center p-2 border rounded-md shadow-sm bg-white dark:bg-gray-700"
          >
            <div
              className="w-5 h-5 rounded-full mr-2"
              style={{ backgroundColor: color }}
            />
            <span className="text-sm dark:text-white">{color}</span>
          </button>
          {showColorPicker && (
            <div className="absolute z-10 mt-2 p-2 bg-white border border-gray-200 shadow-md rounded-md">
              <div className="grid grid-cols-8 gap-2">
                {colorPalette.map((col) => (
                  <div
                    key={col}
                    onClick={() => handleColorChange(col)}
                    className={`w-6 h-6 rounded-full cursor-pointer relative ${
                      color === col ? 'ring-2 ring-offset-1 ring-black' : ''
                    }`}
                    style={{ backgroundColor: col }}
                  >
                    {color === col && (
                      <div className="absolute inset-0 flex items-center justify-center text-white text-xs font-bold">
                        ✓
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Text Formatting Buttons */}
      <div className="flex flex-wrap gap-2">
        <button onClick={() => execCommand('Bold')} className="p-2 bg-gray-200 rounded hover:bg-gray-300">
          <Bold size={16} />
        </button>
        <button onClick={() => execCommand('Italic')} className="p-2 bg-gray-200 rounded hover:bg-gray-300">
          <Italic size={16} />
        </button>
        <button onClick={() => execCommand('Underline')} className="p-2 bg-gray-200 rounded hover:bg-gray-300">
          <Underline size={16} />
        </button>
        <button onClick={() => execCommand('StrikeThrough')} className="p-2 bg-gray-200 rounded hover:bg-gray-300">
          <Strikethrough size={16} />
        </button>
        <button onClick={() => execCommand('JustifyLeft')} className="p-2 bg-gray-200 rounded hover:bg-gray-300">
          <AlignLeft size={16} />
        </button>
        <button onClick={() => execCommand('JustifyCenter')} className="p-2 bg-gray-200 rounded hover:bg-gray-300">
          <AlignCenter size={16} />
        </button>
        <button onClick={() => execCommand('JustifyRight')} className="p-2 bg-gray-200 rounded hover:bg-gray-300">
          <AlignRight size={16} />
        </button>
        <button onClick={() => execCommand('JustifyFull')} className="p-2 bg-gray-200 rounded hover:bg-gray-300">
          <AlignJustify size={16} />
        </button>
      </div>

      {/* List Formatting Buttons */}
      <div className="flex flex-wrap gap-2">
        <button onClick={() => execCommand('InsertUnorderedList')} className="p-2 bg-gray-200 rounded hover:bg-gray-300">
          <List size={16} />
        </button>
        <button onClick={() => execCommand('InsertOrderedList')} className="p-2 bg-gray-200 rounded hover:bg-gray-300">
          <ListOrdered size={16} />
        </button>
      </div>
    </div>
  );
};

export default TextProperties;
