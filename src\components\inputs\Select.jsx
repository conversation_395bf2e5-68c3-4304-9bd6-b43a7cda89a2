import { useField } from 'formik';
import ReactSelect from 'react-select';

export const Select = ({ label, options, isMulti, ...props }) => {
  const [field, meta, helpers] = useField(props);

  const customStyles = {
    control: (base) => ({
      ...base,
      backgroundColor: 'white',
      borderColor: '1px solid #D1D5DB',
      boxShadow: 'none',
      '&:hover': {
        borderColor: '#3B82F6'
      },
      '&.dark': {
        backgroundColor: '#374151',
        borderColor: '1px solid #4B5563',
        '&:hover': {
          borderColor: '#3B82F6'
        }
      }
    }),
    menu: (base) => ({
      ...base,
      backgroundColor: 'white',
      border: '1px solid #D1D5DB',
      zIndex: 9999, // Increase z-index to ensure dropdown is on top
      '&.dark': {
        backgroundColor: '#374151',
        border: '1px solid #4B5563'
      }
    }),
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isSelected 
        ? '#3B82F6' 
        : (state.isFocused ? '#F3F4F6' : 'transparent'),
      color: state.isSelected 
        ? 'white' 
        : '#1F2937',
      '&:hover': {
        backgroundColor: state.isSelected ? '#3B82F6' : '#F3F4F6'
      },
      '&.dark': {
        backgroundColor: state.isSelected 
          ? '#3B82F6' 
          : (state.isFocused ? '#4B5563' : 'transparent'),
        color: state.isSelected 
          ? 'white' 
          : '#F3F4F6',
        '&:hover': {
          backgroundColor: state.isSelected ? '#3B82F6' : '#4B5563'
        }
      }
    }),
    multiValue: (base) => ({
      ...base,
      backgroundColor: '#E5E7EB',
      '&.dark': {
        backgroundColor: '#4B5563'
      }
    }),
    multiValueLabel: (base) => ({
      ...base,
      color: '#1F2937',
      '&.dark': {
        color: '#F3F4F6'
      }
    }),
    multiValueRemove: (base) => ({
      ...base,
      color: '#4B5563',
      '&:hover': {
        backgroundColor: '#D1D5DB',
        color: '#1F2937'
      },
      '&.dark': {
        color: '#F3F4F6',
        '&:hover': {
          backgroundColor: '#6B7280',
          color: '#F3F4F6'
        }
      }
    }),
    input: (base) => ({
      ...base,
      color: '#1F2937',
      '&.dark': {
        color: '#F3F4F6'
      }
    }),
    singleValue: (base) => ({
      ...base,
      color: '#1F2937',
      '&.dark': {
        color: '#F3F4F6'
      }
    })
  };

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium mb-1 dark:text-gray-200">
        {label}
      </label>
      <ReactSelect
        options={options}
        isMulti={isMulti}
        value={
          isMulti 
            ? options.filter(option => field.value?.includes(option.value))
            : options.find(option => option.value === field.value)
        }
        onChange={(selected) => {
          if (isMulti) {
            helpers.setValue(selected?.map(item => item.value) || []);
          } else {
            helpers.setValue(selected?.value || '');
          }
        }}
        styles={customStyles}
        className="react-select-container dark"
        classNamePrefix="react-select"
        {...props}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-600 text-sm mt-1 dark:text-red-400">{meta.error}</div>
      ) : null}
    </div>
  );
};
