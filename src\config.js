// API and service URLs
export const BASE_URL = import.meta.env.VITE_HOST_URL || 'http://localhost:8000';
export const API_URL = `${BASE_URL}/api`;
export const BOOK_URL = import.meta.env.VITE_BOOK_URL;

// Authentication
export const COOKIE_DOMAIN = import.meta.env.VITE_COOKIE_URL || '.yourepub.com';
export const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID;
export const COOKIE_OPTIONS = {
  expires: 7, // 7 days
  path: '/',
  ...(COOKIE_DOMAIN && { domain: COOKIE_DOMAIN }),
  secure: import.meta.env.PROD, // Use Vite's built-in env var for production
  sameSite: 'lax'
};

// Application settings
export const APP_ENV = import.meta.env.MODE; // Vite provides this automatically
export const IS_PROD = import.meta.env.PROD; // Vite provides this automatically
export const IS_DEV = import.meta.env.DEV; // Vite provides this automatically


// Base redirect URL
export const REDIRECT_URL = import.meta.env.VITE_BASE_CONFIG_Redirect_URL;