import { useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { BookOpen, Video, BookCheck, Lightbulb, Users, Accessibility, ArrowRight, Mail } from 'lucide-react';
import Hero from './Hero';
import Books from './BookSection';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import api from '@/lib/axios';
import { useTranslation } from 'react-i18next';
import Chat from '@/components/Chat';

export default function Home() {
  const [darkMode, setDarkMode] = useState(false);
  const [isHovered, setIsHovered] = useState(null);
  const { t } = useTranslation();

  const features = [
    {
      icon: BookOpen,
      title: t('home.features.interactiveEbooks.title'),
      description: t('home.features.interactiveEbooks.description')
    },
    {
      icon: BookCheck,
      title: t('home.features.mcqAssessments.title'),
      description: t('home.features.mcqAssessments.description')
    },
    {
      icon: Lightbulb,
      title: t('home.features.flashcards.title'),
      description: t('home.features.flashcards.description')
    },
    {
      icon: Accessibility,
      title: t('home.features.accessibility.title'),
      description: t('home.features.accessibility.description')
    },
    {
      icon: Video,
      title: t('home.features.richMedia.title'),
      description: t('home.features.richMedia.description')
    },
    {
      icon: Users,
      title: t('home.features.publisherTools.title'),
      description: t('home.features.publisherTools.description')
    }
  ];

  const pricingPlans = [
    {
      name: t('home.pricing.basic.name'),
      price: t('home.pricing.basic.price'),
      description: t('home.pricing.basic.description'),
      features: [
        t('home.pricing.basic.features.ebooks'),
        t('home.pricing.basic.features.storage'),
        t('home.pricing.basic.features.support'),
        t('home.pricing.basic.features.analytics'),
      ],
      cta: t('home.pricing.basic.cta'),
      popular: false,
    },
    {
      name: t('home.pricing.pro.name'),
      price: t('home.pricing.pro.price'),
      description: t('home.pricing.pro.description'),
      features: [
        t('home.pricing.pro.features.unlimited'),
        t('home.pricing.pro.features.advanced'),
        t('home.pricing.pro.features.priority'),
        t('home.pricing.pro.features.customization'),
        t('home.pricing.pro.features.collaboration'),
      ],
      cta: t('home.pricing.pro.cta'),
      popular: true,
    },
    {
      name: t('home.pricing.enterprise.name'),
      price: t('home.pricing.enterprise.price'),
      description: t('home.pricing.enterprise.description'),
      features: [
        t('home.pricing.enterprise.features.custom'),
        t('home.pricing.enterprise.features.dedicated'),
        t('home.pricing.enterprise.features.sla'),
        t('home.pricing.enterprise.features.api'),
        t('home.pricing.enterprise.features.training'),
      ],
      cta: t('home.pricing.enterprise.cta'),
      popular: false,
    },
  ];

  const howItWorks = [
    {
      step: 1,
      title: t('home.howItWorks.step1.title'),
      description: t('home.howItWorks.step1.description'),
    },
    {
      step: 2,
      title: t('home.howItWorks.step2.title'),
      description: t('home.howItWorks.step2.description'),
    },
    {
      step: 3,
      title: t('home.howItWorks.step3.title'),
      description: t('home.howItWorks.step3.description'),
    },
    {
      step: 4,
      title: t('home.howItWorks.step4.title'),
      description: t('home.howItWorks.step4.description'),
    },
  ];

  const contactSchema = Yup.object().shape({
    subject: Yup.string().required('Required'),
    name: Yup.string().required('Required'),
    email: Yup.string().email('Invalid email').required('Required'),
    content: Yup.string().required('Required'),
  });

  const onSubmit = async (values, { resetForm, setSubmitting }) => {
    try {
      const response = await api.post("contact", values);
      if (response.status === 200) {
        resetForm();
      }
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900`}>
      <Chat />
      {/* Hero Section */}
      <Hero />
      <div id='books'>
        <Books />
      </div>
      {/* Features Section */}
      <div id="features" className="py-24 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
              {t('home.transformContent')}
            </h2>
            <p className="mt-4 text-gray-600 dark:text-gray-300">
              {t('home.engagingPublications')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                onMouseEnter={() => setIsHovered(index)}
                onMouseLeave={() => setIsHovered(null)}
                className="relative p-6 bg-gray-50 dark:bg-gray-900 rounded-xl hover:shadow-lg transition-all duration-300"
              >
                <div className="flex items-center justify-center w-12 h-12 mb-4 rounded-full bg-blue-100 dark:bg-blue-900">
                  <feature.icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {feature.description}
                </p>
                {isHovered === index && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="absolute bottom-4 right-4"
                  >
                    <ArrowRight className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </motion.div>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* How It Works Section */}
      <div id="how-it-works" className="py-24 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
              {t('home.howItWorksTitle')}
            </h2>
            <p className="mt-4 text-gray-600 dark:text-gray-300">
              {t('home.howItWorksDescription')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {howItWorks.map((item, index) => (
              <motion.div
                key={item.step}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="relative"
              >
                <div className="absolute top-0 left-0 -ml-4 mt-2 hidden lg:block z-[8]">
                  <span className="text-5xl font-bold text-blue-600/80 z-10">{item.step}</span>
                </div>
                <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm relative z-7">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    {item.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {item.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Pricing Section */}
      <div id="pricing" className="py-24 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-3xl font-bold text-gray-900 dark:text-white"
            >
              {t('home.pricing.title')}
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="mt-4 text-gray-600 dark:text-gray-300"
            >
              {t('home.pricing.subtitle')}
            </motion.p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {pricingPlans.map((plan, index) => (
              <motion.div
                key={plan.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className={`relative rounded-2xl bg-white dark:bg-gray-800 shadow-xl ${
                  plan.popular ? 'ring-2 ring-blue-500' : ''
                }`}
              >
                {plan.popular && (
                  <div className="absolute top-0 right-0 -mr-1 -mt-1 w-24 h-24 overflow-hidden">
                    <div className="absolute transform rotate-45 bg-blue-500 text-white text-xs font-semibold py-1 right-[-35px] top-[32px] w-[170px] text-center">
                      {t('home.pricing.popular')}
                    </div>
                  </div>
                )}
                <div className="p-8">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    {plan.name}
                  </h3>
                  <div className="flex items-baseline text-gray-900 dark:text-white">
                    <span className="text-4xl font-bold tracking-tight">
                      {plan.price}
                    </span>
                    {plan.price !== t('home.pricing.enterprise.price') && (
                      <span className="ml-1 text-xl font-semibold">/month</span>
                    )}
                  </div>
                  <p className="mt-4 text-gray-500 dark:text-gray-400">
                    {plan.description}
                  </p>
                  <ul className="mt-6 space-y-4">
                    {plan.features.map((feature) => (
                      <li key={feature} className="flex items-start">
                        <div className="flex-shrink-0">
                          <svg
                            className="h-6 w-6 text-green-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </div>
                        <p className="ml-3 text-gray-500 dark:text-gray-400">
                          {feature}
                        </p>
                      </li>
                    ))}
                  </ul>
                  <div className="mt-8">
                    <button
                      onClick={() => navigate('/signup')}
                      className={`w-full rounded-lg px-4 py-2 text-sm font-semibold ${
                        plan.popular
                          ? 'bg-blue-500 text-white hover:bg-blue-600'
                          : 'bg-gray-100 text-gray-900 hover:bg-gray-200 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600'
                      }`}
                    >
                      {plan.cta}
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Contact Section */}
      <div id="contact" className="py-24 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
              {t('home.getInTouch')}
            </h2>
            <div className="bg-gray-50 dark:bg-gray-900 p-8 rounded-xl shadow-sm">
              <Formik
                initialValues={{ subject: "", name: "", email: "", content: "" }}
                validationSchema={contactSchema}
                onSubmit={onSubmit}
              >
                {({ isSubmitting }) => (
                  <Form className="space-y-6">
                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                      <div>
                        <label htmlFor="subject" className="sr-only">Subject</label>
                        <Field
                          type="text"
                          id="subject"
                          name="subject"
                          placeholder="Subject"
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700"
                        />
                        <ErrorMessage name="subject" component="div" className="text-red-500" />
                      </div>
                      <div>
                        <label htmlFor="name" className="sr-only">Name</label>
                        <Field
                          type="text"
                          id="name"
                          name="name"
                          placeholder="Your Name"
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700"
                        />
                        <ErrorMessage name="name" component="div" className="text-red-500" />
                      </div>
                    </div>
                    <div>
                      <label htmlFor="email" className="sr-only">Email</label>
                      <Field
                        type="email"
                        id="email"
                        name="email"
                        placeholder="Your Email"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700"
                      />
                      <ErrorMessage name="email" component="div" className="text-red-500" />
                    </div>
                    <div>
                      <label htmlFor="content" className="sr-only">Content</label>
                      <Field
                        as="textarea"
                        id="content"
                        name="content"
                        placeholder="Content"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 h-32"
                      />
                      <ErrorMessage name="content" component="div" className="text-red-500" />
                    </div>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full px-6 py-3 text-white bg-blue-600 rounded-lg hover:bg-blue-700"
                    >
                      {t('home.sendMessage')}
                    </button>
                  </Form>
                )}
              </Formik>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-blue-600 dark:bg-blue-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              {t('home.readyToStart')}
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              {t('home.joinPublishers')}
            </p>
            <Link
              to="/login"
              className="inline-flex items-center px-8 py-3 text-base font-medium rounded-lg text-blue-600 bg-white hover:bg-gray-50 transition duration-150"
            >
              {t('home.startPublishing')}
              <ArrowRight className="ml-2 w-5 h-5" />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
