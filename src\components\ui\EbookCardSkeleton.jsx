import React from 'react';

const EbookCardSkeleton = () => {
  return (
    <div className="relative group bg-white rounded-lg shadow-md overflow-hidden">
      <div className="relative animate-pulse">
        <div className="w-full h-[250px] bg-gray-200" />
        <div className="absolute inset-0 bg-black bg-opacity-40 transition-opacity duration-200 flex items-start justify-end p-2">
          <div className="flex space-x-2">
            <button
              className="p-2 bg-white rounded-full hover:bg-gray-100 transition-colors duration-200"
              aria-label="View"
            >
              <div className="w-6 h-6 bg-gray-300 rounded-full" />
            </button>
            
          </div>
        </div>
      </div>
      <div className="p-4">
        <div className="w-3/4 h-4 bg-gray-300 rounded-full mb-2" />
        <div className="w-1/2 h-3 bg-gray-300 rounded-full" />
      </div>
    </div>
  );
};

export default EbookCardSkeleton;
