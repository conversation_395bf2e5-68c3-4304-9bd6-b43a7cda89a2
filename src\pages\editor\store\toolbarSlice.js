import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  selectedTab: 'Text',
  leftSidebarIsExpanded: true,
  rightSidebarIsExpanded: true,
  isPreviewActive: false
};

const toolbarSlice = createSlice({
  name: 'toolbar',
  initialState,
  reducers: {
    setSelectedTab(state, action) {
      state.selectedTab = action.payload;
    },
    toggleLeftSidebarIsExpanded(state) {
      state.leftSidebarIsExpanded = !state.leftSidebarIsExpanded;
    },
    toggleRightSidebarIsExpanded(state) {
      state.rightSidebarIsExpanded = !state.rightSidebarIsExpanded;
    },
    setIsPreviewActive(state, action) {
      state.isPreviewActive = action.payload;
    },
    setLeftSidebarIsExpanded(state, action) {
      state.leftSidebarIsExpanded = action.payload;
    }
  },
});

export const { setSelectedTab, toggleLeftSidebarIsExpanded, toggleRightSidebarIsExpanded, setIsPreviewActive, setLeftSidebarIsExpanded } = toolbarSlice.actions;

export default toolbarSlice.reducer;
