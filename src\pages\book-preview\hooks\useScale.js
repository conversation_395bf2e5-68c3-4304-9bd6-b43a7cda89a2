// src/hooks/useScale.jsx
import { useState, useEffect } from 'react';

export default function useScale(elementWidth, elementHeight) {
  const [scale, setScale] = useState(1);

  useEffect(() => {
    function updateScale() {
      if (!elementWidth || !elementHeight) return;
      const availableWidth = window.innerWidth - 20;
      const availableHeight = window.innerHeight - 90;
      const scaleX = availableWidth / elementWidth;
      const scaleY = availableHeight / elementHeight;
      setScale(Math.min(scaleX, scaleY));
    }

    updateScale();
    window.addEventListener('resize', updateScale);
    return () => window.removeEventListener('resize', updateScale);
  }, [elementWidth, elementHeight]);

  return scale;
}
