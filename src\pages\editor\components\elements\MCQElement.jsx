import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'sonner';
import { CheckCircle, XCircle } from 'lucide-react'; // Importing icons
import { useSelector } from 'react-redux';
import { updateElement } from '../../store/pagesSlice';

const MCQElement = ({ element }) => {
  const dispatch = useDispatch();
  const { id, content, style } = element;
  const { question, options, correctAnswer } = content;
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const currentReadingElementId = useSelector(state => state.ebook.currentReadingElementId);

  const handleAnswerChange = (index) => {
    if (selectedAnswer !== null) return; // Prevent re-selection

    setSelectedAnswer(index);

    if (index === correctAnswer) {
      toast.success('Correct answer!');
    } else {
      toast.error('Incorrect answer. Try again!');
    }

    dispatch(
      updateElement({
        id,
        updates: {
          content: {
            ...content,
            selectedAnswer: index,
          },
        },
      })
    );
  };

  return (
    <div
      style={{
        height: element.size.height,
        width: element.size.width,
        ...style,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
      }}
      className={`
        ${currentReadingElementId === element.id ? 'border border-blue-500 p-6 border' : ''}
`}

    >
      <div style={{ marginBottom: '1.5rem', width: '100%' }}>
        <p className="text-xl font-medium text-left">{question}</p>
      </div>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', width: '100%' }}>
        {options.map((option, index) => (
          <div
            key={index}
            onClick={() => handleAnswerChange(index)}
            style={{
              padding: '0.5rem 1.5rem',
              borderRadius: '0.5rem',
              cursor: selectedAnswer === null ? 'pointer' : 'not-allowed',
              backgroundColor:
                selectedAnswer === index
                  ? index === correctAnswer
                    ? '#C8E6C9' // Light green for correct
                    : '#FFCDD2' // Light red for incorrect
                  : '#E0E0E0', // Gray for unselected
              color: '#000000',
              // fontSize: '1rem',
              transition: 'background-color 0.3s ease',
              textAlign: 'left',
              maxWidth: '400px', // Set max width for options
              wordWrap: 'break-word',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
            }}
          >
            {selectedAnswer === index ? (
              index === correctAnswer ? (
                <CheckCircle size={20} color="#4CAF50" />
              ) : (
                <XCircle size={20} color="#F44336" />
              )
            ) : null}
            {option}
          </div>
        ))}
      </div>
    </div>
  );
};

export default MCQElement;
