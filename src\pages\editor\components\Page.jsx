import React, { useRef, useCallback, useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  setSelectedElement,
  addElement,
  addToSelection,
  setEditingElementId,
  updateElement,
  setCurrentPage,
} from "../store/pagesSlice";
import ResizableWrapper from "./ResizableWrapper";
import MCQElement from "./elements/MCQElement";
import TrueFalseElement from "./elements/TrueFalseElement";
import TextElement from "./elements/TextElement";
import VideoElement from "./elements/VideoElement";
import AudioElement from "./elements/AudioElement";
import useKeyboardEvents from "../hooks/useKeyboardEvents";
import Flashcard from "./elements/FlashcardElement";
import PageWithGuides from "./guides/PageGuides";
import ShapeElement from "./elements/ShapeElement";
import ElementIndex from "./elements/ElementIndex";

const Page = ({ page, index, height, width }) => {
  const { id, elements, style, layout } = page;
  const dispatch = useDispatch();
  const selectedElements = useSelector(
    (state) => state.pages.present.selectedElements
  );
  const editingElementId = useSelector(
    (state) => state.pages.present.editingElementId
  );
  const showGuide = useSelector((state) => state.pages.present.showGuide);
  const [isInView, setIsInView] = useState(false);
  const pageRef = useRef(null);
  const contentEditableRefs = useRef({});
  useKeyboardEvents(elements, selectedElements, editingElementId);

  // Observe visibility using Intersection Observer
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsInView(entry.isIntersecting);
        if (entry.isIntersecting) {
          console.log(`Current page in view: ${index}`);
        }
      },
      { threshold: 0.1 } // Adjust threshold as needed
    );

    if (pageRef.current) {
      observer.observe(pageRef.current);
    }

    return () => {
      if (pageRef.current) {
        observer.unobserve(pageRef.current);
      }
    };
  }, [index]);

  const handleElementClick = useCallback(
    (elementId, e) => {
      e.stopPropagation();
      if (e.shiftKey) {
        dispatch(setCurrentPage(index));
        dispatch(addToSelection(elementId));
      } else {
        dispatch(setCurrentPage(index));
        dispatch(setSelectedElement(elementId));
        const element = elements.find((el) => el.id === elementId);
        if (element && element.type === "text") {
          dispatch(setEditingElementId(elementId));
        }
      }
    },
    [dispatch, elements]
  );

  const handlePageClick = useCallback(() => {
    dispatch(setSelectedElement(null));
    dispatch(setEditingElementId(null));
  }, [dispatch]);

  const handleDrop = useCallback(
    (e) => {
      e.preventDefault();
      const type = e.dataTransfer.getData("type");
      const content = e.dataTransfer.getData("content");
      const style = e.dataTransfer.getData("style");
      const size = e.dataTransfer.getData("size");
      const pageRect = pageRef.current.getBoundingClientRect();
      const dropX = e.clientX - pageRect.left;
      const dropY = e.clientY - pageRect.top;

      const parsedSize = JSON.parse(size);
      const elementWidth = parsedSize.width;

      // Ensure the element does not go outside the page width
      const adjustedX = Math.max(0, Math.min(dropX, width - elementWidth));

      if (type) {
        if (type === "text" && editingElementId) {
          const element = elements.find((el) => el.id === editingElementId);
          if (element) {
            dispatch(
              updateElement({
                id: editingElementId,
                updates: {
                  content: element.content + content,
                  size: { ...element.size, height: element.size.height + 20 },
                },
              })
            );
          } else {
            dispatch(setEditingElementId(null));
            dispatch(
              addElement({
                type: type,
                content: content,
                position: { x: adjustedX, y: dropY },
                size: parsedSize,
              })
            );
          }
        } else if (type === "text" && !editingElementId) {
          dispatch(
            addElement({
              type: type,
              content: content,
              position: { x: adjustedX, y: dropY },
              size: parsedSize,
            })
          );
        } else {
          dispatch(
            addElement({
              type: type,
              content: JSON.parse(content),
              position: { x: adjustedX, y: dropY },
              style: JSON.parse(style),
              size: parsedSize,
            })
          );
        }
      }
    },
    [dispatch, elements, editingElementId, width]
  );

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
  }, []);

  const renderElement = (element) => {
    switch (element.type) {
      case "text":
        if (!contentEditableRefs.current[element.id]) {
          contentEditableRefs.current[element.id] = React.createRef();
        }
        return (
          <TextElement
            key={element.id}
            element={element}
            setEditingElementId={(id) => dispatch(setEditingElementId(id))}
            editingElementId={editingElementId}
          />
        );
      case "image":
        return (
          <img
            key={element.id}
            src={element.content}
            alt=""
            className="w-full h-full object-cover"
            style={element.style}
            draggable={false}
          />
        );
      case "mcq":
        return <MCQElement key={element.id} element={element} />;
      case "true-false":
        return <TrueFalseElement key={element.id} element={element} />;
      case "video":
        return <VideoElement key={element.id} element={element} />;
      case "flashcard":
        return <Flashcard key={element.id} element={element} />;
      case "index-list":
        return <ElementIndex key={element.id} element={element} />;
      case "shape":
        return <ShapeElement key={element.id} element={element} />;
      case "audio":
        return <AudioElement key={element.id} element={element} />;
      default:
        return null;
    }
  };

  if (!isInView) {
    return <div ref={pageRef} style={{ width, height }} />;
  }

  return (
    <div
      ref={pageRef}
      className="relative shadow-lg bg-white"
      style={{
        width: width,
        height: height,
        ...style,
        overflow: selectedElements.length ? "visible" : "hidden",
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
        backgroundPosition: style?.backgroundPosition || "center",
      }}
      onClick={handlePageClick}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
    >
      {elements.map((element) => (
        <ResizableWrapper
          key={element.id}
          id={element.id}
          style={element.style}
          className="cursor-pointer"
          onClick={(e) => handleElementClick(element.id, e)}
          position={element.position}
          size={element.size}
          pageWidth={width}
          rotation={element.rotation}
          type={element.type}
        >
          {renderElement(element)}
        </ResizableWrapper>
      ))}
      {showGuide && (
        <PageWithGuides
          pageWidth={width}
          pageHeight={height}
          margin={style?.margins}
          gutter={layout?.gutter}
          numColumns={layout?.columns}
        />
      )}
      {index !== 0 && (
        <div className="rounded-full border w-6 h-6 flex items-center justify-center px-2 py-1 absolute bottom-4 right-4 text-gray-500 text-sm">
          {index}
        </div>
      )}
    </div>
  );
};

export default Page;
