import React, { useEffect, useState, useRef } from 'react';

const ScaleTest = () => {
  const [scale, setScale] = useState(1);
  const containerRef = useRef(null);
  const [dimensions, setDimensions] = useState({ width: 800, height: 1000 });
  
  // Test pages with different colors and text
  const testPages = [
    { color: '#FFE4E1', text: 'Page 1' },
    // { color: '#E0FFFF', text: 'Page 2' },
    // { color: '#F0FFF0', text: 'Page 3' },
    // { color: '#FAF0E6', text: 'Page 4' },
  ];

  const calculateScale = () => {
    if (!containerRef.current) return;

    const margin = 40; // pixels of margin
    const availableWidth = window.innerWidth - margin;
    const availableHeight = window.innerHeight - margin;

    // Calculate scale based on available space
    const widthScale = availableWidth / dimensions.width;
    const heightScale = availableHeight / dimensions.height;
    
    // Use the smaller scale to ensure content fits both dimensions
    const newScale = Math.min(widthScale, heightScale, 1);
    
    console.log('Scaling Calculation:', {
      availableWidth,
      availableHeight,
      widthScale,
      heightScale,
      finalScale: newScale
    });

    setScale(newScale);
  };

  useEffect(() => {
    calculateScale();
    window.addEventListener('resize', calculateScale);
    
    return () => window.removeEventListener('resize', calculateScale);
  }, []);

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-5">
      <div ref={containerRef} className="relative">
        {/* Scaling container */}
        <div
          style={{
            transform: `scale(${scale})`,
            transformOrigin: 'top center',
          }}
          className="flex flex-col items-center gap-4"
        >
          {/* Test pages */}
          {testPages.map((page, index) => (
            <div
              key={index}
              style={{
                width: `${dimensions.width}px`,
                height: `${dimensions.height}px`,
                backgroundColor: page.color,
              }}
              className="relative shadow-lg rounded-lg overflow-hidden"
            >
              {/* Content */}
              <div className="absolute inset-0 flex flex-col items-center justify-center">
                <h1 className="text-4xl font-bold text-gray-700">{page.text}</h1>
                <div className="mt-4 text-lg">
                  <p>Current Scale: {scale.toFixed(3)}</p>
                  <p>Original Size: {dimensions.width} x {dimensions.height}</p>
                  <p>Scaled Size: {Math.round(dimensions.width * scale)} x {Math.round(dimensions.height * scale)}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Scale Controls */}
        {/* <div className="fixed bottom-4 right-4 bg-white p-4 rounded-lg shadow-lg">
          <div className="flex items-center gap-4">
            <button
              onClick={() => setScale(prev => Math.max(0.1, prev - 0.1))}
              className="px-4 py-2 bg-blue-500 text-white rounded"
            >
              Zoom Out
            </button>
            <span className="text-lg font-medium">
              {(scale * 100).toFixed(0)}%
            </span>
            <button
              onClick={() => setScale(prev => Math.min(2, prev + 0.1))}
              className="px-4 py-2 bg-blue-500 text-white rounded"
            >
              Zoom In
            </button>
          </div>
        </div> */}
      </div>
    </div>
  );
};

export default ScaleTest;