import React from "react";
import { useSelector } from "react-redux";

const AudioElement = ({ element, miniView }) => {
  const { content } = element;
  const { audioUrl } = content;

  const currentReadingElementId = useSelector(
    (state) => state.ebook.currentReadingElementId
  );

  return (
    <div
      style={{ width: "100%", height: "100%" }}
      className={`
      ${
        currentReadingElementId === element.id
          ? "border border-blue-500 p-6 border"
          : "p-6 border"
      }
`}
    >
      <audio controls style={{ width: "100%" }} muted={miniView}>
        <source src={audioUrl} type="audio/mpeg" />
        Your browser does not support the audio element.
      </audio>
    </div>
  );
};

export default AudioElement;
