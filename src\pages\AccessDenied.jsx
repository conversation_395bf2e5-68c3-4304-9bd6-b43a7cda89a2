import React from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

const AccessDenied = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  return (
    <div className="flex flex-col items-center justify-center h-screen bg-gray-100 text-center p-6">
      <h1 className="text-6xl font-bold text-red-600">403</h1>
      <h2 className="text-2xl font-semibold text-gray-800 mt-4">{t("accessDenied.title")}</h2>
      <p className="text-gray-600 mt-2">
        {t("accessDenied.message")}
      </p>
      <button
        onClick={() => navigate("/")}
        className="mt-6 px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg shadow-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        {t("accessDenied.goHome")}
      </button>
    </div>
  );
};

export default AccessDenied;
