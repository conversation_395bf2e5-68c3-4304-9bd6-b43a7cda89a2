// Pagination.jsx
import React from 'react';
import { Icon } from '@iconify/react';

// Accept isMobile as a prop (for Step 3 fix)
const Pagination = ({ pageIndex, totalPages, onPrevious, onNext, isMobile }) => {
  // Determine the correct last page index based on view mode
  const lastPageIndex = isMobile ? totalPages - 1 : totalPages - 2;
  // Ensure totalPages > 0 to avoid negative lastPageIndex
  const effectiveLastIndex = totalPages > 0 ? Math.max(0, lastPageIndex) : 0;

  return (
    <>
      <button
        // Add pointer-events-auto
        className={`absolute p-2 rounded-full flex items-center justify-center pointer-events-auto ${pageIndex === 0 ? 'bg-gray-300 cursor-not-allowed' : 'bg-gray-800 text-white hover:bg-gray-600'}`}
        onClick={onPrevious}
        disabled={pageIndex === 0}
        style={{ top: '50%', left: '20px', transform: 'translateY(-50%)' }}
        aria-label="Previous Page" // Accessibility improvement
      >
        <Icon icon="mdi:chevron-left" width="26" height="26" />
      </button>

      <button
        // Add pointer-events-auto
        // Use effectiveLastIndex for disable check
        className={`absolute p-2 rounded-full flex items-center justify-center pointer-events-auto ${pageIndex >= effectiveLastIndex ? 'bg-gray-300 cursor-not-allowed' : 'bg-gray-800 text-white hover:bg-gray-600'}`}
        onClick={onNext}
        disabled={pageIndex >= effectiveLastIndex}
        style={{ top: '50%', right: '20px', transform: 'translateY(-50%)' }}
        aria-label="Next Page" // Accessibility improvement
      >
        <Icon icon="mdi:chevron-right" width="26" height="26" />
      </button>
    </>
  );
};

export default Pagination;