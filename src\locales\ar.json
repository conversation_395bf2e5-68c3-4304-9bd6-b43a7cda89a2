{"trusted_by": "موثوق به من قبل أكثر من 1000 معلم", "future_of": "مستقبل", "digital_education": "التعليم الرقمي", "create_stunning_ebooks": "أنشئ كتبًا إلكترونية تفاعلية مذهلة تغير طريقة تعلم الطلاب وتفاعلهم واحتفاظهم بالمعلومات.", "try_ebook_builder": "جرب أداة إنشاء الكتب الإلكترونية", "read_books": "اقر<PERSON> الكتب", "books_created": "تم إنشاء أكثر من 10 آلاف كتاب", "coppa_compliant": "متوافق مع قانون حماية خصوصية الأطفال على الإنترنت (COPPA)", "interactive_learning_platform": "منصة تعليم تفاعلية", "interactive_content": "محتوى تفاعلي", "interactive_content_desc": "تفاعل مع عناصر الوسائط المتعددة", "smart_assessment": "تقييم ذكي", "smart_assessment_desc": "تتبع تقدم التعلم", "features": "الميزات", "how_it_works": "<PERSON>ي<PERSON> تعمل", "books": "الكتب", "contact": "اتصل", "bookSection.exploreLibrary": "استكشف مكتبتنا الرقمية", "bookSection.browseDescription": "تصفح مجموعتنا من الكتب الإلكترونية التفاعلية واكتشف معرفة جديدة", "bookSection.error": "حدث خطأ ما. يرجى المحاولة مرة أخرى لاحقًا.", "bookSection.showMore": "أ<PERSON>ه<PERSON> المزيد", "home.transformContent": "حوّل المحتوى الخاص بك", "home.engagingPublications": "كل ما تحتاجه لإنشاء منشورات رقمية جذابة", "home.howItWorksTitle": "<PERSON>ي<PERSON> تعمل", "home.howItWorksDescription": "قم بإنشاء كتابك الإلكتروني التفاعلي ونشره في أربع خطوات بسيطة", "home.getInTouch": "ا<PERSON><PERSON>ى على اتصال", "home.readyToStart": "هل أنت مستعد للبدء في الإنشاء؟", "home.joinPublishers": "انضم إلى الآلاف من الناشرين الذين يقومون بإنشاء محتوى تفاعلي", "home.startPublishing": "ابد<PERSON> النشر", "home.features.interactiveEbooks.title": "الكتب الإلكترونية التفاعلية", "home.features.interactiveEbooks.description": "أنشئ كتبًا رقمية جذابة مع عناصر تفاعلية ومحتوى وسائط متعددة", "home.features.mcqAssessments.title": "أسئلة الاختيار من متعدد والتقييمات", "home.features.mcqAssessments.description": "قم بإنشاء اختبارات وتقييمات شاملة لتحسين نتائج التعلم", "home.features.flashcards.title": "بطاقات تعليمية", "home.features.flashcards.description": "صمم بطاقات تعليمية ديناميكية للحفظ والمراجعة الفعالين", "home.features.accessibility.title": "إمكانية الوصول", "home.features.accessibility.description": "تأكد من وصول المحتوى الخاص بك إلى الجميع من خلال ميزات إمكانية الوصول المضمنة", "home.features.richMedia.title": "الوسائط الغنية", "home.features.richMedia.description": "قم بتضمين مقاطع الفيديو والوسائط التفاعلية لإنشاء تجارب تعليمية غامرة", "home.features.publisherTools.title": "أدوات الناشر", "home.features.publisherTools.description": "أدوات نشر احترافية لتوزيع المحتوى وإدارته", "home.howItWorks.step1.title": "أنشئ المحتوى الخاص بك", "home.howItWorks.step1.description": "ابدأ بالمحتوى الحالي لديك أو أنشئ مواد جديدة باستخدام محررنا البديهي", "home.howItWorks.step2.title": "أض<PERSON> التفاعل", "home.howItWorks.step2.description": "عزز المحتوى الخاص بك بالاختبارات والبطاقات التعليمية وعناصر الوسائط المتعددة", "home.howItWorks.step3.title": "خصص التصميد", "home.howItWorks.step3.description": "اختر من بين القوالب الجميلة أو أنشئ تصميمك المخصص", "home.howItWorks.step4.title": "انشر وشارك", "home.howItWorks.step4.description": "انشر كتابك الإلكتروني التفاعلي وشاركه مع جمهورك", "home.sendMessage": "أرسل رسالة", "footer.product": "المنتج", "footer.features": "الميزات", "footer.howItWorks": "<PERSON>ي<PERSON> تعمل", "footer.pricing": "التسعير", "footer.faq": "الأسئلة الشائعة", "footer.company": "الشركة", "footer.about": "عن", "footer.blog": "المدونة", "footer.careers": "الوظائف", "footer.contact": "اتصل", "footer.resources": "الموارد", "footer.documentation": "الوثائق", "footer.templates": "القوالب", "footer.examples": "الأمثلة", "footer.support": "الدعم", "footer.legal": "قانوني", "footer.privacy": "الخصوصية", "footer.terms": "الشروط", "footer.security": "الأمان", "footer.allRightsReserved": "جميع الحقوق محفوظة.", "authorForm.name": "اسم", "authorForm.nameRequired": "اسم مطلوب", "authorForm.status": "الحالة", "authorForm.statusRequired": "الحالة مطلوبة", "authorForm.active": "نشط", "authorForm.inactive": "غير نشط", "authorForm.uploadPhoto": "تحميل الصورة", "authorForm.unsupportedFileFormatOrSize": "تنسيق أو حجم ملف غير مدعوم", "authorForm.photoRequired": "الصورة مطلوبة", "authorForm.unsupportedFileFormat": "تنسيق ملف غير مدعوم", "authorForm.fileSizeTooLarge": "حجم المل<PERSON> كبير جدًا", "authorForm.bio": "سيرة ذاتية", "authorForm.bioRequired": "السيرة الذاتية مطلوبة", "authorForm.updateAuthor": "تحديث المؤلف", "authorForm.addAuthor": "إضافة مؤلف", "authors.authorsList": "قائمة المؤلفين", "authors.name": "اسم", "authors.photo": "صورة", "authors.status": "الحالة", "authors.active": "نشط", "authors.inactive": "غير نشط", "authors.action": "<PERSON><PERSON><PERSON>", "authors.addAuthor": "إضافة مؤلف", "authors.editAuthor": "تحرير المؤلف", "inquiries.title": "الاستفسارات", "inquiries.name": "الاسم", "inquiries.email": "الب<PERSON>يد الإلكتروني", "inquiries.subject": "الموضوع", "inquiries.status": "الحالة", "inquiries.createdAt": "تاريخ الإنشاء", "inquiries.action": "الإجراء", "inquiries.modalTitle": "تفاصيل الاستفسار", "genreForm.name": "الاسم", "genreForm.status": "الحالة", "genreForm.active": "نشط", "genreForm.inactive": "غير نشط", "genreForm.uploadThumbnail": "تحميل الصورة المصغرة", "genreForm.addGenre": "إضافة فئة", "genreForm.updateGenre": "تحديث النوع", "genreForm.validation.nameRequired": "الاسم مطلوب", "genreForm.validation.statusRequired": "الحالة مطلوبة", "genreForm.validation.thumbnailRequired": "الصورة المصغرة مطلوبة", "genreForm.validation.unsupportedFormat": "تنسيق الملف غير مدعوم", "genreForm.validation.fileTooLarge": "حجم المل<PERSON> كبير جدًا", "genreForm.validation.fileInvalid": "تنسيق ملف غير صالح أو حجم غير صحيح", "genresPage.title": "قائمة الأنواع", "genresPage.name": "الاسم", "genresPage.image": "الصورة", "genresPage.status": "الحالة", "genresPage.active": "نشط", "genresPage.inactive": "غير نشط", "genresPage.action": "الإجراء", "genresPage.addGenre": "إضافة نوع", "genresPage.editGenre": "تحرير النوع", "genresPage.submitError": "خطأ في إرسال النموذج", "genresPage.deleteError": "خطأ في حذف النوع", "userForm.name": "الاسم", "userForm.email": "الب<PERSON>يد الإلكتروني", "userForm.password": "كلمة المرور", "userForm.role": "الدور", "userForm.isActive": "نشط؟", "userForm.profilePicture": "صورة الملف الشخصي", "userForm.active": "نشط", "userForm.inactive": "غير نشط", "userForm.admin": "المسؤول", "userForm.author": "المؤلف", "userForm.user": "المستخدم", "userForm.createUser": "إنشاء مستخدم", "userForm.updateUser": "تحديث المستخدم", "userForm.validation.nameRequired": "الاسم مطلوب", "userForm.validation.emailRequired": "البريد الإلكتروني مطلوب", "userForm.validation.invalidEmail": "عنوان بريد إلكتروني غير صالح", "userForm.validation.passwordRequired": "كلمة المرور مطلوبة", "userForm.validation.passwordMin": "يجب أن تتكون كلمة المرور من 6 أحرف على الأقل", "userForm.validation.roleRequired": "الدور مطلوب", "userForm.submitError": "خطأ في إرسال النموذج", "usersList.title": "قائمة المستخدمين", "usersList.name": "الاسم", "usersList.email": "الب<PERSON>يد الإلكتروني", "usersList.role": "الدور", "usersList.status": "الحالة", "usersList.active": "نشط", "usersList.inactive": "غير نشط", "usersList.action": "الإجراء", "usersList.addUser": "إضافة مستخدم", "usersList.editUser": "تحرير المستخدم", "usersList.deleteError": "خطأ في حذف المستخدم", "userView.loading": "جار التحميل...", "userView.user": "المستخدم", "userView.backToList": "العودة إلى قائمة المستخدمين", "userView.name": "الاسم", "userView.email": "الب<PERSON>يد الإلكتروني", "userView.role": "الدور", "userView.profilePicture": "صورة الملف الشخصي", "userView.status": "الحالة", "userView.active": "نشط", "userView.inactive": "غير نشط", "accessDenied.title": "تم رفض الوصول", "accessDenied.message": "ليس لديك إذن للوصول إلى هذه الصفحة.", "accessDenied.goHome": "الذها<PERSON> إلى الصفحة الرئيسية", "profile.title": "إعدادات الملف الشخصي", "profile.profilePicture": "صورة الملف الشخصي", "profile.uploadProfilePicture": "تحميل صورة الملف الشخصي", "profile.fullName": "الاسم الكامل", "profile.currentPassword": "كلمة المرور الحالية", "profile.newPassword": "كلمة المرور الجديدة", "profile.confirmNewPassword": "تأكيد كلمة المرور الجديدة", "profile.role": "الدور", "profile.saving": "جارٍ الحفظ...", "profile.saveChanges": "حفظ التغييرات", "profile.updateSuccess": "تم تحديث الملف الشخصي بنجاح!", "profile.updateFail": "فشل تحديث الملف الشخصي. الرجاء المحاولة مرة أخرى.", "profile.updateError": "حد<PERSON> خطأ أثناء تحديث الملف الشخصي", "profile.validation.nameRequired": "الاسم الكامل مطلوب", "profile.validation.passwordMin": "يجب أن تتكون كلمة المرور من 6 أحرف على الأقل", "profile.validation.newPasswordRequired": "كلمة المرور الجديدة مطلوبة", "profile.validation.confirmPasswordRequired": "تأكيد كلمة المرور مطلوب", "profile.validation.passwordMismatch": "كلمتا المرور غير متطابقتين", "security.title": "إعدادات الأمان", "security.changePassword": "تغيير كلمة المرور", "security.currentPassword": "كلمة المرور الحالية", "security.newPassword": "كلمة المرور الجديدة", "security.confirmNewPassword": "تأكيد كلمة المرور الجديدة", "security.updating": "جارٍ التحديث...", "security.updatePassword": "تحديث كلمة المرور", "security.updateSuccess": "تم تحديث كلمة المرور بنجاح!", "security.updateError": "خطأ في تحديث كلمة المرور", "security.validation.required": "مطلوب", "security.validation.passwordMin": "يجب أن تحتوي كلمة المرور على 8 أحرف على الأقل", "security.validation.passwordMismatch": "كلمتا المرور غير متطابقتين", "dashboard.title": "نظرة عامة على لوحة التحكم", "dashboard.totalUnpublishedBooks": "إجمالي الكتب غير المنشورة", "dashboard.totalPublishedBooks": "إجمالي الكتب المنشورة", "dashboard.totalEbooks": "إجمالي الكتب الإلكترونية", "dashboard.totalGenres": "إجمالي الأنواع", "dashboard.recentEbooks": "الكتب الإلكترونية الحديثة", "dashboard.pendingEbooks": "الكتب الإلكترونية المعلقة", "dashboard.titleColumn": "العنوان", "dashboard.authorColumn": "المؤلف", "dashboard.genreColumn": "النوع", "dashboard.loading": "جارٍ التحميل...", "dashboard.noData": "لا توجد بيانات متاحة", "Dashboard": "لوحة القيادة", "eBook": "كتاب إلكتروني", "Manage eBooks": "إدارة الكتب الإلكترونية", "Templates": "قوالب", "Categories": "الفئات", "Coupons": "كوبونات", "Publishers": "الناشرون", "Authors": "المؤلفين", "Users": "المستخدمين", "Inquiries": "الاستفسارات", "Orders": "الطلبات", "Settings": "الإعدادات", "Profile": "الملف الشخصي", "Static Pages": "الصفحات الثابتة", "Security": "الأمان", "Logout": "تسجيل الخروج", "home.pricing.title": "تسعير بسيط وشفاف", "home.pricing.subtitle": "اختر الخطة المثالية لاحتياجات النشر الخاصة بك", "home.pricing.popular": "الأكثر شعبية", "home.pricing.basic.name": "أساسي", "home.pricing.basic.price": "١٠٩ ريال", "home.pricing.basic.description": "مثالي للبدء مع الكتب الإلكترونية التفاعلية", "home.pricing.basic.features.ebooks": "حتى ٥ كتب إلكترونية", "home.pricing.basic.features.storage": "٥ جيجابايت تخزين", "home.pricing.basic.features.support": "دعم بالبريد الإلكتروني", "home.pricing.basic.features.analytics": "تحليلات أساسية", "home.pricing.basic.cta": "اب<PERSON><PERSON> الآن", "home.pricing.pro.name": "احترافي", "home.pricing.pro.price": "٣٢٩ ريال", "home.pricing.pro.description": "كل ما تحتاجه للنشر الاحترافي", "home.pricing.pro.features.unlimited": "كتب إلكترونية غير محدودة", "home.pricing.pro.features.advanced": "ميزات تفاعلية متقدمة", "home.pricing.pro.features.priority": "دعم ذو أولوية", "home.pricing.pro.features.customization": "تخصيص العلامة التجارية", "home.pricing.pro.features.collaboration": "تعاون الفريق", "home.pricing.pro.cta": "ابدأ النسخة التجريبية المجانية", "home.pricing.enterprise.name": "مؤسسات", "home.pricing.enterprise.price": "مخصص", "home.pricing.enterprise.description": "حلول مخصصة للمؤسسات الكبيرة", "home.pricing.enterprise.features.custom": "حلول مخصصة", "home.pricing.enterprise.features.dedicated": "دعم مخصص", "home.pricing.enterprise.features.sla": "ضمان مستوى الخدمة", "home.pricing.enterprise.features.api": "الوصول إلى API", "home.pricing.enterprise.features.training": "تدريب الفريق", "home.pricing.enterprise.cta": "تواصل مع المبيعات", "pricing": "التسعير", "couponsPage.title": "قائمة الكوبونات", "couponsPage.code": "الر<PERSON>ز", "couponsPage.discountType": "نوع الخصم", "couponsPage.appliesTo": "ينط<PERSON><PERSON> على", "couponsPage.validPeriod": "فترة الصلاحية", "couponsPage.status": "الحالة", "couponsPage.action": "الإجراء", "couponsPage.active": "نشط", "couponsPage.inactive": "غير نشط", "couponsPage.addCoupon": "إضافة كوبون", "couponsPage.editCoupon": "تعديل الكوبون", "couponsPage.item": "العنصر", "couponsPage.submitError": "خطأ في إرسال الكوبون", "couponsPage.deleteError": "خطأ في حذف الكوبون", "couponForm.code": "<PERSON><PERSON>ز الكوبون", "couponForm.discountType": "نوع الخصم", "couponForm.percentage": "نسبة مئوية", "couponForm.fixed": "مب<PERSON>غ ثابت", "couponForm.discountValue": "قيمة الخصم", "couponForm.appliesTo": "ينط<PERSON><PERSON> على", "couponForm.entireOrder": "الطلب بأكمله", "couponForm.specificItem": "عنصر محدد", "couponForm.itemId": "معر<PERSON> العنصر", "couponForm.minOrderAmount": "ال<PERSON><PERSON> الأ<PERSON>نى لمبلغ الطلب", "couponForm.maxDiscount": "الح<PERSON> الأقصى للخصم", "couponForm.usageLimit": "ح<PERSON> الاستخدام", "couponForm.startDate": "تاريخ البدء", "couponForm.endDate": "تاريخ الانتهاء", "couponForm.status": "الحالة", "couponForm.active": "نشط", "couponForm.inactive": "غير نشط", "couponForm.addCoupon": "إضافة كوبون", "couponForm.updateCoupon": "تحديث الكوبون", "couponForm.validation.codeRequired": "رمز الكوبون مطلوب", "couponForm.validation.invalidDiscountType": "نوع الخصم غير صالح", "couponForm.validation.discountTypeRequired": "نوع الخصم مطلوب", "couponForm.validation.discountValueRequired": "قيمة الخصم مطلوبة", "couponForm.validation.minDiscount": "يجب أن يكون الخصم أكبر من أو يساوي 0", "couponForm.validation.appliesToRequired": "حقل 'ينطبق على' مطلوب", "couponForm.validation.invalidAppliesTo": "قيمة 'ينطبق على' غير صالحة", "couponForm.validation.itemIdRequired": "معرف العنصر مطلوب للكوبونات الخاصة بعنصر محدد", "couponForm.validation.minOrderAmount": "يجب أن يكون الحد الأدنى لمبلغ الطلب أكبر من أو يساوي 0", "couponForm.validation.minOrderAmountRequired": "ال<PERSON><PERSON> الأ<PERSON>نى لمبلغ الطلب مطلوب", "couponForm.validation.minMaxDiscount": "يجب أن يكون الحد الأقصى للخصم أكبر من أو يساوي 0", "couponForm.validation.maxDiscountRequired": "الح<PERSON> الأقصى للخصم مطلوب", "couponForm.validation.minUsageLimit": "يجب أن يكون حد الاستخدام أكبر من أو يساوي 0", "couponForm.validation.usageLimitRequired": "حد الاستخ<PERSON><PERSON>م مطلوب", "couponForm.validation.startDateRequired": "تاريخ البدء مطلوب", "couponForm.validation.endDateRequired": "تاريخ الانتهاء مطلوب", "couponForm.validation.endDateAfterStart": "يجب أن يكون تاريخ الانتهاء بعد تاريخ البدء", "couponForm.validation.invalidStatus": "الحالة غير صالحة", "couponForm.validation.statusRequired": "الحالة مطلوبة", "publishers.name": "الاسم", "publishers.mobile": "الجوال", "publishers.email": "الب<PERSON>يد الإلكتروني", "publishers.tradeLicense": "رقم الترخيص التجاري", "publishers.status": "الحالة", "publishers.active": "نشط", "publishers.inactive": "غير نشط", "publishers.action": "إجراء", "publishers.editPublisher": "تعديل الناشر", "publishers.addPublisher": "إضافة ناشر", "publishers.deleteError": "فشل في حذف النا<PERSON>ر.", "publishers.submitError": "فشل في إرسال النموذج.", "publishers.title": "قائمة الناشرين", "publishers.form.name": "الاسم", "publishers.form.mobile": "الهات<PERSON> المحمول", "publishers.form.nid": "رقم الهوية الوطنية", "publishers.form.nidImage": "صورة رقم الهوية الوطنية", "publishers.form.proprietorPhoto": "صورة المالك", "publishers.form.proprietorSignature": "توقيع المالك", "publishers.form.presentAddress": "العنوان الحالي", "publishers.form.permanentAddress": "العنوان الدائم", "publishers.form.officePresentAddress": "عنوان المكتب الحالي", "publishers.form.tradeLicenseNumber": "رقم الرخصة التجارية", "publishers.form.tradeLicenseImage": "صورة الرخصة التجارية", "publishers.form.logo": "شعار", "publishers.form.etin": "رقم التعريف الضريبي الإلكتروني", "publishers.form.vatNumber": "رقم ضريبة القيمة المضافة", "publishers.form.officeMobile": "الهات<PERSON> المحمول للمكتب", "publishers.form.officePhone": "<PERSON><PERSON><PERSON><PERSON> الم<PERSON>تب", "publishers.form.officeEmail": "البريد الإلكتروني للمكتب", "publishers.form.officePermanentAddress": "العنوان الدائم للمكتب", "publishers.form.cancel": "إلغاء", "publishers.form.add": "إضافة", "publishers.form.edit": "تعديل", "publishers.form.email": "الب<PERSON>يد الإلكتروني", "publishers.form.password": "كلمة المرور", "publishers.form.confirmPassword": "تأكيد كلمة المرور", "publishers.form.publisherLogin": "تسجيل دخول الناشر", "publishers.form.passwordMin": "يجب أن تكون كلمة المرور 8 أحرف على الأقل", "publishers.form.passwordRequired": "كلمة المرور مطلوبة", "publishers.form.confirmPasswordRequired": "تأكيد كلمة المرور مطلوب", "publishers.form.passwordMismatch": "كلمتا المرور غير متطابقتين", "category.list": "قائمة الفئات", "static.page": "قائمة الصفحات الثابتة", "static.addStaticPage": "إضافة صفحة ثابتة", "title": "عنوان", "staticForm.content": "مح<PERSON><PERSON><PERSON>", "staticForm.slug": "Slug", "static.editStatic": "تحديث الصفحة الثابتة", "update": "تحديث", "pending": "قيد الانتظار", "approved": "تمت الموافقة", "rejected": "مرفو<PERSON>", "publishers.form.emailRequired": "البريد الإلكتروني مطلوب", "publishers.form.nameRequired": "اسم الناشر مطلوب", "publishers.form.invalidEmail": "البريد الإلكتروني غير صالح", "Search eBooks": "البحث عن كتب إلكترونية", "Author": "المؤلف", "Publisher": "الناشر", "Book Type": "نوع الكتاب", "Status": "الحالة", "Approval Status": "حالة الموافقة", "ebook.title": "الكتب الإلكترونية", "ebook.modal.title.view": "معلومات الكتاب الأساسية", "ebook.modal.title.edit": "تعديل معلومات الكتاب الأساسية", "ebook.modal.title.create": "إنشاء كتاب جديد", "ebook.button.tooltip.view": "عرض الكتاب", "ebook.button.tooltip.viewInfo": "عرض المعلومات الأساسية", "ebook.button.tooltip.editInfo": "تعديل المعلومات الأساسية", "ebook.button.tooltip.design": "تصميم بالمحرر", "ebook.button.tooltip.template": "<PERSON><PERSON><PERSON> كقالب", "ebook.button.tooltip.delete": "<PERSON><PERSON><PERSON> ال<PERSON><PERSON>اب", "ebook.delete.confirm.title": "هل أنت متأكد من حذف هذا الكتاب الإلكتروني؟", "ebook.delete.confirm.text": "هذا الكتاب الإلكتروني", "ebook.delete.confirm.description": "سيتم حذفه نهائ<|im_start|>.", "ebook.delete.confirm.cancel": "إلغاء", "ebook.delete.confirm.confirm": "<PERSON><PERSON><PERSON>", "ebook.modal.editTitle": "تعديل معلومات الكتاب الأساسية", "ebook.modal.createTitle": "إنشاء كتاب جديد", "ebook.form.fields.title": "العنوان", "ebook.form.fields.authors": "اختر المؤلفين", "ebook.form.fields.description": "الوصف", "ebook.form.fields.categories": "اختر الفئات", "ebook.form.fields.status": "الحالة", "ebook.form.fields.status.options.draft": "مسودة", "ebook.form.fields.status.options.published": "منشور", "ebook.form.fields.bookType": "نوع الكتاب", "ebook.form.fields.bookType.options.ebook": "كتاب إلكتروني", "ebook.form.fields.bookType.options.pdf": "PDF", "ebook.form.fields.pageDimensions": "أ<PERSON><PERSON><PERSON> الصفحة", "ebook.form.fields.orientation": "اتجاه الصفحة", "ebook.form.fields.orientation.options.portrait": "عمودي", "ebook.form.fields.orientation.options.landscape": "<PERSON><PERSON><PERSON>ي", "ebook.form.fields.pdfFile": "ملف PDF", "ebook.form.fields.coverImage": "صورة الغلاف", "ebook.form.fields.buttons.update": "تحديث الكتاب الإلكتروني", "ebook.form.fields.buttons.submit": "إرسال الكتاب الإلكتروني", "ebook.form.fields.price": "السعر", "ebook.form.fields.discount": "الخصم", "ebook.form.fields.finalPrice": "السعر النهائي", "ebook.form.fields.pageDimension": "أ<PERSON><PERSON><PERSON> الصفحة", "fileInput.dragActive": "...أفلت الملف هنا", "fileInput.dragInactive": "اسحب وأفلت أو انقر للاختيار", "ebook.button.create": "إنشاء كتاب"}