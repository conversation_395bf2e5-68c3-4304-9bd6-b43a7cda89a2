import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Modal from '@/components/ui/Modal';
import { setTextToSpeechSettings } from '@/pages/book-preview/store/ebookSlice';

const TextToSpeechModal = ({ isOpen, handleClose }) => {
    const dispatch = useDispatch();
    const { pitch, rate, volume, voice } = useSelector(state => state.ebook.textToSpeechSettings);

    const [voices, setVoices] = useState([]);

    // Fetch available voices with error handling for mobile WebViews
    useEffect(() => {
        try {
            const fetchVoices = () => {
                try {
                    const availableVoices = window.speechSynthesis?.getVoices() || [];
                    setVoices(availableVoices);
                } catch (error) {
                    console.log('Error fetching voices:', error);
                    setVoices([]);
                }
            };

            // Safely check if speech synthesis is available
            if (window.speechSynthesis) {
                // Fetch voices and handle asynchronous voice loading
                try {
                    if (typeof window.speechSynthesis.onvoiceschanged !== 'undefined') {
                        window.speechSynthesis.onvoiceschanged = fetchVoices;
                    } else {
                        fetchVoices();
                    }
                } catch (error) {
                    console.log('Error setting onvoiceschanged:', error);
                    fetchVoices();
                }
            } else {
                // Speech synthesis not available
                setVoices([]);
            }
        } catch (error) {
            console.log('Speech synthesis not supported in this environment');
            setVoices([]);
        }
    }, []);

    const handleVoiceChange = (e) => {
        const selectedVoice = voices.find(voice => voice.name === e.target.value);
        dispatch(setTextToSpeechSettings({ voice: selectedVoice.name })); // Store the selected voice name in Redux
    };

    const handlePitchChange = (e) => {
        dispatch(setTextToSpeechSettings({ pitch: parseFloat(e.target.value) }));
    };

    const handleRateChange = (e) => {
        dispatch(setTextToSpeechSettings({ rate: parseFloat(e.target.value) }));
    };

    const handleVolumeChange = (e) => {
        dispatch(setTextToSpeechSettings({ volume: parseFloat(e.target.value) }));
    };

    return (
        <Modal
            title="Text to Speech Settings"
            label="Text to Speech Settings"
            activeModal={isOpen}
            onClose={handleClose}
        >
            {/* Voice Selection */}
            <div className="mt-4">
                <label className="block text-gray-700">
                    Voice:
                    <select
                        value={voice || ''}
                        onChange={handleVoiceChange}
                        className="w-full mt-2 border rounded px-2 py-1"
                    >
                        <option value="" disabled>Select a voice</option>
                        {voices.map((v, index) => (
                            <option key={index} value={v.name}>
                                {`${v.name} (${v.lang})`}
                            </option>
                        ))}
                    </select>
                </label>
            </div>
            {/* Pitch Control */}
            <div className="mt-4">
                <label className="block text-gray-700">
                    Pitch:
                    <input
                        type="range"
                        min="0.1"
                        max="2"
                        step="0.1"
                        value={pitch}
                        onChange={handlePitchChange}
                        className="w-full mt-2"
                    />
                    <span className="ml-2">{pitch}</span>
                </label>
            </div>
            {/* Rate Control */}
            <div className="mt-4">
                <label className="block text-gray-700">
                    Rate:
                    <input
                        type="range"
                        min="0.1"
                        max="2"
                        step="0.1"
                        value={rate}
                        onChange={handleRateChange}
                        className="w-full mt-2"
                    />
                    <span className="ml-2">{rate}</span>
                </label>
            </div>
            {/* Volume Control */}
            <div className="mt-4">
                <label className="block text-gray-700">
                    Volume:
                    <input
                        type="range"
                        min="0"
                        max="1"
                        step="0.1"
                        value={volume}
                        onChange={handleVolumeChange}
                        className="w-full mt-2"
                    />
                    <span className="ml-2">{volume}</span>
                </label>
            </div>
        </Modal>
    );
};

export default TextToSpeechModal;
