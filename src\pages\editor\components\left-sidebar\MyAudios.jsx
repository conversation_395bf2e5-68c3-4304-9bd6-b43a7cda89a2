import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Icon } from "@iconify/react";
import useDataFetching from '@/hooks/useDataFetching';
import { useQueryClient } from '@tanstack/react-query';
import Confirm from '@/components/ui/Confirm';
import api from '@/lib/axios';
import { useDispatch } from 'react-redux';
import { addElement } from '../../store/pagesSlice';

const MyAudios = ({ onClose }) => {
  const queryClient = useQueryClient();
  const dispatch = useDispatch();

  const [page, setPage] = useState(1);
  const [per_page] = useState(10); // Number of items per page (static for now)

  const onDrop = useCallback((acceptedFiles) => {
    const file = acceptedFiles[0];
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'audio');

    api.post('/admin/attachments', formData)
      .then(response => {
        queryClient.invalidateQueries('myAudios');
      })
      .catch(error => {
        console.error('Error uploading file:', error);
      });
  }, [queryClient, page, per_page]);

  const handleDelete = (id) => {
    Confirm(() => {
      api.delete(`/admin/attachments/${id}`)
        .then(() => {
          queryClient.invalidateQueries('myAudios');
        });
    });
  };

  const handleItemClick = (url) => {
    dispatch(addElement({
      type: 'audio',
      content: {
        audioUrl: url,
      },
      position: { x: 50, y: 50 },
      size: { width: 400, height: 100 },
    }));
    onClose();
  };

  const { getRootProps, getInputProps } = useDropzone({ onDrop });

  // Fetch audio attachments with pagination
  const { data, isLoading } = useDataFetching({
    queryKey: 'myAudios',
    endPoint: `/admin/attachments?search=audio&per_page=${per_page}&page=${page}`,
  });

  const totalPages = data?.data?.total_pages || 1; // Extract total pages from response

  const handlePrevious = () => {
    if (page > 1) setPage(page - 1);
  };

  const handleNext = () => {
    if (page < totalPages) setPage(page + 1);
  };

  return (
    <>
      <div {...getRootProps()} className="w-full">
        <input {...getInputProps()} />
        <button className="py-2 w-full bg-gray-100 border rounded hover:bg-gray-200">
          Upload Audio
        </button>
      </div>

      {data?.data?.data.filter(item => item.type === 'audio').length === 0 ? (
        <div className="text-center mt-4">No audio files found</div>
      ) : (
        <>
          <div className="grid grid-cols-1 gap-4 mt-4">
            {data?.data?.data.map((item) => (
              <div key={item.id} className="border p-4 rounded relative">
                <audio controls className="w-full">
                  <source src={`${import.meta.env.VITE_HOST_URL}/storage/${item.path}`} type="audio/mpeg" />
                </audio>
                <div className="flex justify-between mt-2">
                  <button
                    onClick={() => handleItemClick(`${import.meta.env.VITE_HOST_URL}/storage/${item.path}`)}
                    className="bg-blue-500 text-white px-4 py-2 rounded"
                  >
                    Insert
                  </button>
                  <button
                    onClick={() => handleDelete(item.id)}
                    className="bg-red-500 text-white p-2 rounded"
                  >
                    <Icon icon="bi:trash" />
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Conditional Pagination Controls */}
          {totalPages > 1 && (
            <div className="flex justify-center items-center mt-4 space-x-4">
              <button
                onClick={handlePrevious}
                disabled={page === 1}
                className={`px-3 py-1 rounded ${
                  page === 1 ? 'bg-gray-300 cursor-not-allowed' : 'bg-blue-500 text-white hover:bg-blue-600'
                }`}
              >
                Previous
              </button>
              <span>
                Page {page} of {totalPages}
              </span>
              <button
                onClick={handleNext}
                disabled={page === totalPages}
                className={`px-3 py-1 rounded ${
                  page === totalPages ? 'bg-gray-300 cursor-not-allowed' : 'bg-blue-500 text-white hover:bg-blue-600'
                }`}
              >
                Next
              </button>
            </div>
          )}
        </>
      )}
    </>
  );
};

export default MyAudios;
