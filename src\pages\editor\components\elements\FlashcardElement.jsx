import { useState } from 'react';
import { useSelector } from 'react-redux';

export default function Flashcard({  element }) {
  const [flipped, setFlipped] = useState(false);
  const { frontContent, backContent, styleOption } = element.content;

  const handleFlip = () => setFlipped(!flipped);
  const currentReadingElementId = useSelector(state => state.ebook.currentReadingElementId);

  return (
    <div
      className={`flashcard ${styleOption || 'Basic Flip'} ${flipped ? 'flipped' : ''}`}
      onClick={handleFlip}
    >
      <div className="card-inner">
        <div className="front">
          <div className="card-content text-center">
            <h3>{frontContent}</h3>
          </div>
        </div>
        <div className="back">
          <div className="card-content text-center">
            <h3>{backContent}</h3>
          </div>
        </div>
      </div>
    </div>
  );
}
