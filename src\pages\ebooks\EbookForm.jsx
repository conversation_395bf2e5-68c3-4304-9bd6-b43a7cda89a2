import React, { useState } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import {
  TextField,
  TextArea,
  Select,
  FileInput,
  SelectWithQuery,
} from "@/components/inputs";
import api from "@/lib/axios";
import { useQueryClient } from "@tanstack/react-query";
import useDataFetching from "@/hooks/useDataFetching";
import { CreatableSelectWithQuery } from "@/components/inputs/CreatableSelectWithQuery";
import { useTranslation } from "react-i18next";

const pageDimensions = [
  {
    value: "standard",
    label: "Standard (794 x 1123)",
    width: 794,
    height: 1123,
  },
  { value: "a4", label: "A4 (595 x 842)", width: 595, height: 842 },
  { value: "a5", label: "A5 (420 x 595)", width: 420, height: 595 },
  { value: "letter", label: "Letter (612 x 792)", width: 612, height: 792 },
  { value: "legal", label: "Legal (612 x 1008)", width: 612, height: 1008 },
  { value: "tabloid", label: "Tabloid (792 x 1224)", width: 792, height: 1224 },
  {
    value: "executive",
    label: "Executive (522 x 756)",
    width: 522,
    height: 756,
  },
  { value: "b5", label: "B5 (516 x 729)", width: 516, height: 729 },
  { value: "custom", label: "Custom Size", width: 0, height: 0 },
];

const EbookForm = ({ initialValues = {}, handleModalClose = () => {} }) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const { data: ebooksData, isLoading: ebooksLoading } = useDataFetching({
    queryKey: ["ebooksData", initialValues?.id],
    endPoint: `admin/ebooks/${initialValues?.id}`,
    enabled: !!initialValues?.id,
  });

  const { data: authorData, isLoading: authorLoading } = useDataFetching({
    queryKey: ["authorData", initialValues?.id],
    endPoint: "authors?pagination=false",
  });
  const { data: categoryData, isLoading: categoryLoading } = useDataFetching({
    queryKey: ["categoryData", initialValues?.id],
    endPoint: "categories?pagination=false",
  });

  const defaultValues = {
    title: "",
    description: "",
    genre_id: "",
    author: "",
    status: "published",
    book_type: "ebook",
    author_ids: [],
    category_ids: [],
    cover_image: "",
    price: "",
    discount: "",
    final_price: "",
    pdf_file: "",
    page_dimension: "standard",
    width: 794,
    height: 1123,
    orientation: "portrait",
    margins: {
      top: 10,
      bottom: 10,
      left: 10,
      right: 10,
    },
  };

  const newInitialValues = {
    title: initialValues?.title ?? "",
    author_ids: initialValues?.authors?.map((author) => author.id) ?? [],
    description: initialValues?.description ?? "",
    // is_public: initialValues?.is_public ?? 0,
    status: initialValues?.status ?? "published",
    book_type: initialValues?.book_type ?? "ebook",
    price: initialValues?.price ?? "",
    discount: initialValues?.discount ?? "",
    final_price: initialValues?.final_price ?? "",
    // pdf_file: initialValues?.pdf_file ?? '',
    width: initialValues?.width ?? 794,
    height: initialValues?.height ?? 1123,
    orientation: initialValues?.orientation ?? "portrait",
    margins: initialValues?.margins ?? {
      top: 10,
      bottom: 10,
      left: 10,
      right: 10,
    },
  };
  const cleanInitialValues = {
    ...newInitialValues,
    genre_id: initialValues?.genre_id ? parseInt(initialValues?.genre_id) : "",
    author_ids:
      authorData &&
      ebooksData &&
      authorData?.data
        ?.filter((author) =>
          ebooksData?.data?.authors?.some((a) => a?.id === author?.id)
        )
        .map((author) => author?.id),
    category_ids:
      categoryData &&
      ebooksData &&
      categoryData?.data
        ?.filter((category) =>
          ebooksData?.data?.categories?.some((a) => a?.id === category?.id)
        )
        .map((author) => author?.id),
    cover_image: initialValues?.cover_image ?? "",
    pdf_file: initialValues?.pdf_file ?? "",
  };

  delete cleanInitialValues?.publisher_id;
  delete cleanInitialValues?.author_id;
  delete cleanInitialValues?.tenant_id;
  delete cleanInitialValues?.author;

  // Find a matching predefined dimension or set to custom
  const matchedDimension =
    pageDimensions.find(
      (dim) =>
        dim.value !== "custom" &&
        dim.width === cleanInitialValues.width &&
        dim.height === cleanInitialValues.height
    )?.value || "custom";
  const mergedValues = {
    ...defaultValues,
    ...cleanInitialValues,
    page_dimension: matchedDimension,
  };

  const validationSchema = Yup.object().shape({
    title: Yup.string().required("Title is required"),
    status: Yup.string().required("Status is required"),
    book_type: Yup.string().required("Book type is required"),
    width: Yup.number().required("Width is required").positive(),
    height: Yup.number().required("Height is required").positive(),
    page_dimension: Yup.string().required("Page dimension is required"),
    category_ids: Yup.array()
      .min(1, "At least one category is required")
      .required("Categories are required"),
  });

  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();
    const authorIdsArray = Array.isArray(values.author_ids)
      ? values.author_ids
      : [];
    const categoryIdsArray = Array.isArray(values.category_ids)
      ? values.category_ids
      : [];

    Object.keys(values).forEach((key) => {
      if (key === "margins") {
        formData.append(key, JSON.stringify(values[key]));
      } else if (key === "cover_image") {
        if (values.cover_image && typeof values.cover_image !== "string") {
          formData.append("cover_image", values.cover_image);
        }
      } else if (key === "pdf_file") {
        if (values.pdf_file && typeof values.pdf_file !== "string") {
          formData.append("pdf_file", values.pdf_file);
        }
      } else if (key === "author_ids") {
        authorIdsArray.forEach((id) => formData.append("author_ids[]", id));
      } else if (key === "category_ids") {
        categoryIdsArray.forEach((id) => formData.append("category_ids[]", id));
      } else {
        formData.append(key, values[key]);
      }
    });

    if (initialValues?.id) {
      formData.append("_method", "PUT");
    }

    try {
      const url = initialValues?.id
        ? `/admin/ebooks/${initialValues.id}`
        : `/admin/ebooks`;
      await api.post(url, formData);
      resetForm();
      handleModalClose();
      queryClient.invalidateQueries("ebooks");
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  return (
    <Formik
      initialValues={mergedValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize
    >
      {({ values, dirty, errors, isSubmitting, setFieldValue }) => {
        React.useEffect(() => {
          const updatedFinalPrice = Math.max(values.price - values.discount, 0);
          setFieldValue("final_price", updatedFinalPrice);
        }, [values.price, values.discount, setFieldValue]);

        return (
          <Form className="grid grid-cols-3 gap-x-4" noValidate>
            {console.log(errors, values)}
            <TextField
              label={t("ebook.form.fields.title")}
              name="title"
              required
            />
            <CreatableSelectWithQuery
              label={t("ebook.form.fields.authors")}
              isMulti={true}
              name="author_ids"
              endPoint="authors?pagination=false"
              createEndpoint="admin/authors"
              queryKey="authors"
              dataAccessKey="data"
            />
            <SelectWithQuery
              label={t("ebook.form.fields.categories")}
              name="category_ids"
              isMulti={true}
              endPoint="categories?pagination=false"
              queryKey="categories"
              valueKey="id"
              labelKey="name"
              dataAccessKey="data"
              required
              returnSingleValue={false}
            />
            <div className="col-span-3">
              <TextArea
                label={t("ebook.form.fields.description")}
                name="description"
              />
            </div>
            <Select
              label={t("ebook.form.fields.bookType")}
              name="book_type"
              options={[
                {
                  value: "pdf",
                  label: t("ebook.form.fields.bookType.options.pdf"),
                },
                {
                  value: "ebook",
                  label: t("ebook.form.fields.bookType.options.ebook"),
                },
              ]}
            />
            {values.book_type === "ebook" && (
              <div>
                <Select
                  label={t("ebook.form.fields.pageDimension")}
                  name="page_dimension"
                  required
                  isDisabled={!!initialValues?.id}
                  options={pageDimensions}
                  onChange={(selected) => {
                    setFieldValue("page_dimension", selected.value);
                    if (selected.value !== "custom") {
                      setFieldValue("width", selected.width);
                      setFieldValue("height", selected.height);
                    }
                  }}
                  value={
                    values.page_dimension === "custom"
                      ? pageDimensions.find((dim) => dim.value === "custom")
                      : pageDimensions.find(
                          (dim) =>
                            dim.width === values.width &&
                            dim.height === values.height
                        ) || pageDimensions[0]
                  }
                />
              </div>
            )}
            {values.page_dimension === "custom" && (
              <>
                <TextField
                  label={t("ebook.form.fields.width")}
                  name="width"
                  type="number"
                  required
                />
                <TextField
                  label={t("ebook.form.fields.height")}
                  name="height"
                  type="number"
                  required
                />
              </>
            )}
            <Select
              label={t("ebook.form.fields.status")}
              name="status"
              options={[
                {
                  value: "draft",
                  label: t("ebook.form.fields.status.options.draft"),
                },
                {
                  value: "published",
                  label: t("ebook.form.fields.status.options.published"),
                },
              ]}
            />
            <TextField label={t("ebook.form.fields.price")} name="price" />
            <TextField
              label={t("ebook.form.fields.discount")}
              name="discount"
            />
            <TextField
              label={t("ebook.form.fields.finalPrice")}
              name="final_price"
            />

            {values.book_type === "pdf" && (
              <div className="col-span-3">
                <FileInput
                  label={t("ebook.form.fields.pdfFile")}
                  name="pdf_file"
                  accept={{ "application/pdf": [".pdf"] }}
                  accepts="application/pdf"
                />
              </div>
            )}
            <div className="col-span-3">
              <FileInput
                label={t("ebook.form.fields.coverImage")}
                name="cover_image"
                accept={{ "image/*": [".png", ".jpg", ".jpeg"] }}
                accepts="image/*"
              />
            </div>
            <button
              type="submit"
              className={`col-span-3 py-2 px-4 rounded ${
                dirty && !isSubmitting
                  ? "bg-blue-500 text-white"
                  : "bg-gray-300 text-gray-600 cursor-not-allowed"
              }`}
              disabled={!dirty || isSubmitting}
            >
              {initialValues?.id
                ? t("ebook.form.fields.buttons.update")
                : t("ebook.form.fields.buttons.submit")}
            </button>
          </Form>
        );
      }}
    </Formik>
  );
};

export default EbookForm;
