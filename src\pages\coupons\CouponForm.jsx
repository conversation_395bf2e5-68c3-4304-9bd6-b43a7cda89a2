import React from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { TextField } from "@/components/inputs/TextField";
import { Select } from "@/components/inputs/Select";
import { DatePicker } from "@/components/inputs/DatePicker";
import Button from "@/components/ui/Button";
import { useTranslation } from "react-i18next";
import SearchableSelect from "@/components/inputs/SearchableSelect";

const CouponForm = ({
  initialValues,
  onSubmit,
  isEditMode = false,
  onClose,
}) => {
  const { t } = useTranslation();

  const validationSchema = Yup.object({
    code: Yup.string().required(t("couponForm.validation.codeRequired")),
    discount_type: Yup.string()
      .oneOf(
        ["percentage", "fixed"],
        t("couponForm.validation.invalidDiscountType")
      )
      .required(t("couponForm.validation.discountTypeRequired")),
    discount_value: Yup.number()
      .min(0, t("couponForm.validation.minDiscount"))
      .required(t("couponForm.validation.discountValueRequired")),
    applies_to: Yup.string()
      .oneOf(["order", "item"], t("couponForm.validation.invalidAppliesTo"))
      .required(t("couponForm.validation.appliesToRequired")),
    item_id: Yup.string().when("applies_to", {
      is: "item",
      then: Yup.string().required(t("couponForm.validation.itemIdRequired")),
    }),
    min_order_amount: Yup.number()
      .min(0, t("couponForm.validation.minOrderAmount"))
      .required(t("couponForm.validation.minOrderAmountRequired")),
    max_discount: Yup.number()
      .min(0, t("couponForm.validation.minMaxDiscount"))
      .required(t("couponForm.validation.maxDiscountRequired")),
    usage_limit: Yup.number()
      .min(0, t("couponForm.validation.minUsageLimit"))
      .required(t("couponForm.validation.usageLimitRequired")),
    start_date: Yup.date().required(
      t("couponForm.validation.startDateRequired")
    ),
    end_date: Yup.date()
      .min(Yup.ref("start_date"), t("couponForm.validation.endDateAfterStart"))
      .required(t("couponForm.validation.endDateRequired")),
    is_active: Yup.string()
      .oneOf(["0", "1"], t("couponForm.validation.invalidStatus"))
      .required(t("couponForm.validation.statusRequired")),
  });

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={onSubmit}
    >
      {({ values, isSubmitting }) => (
        <Form className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <TextField
              label={t("couponForm.code")}
              name="code"
              type="text"
              required
            />

            <Select
              label={t("couponForm.discountType")}
              name="discount_type"
              options={[
                { value: "percentage", label: t("couponForm.percentage") },
                { value: "fixed", label: t("couponForm.fixed") },
              ]}
            />

            <TextField
              label={t("couponForm.discountValue")}
              name="discount_value"
              type="number"
              min="0"
              required
            />

            <Select
              label={t("couponForm.appliesTo")}
              name="applies_to"
              options={[
                { value: "order", label: t("couponForm.entireOrder") },
                { value: "item", label: t("couponForm.specificItem") },
              ]}
            />

            {values.applies_to === "item" && (
              <SearchableSelect
                label="Select Item"
                name="item_id"
                endpoint="https://dev-api.yourepub.com/api/admin/ebooks"
                dataKey="data.data"
                labelKey="title"
                valueKey="id"
                isMulti={false}
              />
            )}

            <TextField
              label={t("couponForm.minOrderAmount")}
              name="min_order_amount"
              type="number"
              min="0"
              required
            />

            <TextField
              label={t("couponForm.maxDiscount")}
              name="max_discount"
              type="number"
              min="0"
              required
            />

            <TextField
              label={t("couponForm.usageLimit")}
              name="usage_limit"
              type="number"
              min="0"
              required
            />

            <DatePicker
              label={t("couponForm.startDate")}
              name="start_date"
              dateFormat="yyyy-MM-dd"
              placeholderText="Select start date"
              required
            />

            <DatePicker
              label={t("couponForm.endDate")}
              name="end_date"
              dateFormat="yyyy-MM-dd"
              placeholderText="Select end date"
              minDate={values.start_date instanceof Date ? values.start_date : null}
              required
            />

            <Select
              label={t("couponForm.status")}
              name="is_active"
              options={[
                { value: "1", label: t("couponForm.active") },
                { value: "0", label: t("couponForm.inactive") },
              ]}
            />
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-white bg-gray-500 hover:bg-gray-600 rounded-md"
            >
              {t("cancel")}
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-blue-500 text-white py-2 px-4 rounded"
            >
              {isSubmitting && (
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              )}
              {isEditMode
                ? t("couponForm.updateCoupon")
                : t("couponForm.addCoupon")}
            </button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default CouponForm;
