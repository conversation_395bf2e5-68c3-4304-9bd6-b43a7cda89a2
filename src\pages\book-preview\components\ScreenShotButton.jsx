import React from 'react';
import { Icon } from '@iconify/react';
import IconButton from './IconButton';
import html2canvas from 'html2canvas';

const ScreenShotButton = () => {
  const takeScreenshot = () => {
    // Function to take a screenshot of the entire window
    html2canvas(document.body).then((canvas) => {
      // Convert the canvas to a data URL
      const screenshot = canvas.toDataURL('image/png');

      // Create a link element and trigger the download
      const downloadLink = document.createElement('a');
      downloadLink.href = screenshot;
      downloadLink.download = 'screenshot.png'; // File name
      downloadLink.click();
    });
  };

  return (
      <IconButton icon="mdi:camera" text="Screenshot" onClick={takeScreenshot} />
  );
};

export default ScreenShotButton;
