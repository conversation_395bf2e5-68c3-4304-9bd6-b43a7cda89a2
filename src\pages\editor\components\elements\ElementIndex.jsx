// src/pages/editor/components/elements/IndexElement.jsx

import React from 'react';
import { useSelector } from 'react-redux';

const IndexElement = ({ element }) => {
  const { id, content, style } = element;
  const { title = '', items = [], template = 'classic' } = content;
  const currentReadingElementId = useSelector((state) => state.ebook.currentReadingElementId);

  return (
    <div
      style={{
        height: element.size.height,
        width: element.size.width,
        ...style,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
      }}
      className={
        currentReadingElementId === element.id
          ? 'border border-blue-500 p-4'
          : ''
      }
    >
      {/* Index Title */}
      {/* <h2 className="text-lg font-semibold mb-2">{title}</h2> */}

      {/* Decide which template to render */}
      {template === 'classic' && <ClassicIndex items={items} />}
      {template === 'greenStripes' && <GreenStripesIndex items={items} />}
      {template === 'twoColumnOld' && <TwoColumnIndex items={items} />}
      {template === 'modernGraphic' && <ModernGraphicIndex items={items} />}
    </div>
  );
};

export default IndexElement;

/** Classic "dotted line" single column */
function ClassicIndex({ items }) {
  return (
    <ul className="list-none w-full">
      {items.map((item, idx) => (
        <li key={idx} className="mb-2">
          <a
            href={`#page-${item.page}`}
            className="flex items-center no-underline text-black hover:underline"
            style={{ textDecorationThickness: '1px' }}
          >
            <span className="whitespace-nowrap">{item.label}</span>
            <span className="flex-grow border-b-2 border-dotted border-black mx-2" />
            <span>{item.page}</span>
          </a>
        </li>
      ))}
    </ul>
  );
}

/** "Green Stripes" single column */
function GreenStripesIndex({ items }) {
  const rowColors = ['#D0F0C0', '#A5D6A7']; // pick your own green shades

  return (
    <ul className="list-none w-full">
      {items.map((item, idx) => {
        const bgColor = rowColors[idx % 2];
        return (
          <li key={idx} className="mb-1">
            <a
              href={`#page-${item.page}`}
              className="flex items-center px-2 py-2 text-black no-underline hover:underline"
              style={{ backgroundColor: bgColor, borderRadius: '4px' }}
            >
              <span className="whitespace-nowrap font-semibold">{item.label}</span>
              <span className="flex-grow" />
              <span className="ml-4">{item.page}</span>
            </a>
          </li>
        );
      })}
    </ul>
  );
}

/** Alternate left-right in two columns, dotted line style. */
function TwoColumnIndex({ items }) {
  const leftItems = [];
  const rightItems = [];
  items.forEach((item, idx) => {
    if (idx % 2 === 0) leftItems.push(item);
    else rightItems.push(item);
  });

  return (
    <div className="flex w-full">
      {/* Left column */}
      <div className="w-1/2 pr-2">
        <ul className="list-none">
          {leftItems.map((item, idx) => (
            <li key={idx} className="mb-1">
              <a
                href={`#page-${item.page}`}
                className="flex items-center no-underline text-black hover:underline"
                style={{ textDecorationThickness: '1px' }}
              >
                <span className="whitespace-nowrap">{item.label}</span>
                <span className="flex-grow border-b-2 border-dotted border-black mx-2" />
                <span>{item.page}</span>
              </a>
            </li>
          ))}
        </ul>
      </div>
      {/* Right column */}
      <div className="w-1/2 pl-2">
        <ul className="list-none">
          {rightItems.map((item, idx) => (
            <li key={idx} className="mb-1">
              <a
                href={`#page-${item.page}`}
                className="flex items-center no-underline text-black hover:underline"
                style={{ textDecorationThickness: '1px' }}
              >
                <span className="whitespace-nowrap">{item.label}</span>
                <span className="flex-grow border-b-2 border-dotted border-black mx-2" />
                <span>{item.page}</span>
              </a>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}

/** "Modern Graphic" design (inspired by your sample image) */
function ModernGraphicIndex({ items }) {
  return (
    <div className="flex flex-col gap-4 w-full">
      {items.map((item, idx) => {
        // Create a 2-digit chapter number, e.g. 01, 02, ...
        const chapterNumber = String(idx + 1).padStart(2, '0');

        return (
          <div
            key={idx}
            className="flex items-center bg-gray-100 shadow-md rounded-lg px-4 py-3 relative"
          >
            {/* Left "Chapter" label and big number */}
            <div className="flex flex-col items-center justify-center mr-4">
              <div className="text-xs font-semibold text-gray-600 uppercase tracking-wide">
                Chapter
              </div>
              <div className="text-3xl font-extrabold text-blue-700">
                {chapterNumber}
              </div>
            </div>

            {/* Main label & some placeholder text */}
            <div className="flex-1">
              <div className="text-lg font-semibold text-gray-800">
                {item.label}
              </div>
              <div className="text-sm text-gray-500">
                {/* If you have a description, put it here. For now, example text: */}
                {item.description}
              </div>
            </div>

            {/* Page link on the right */}
            <a
              href={`#page-${item.page}`}
              className="ml-auto text-blue-600 hover:underline text-sm font-medium"
            >
              Page {item.page}
            </a>
          </div>
        );
      })}
    </div>
  );
}
