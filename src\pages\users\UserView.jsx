import React from 'react';
import { useNavigate, useParams } from "react-router-dom";
import Icons from '@/components/ui/Icon';
import useDataFetching from '@/hooks/useDataFetching';
import { useTranslation } from "react-i18next";

const UserView = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const { id } = useParams();
    const { data: usersData, isLoading } = useDataFetching({
        queryKey: ['users', id],
        endPoint: `admin/users/${id}`,
    });

    if (isLoading) {
        return <div>{t("userView.loading")}</div>;
    }
    return (
        <div>
            <div className="flex justify-center">
                <div className="w-full max-w-4xl py-5 shadow-lg border border-[#E2E8F0] rounded-md bg-white">
                    <div className="border-b border-[#D0D0D0]">
                        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between px-5 pb-3">
                            <h6 className="text-xl text-[#000000] font-medium mb-2 sm:mb-0">
                                {t("userView.user")}
                            </h6>
                            <button
                                className="btn w-full sm:w-auto rounded-lg bg-[#414243] text-base font-normal"
                                onClick={() => navigate(-1)}
                            >
                                <span className="flex gap-3 items-center justify-center text-white">
                                    <Icons icon="material-symbols:arrow-top-left" width={18} />
                                    {t("userView.backToList")}
                                </span>
                            </button>
                        </div>
                    </div>
                    <div className="px-5 mt-5">
                        <div className="grid grid-cols-12 gap-4 sm:gap-2">
                            <div className="col-span-2">
                                <h6 className="text-base font-normal text-[#000000]">{t("userView.name")}</h6>
                            </div>
                            <div className="col-span-1">
                                <p className='text-base font-semibold text-[#000000]'>:</p>
                            </div>
                            <div className="col-span-9">
                                <p className="text-base font-normal text-[#7A818A]">{usersData?.data?.name}</p>
                            </div>
                        </div>
                        <div className="grid grid-cols-12 gap-4 sm:gap-2 mt-3">
                            <div className="col-span-2">
                                <h6 className="text-base font-normal text-[#000000]">{t("userView.email")}</h6>
                            </div>
                            <div className="col-span-1">
                                <p className='text-base font-semibold text-[#000000]'>:</p>
                            </div>
                            <div className="col-span-9">
                                <p className="text-base font-normal text-[#7A818A]">{usersData?.data?.email}</p>
                            </div>
                        </div>
                        <div className="grid grid-cols-12 gap-4 sm:gap-2 mt-3">
                            <div className="col-span-2">
                                <h6 className="text-base font-normal text-[#000000]">{t("userView.role")}</h6>
                            </div>
                            <div className="col-span-1">
                                <p className='text-base font-semibold text-[#000000]'>:</p>
                            </div>
                            <div className="col-span-9">
                                <p className="text-base font-normal text-[#7A818A]">{usersData?.data?.role}</p>
                            </div>
                        </div>
                        <div className="grid grid-cols-12 gap-4 sm:gap-2 mt-3">
                            <div className="col-span-2">
                                <h6 className="text-base font-normal text-[#000000]">{t("userView.profilePicture")}</h6>
                            </div>
                            <div className="col-span-1">
                                <p className='text-base font-semibold text-[#000000]'>:</p>
                            </div>
                            <div className="col-span-9">
                                <img
                                    src={usersData?.data?.profile_picture_url}
                                    alt={usersData?.data?.title}
                                    className="h-[52px] w-[80px] object-cover rounded-md"
                                />
                                <p className="text-sm text-[#7A818A] mt-2">
                                    {t("userView.profilePicture") + usersData?.data?.profile_picture?.substring(usersData?.data?.profile_picture.lastIndexOf('.'))}
                                </p>
                            </div>
                        </div>
                        <div className="grid grid-cols-12 gap-4 sm:gap-2 mt-3">
                            <div className="col-span-2">
                                <h6 className="text-base font-normal text-[#000000]">{t("userView.status")}</h6>
                            </div>
                            <div className="col-span-1">
                                <p className='text-base font-semibold text-[#000000]'>:</p>
                            </div>
                            <div className="col-span-9">
                                <p className="text-base font-normal text-[#7A818A]">
                                    {usersData?.data?.is_active ? t("userView.active") : t("userView.inactive")}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default UserView;
