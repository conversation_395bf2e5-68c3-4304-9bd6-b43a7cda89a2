import { Icon } from '@iconify/react';
import useIsMobile from '../../../hooks/useIsMobile';

const IconButton = ({ icon, text, onClick, activeButton }) => {
  const isMobile = useIsMobile();

  const buttonStyle = activeButton
    ? 'bg-blue-500 text-white' 
    : 'bg-gray-100 text-gray-600'; 

  return (
    <button
      className={`flex rounded-lg flex-col justify-center  items-center focus:outline-none border-b  ${buttonStyle}`}
      style={{ width: isMobile ? '25px' : '50px' }}
      onClick={onClick}
    >
      <Icon icon={icon} className="w-8 h-8" />
    </button>
  );
};

export default IconButton;
