import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { setSelectedElement, deleteElement, reorderElements } from '../../store/pagesSlice';
import { TrashIcon, GripVerticalIcon } from 'lucide-react';

const Layers = () => {
  const dispatch = useDispatch();
  const currentPage = useSelector((state) => state.pages.present.currentPage);
  const pages = useSelector((state) => state.pages.present.pages);
  const selectedElements = useSelector((state) => state.pages.present.selectedElements);

  const currentPageData = pages[currentPage];
  const elements = currentPageData?.elements || [];

  const handleSelectElement = (elementId) => {
    dispatch(setSelectedElement(elementId));
  };

  const handleDragStart = (e, index) => {
    e.dataTransfer.setData('text/plain', index);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e, dropIndex) => {
    e.preventDefault();
    const dragIndex = parseInt(e.dataTransfer.getData('text/plain'), 10);
    if (dragIndex === dropIndex) return;

    const newOrder = [...elements];
    const [draggedElement] = newOrder.splice(dragIndex, 1);
    newOrder.splice(dropIndex, 0, draggedElement);

    // Update the Redux state with the new order
    dispatch(
      reorderElements({
        pageId: currentPageData.id,
        newOrder,
      })
    );
  };

  const handleDeleteElement = (elementId) => {
    dispatch(deleteElement(elementId));
  };

  return (
    <div className="flex flex-col space-y-2">
      <div className="text-sm font-medium dark:text-white mb-2">
        {currentPageData?.name || `Page ${currentPage + 1}`}
      </div>
      {elements.map((element, index) => (
        <div
          key={element.id}
          draggable
          onDragStart={(e) => handleDragStart(e, index)}
          onDragOver={handleDragOver}
          onDrop={(e) => handleDrop(e, index)}
          className={`flex items-center justify-between p-2 rounded cursor-move dark:text-white 
            hover:bg-gray-100 hover:text-black dark:hover:text-black 
            ${selectedElements.includes(element.id) 
              ? 'bg-blue-50 text-black dark:bg-blue-50 dark:text-black' 
              : ''
            }`}
          onClick={() => handleSelectElement(element.id)}
        >
          <div className="flex items-center space-x-2">
            <span className="text-xs ">{elements.length - index}</span>
            <span className="text-sm ">{element.type}</span>
          </div>
          <div className="flex items-center space-x-2 text-gray-400">
            <GripVerticalIcon size={16} className="text-gray-500" style={{ color: 'blue' }} />
            <TrashIcon
              size={16}
              onClick={() => handleDeleteElement(element.id)}
              className="cursor-pointer"
              style={{ color: 'red' }}
            />
          </div>
        </div>
      ))}
      {elements.length === 0 && (
        <div className="text-sm text-gray-500 text-center py-4">
          No elements on this page
        </div>
      )}
    </div>
  );
};

export default Layers;
