import React from 'react';

const Sections = () => {
  return (
    <div className="flex flex-col min-h-screen w-[300px] mx-auto bg-gray-50 text-gray-800 scale-[.85]">
      {/* Header */}
      <header className="bg-blue-600 text-white p-4 shadow-md">
        <h1 className="text-2xl font-bold">My Website</h1>
        <p className="text-sm">Responsive layout example with scaled sections</p>
      </header>

      {/* Main Content with Scaled Image Grid */}
      <main className="flex-1 p-4">
        <h2 className="text-xl font-semibold mb-4">Image Grid</h2>
        <div className="grid grid-cols-2 gap-3">
          {[...Array(6)].map((_, index) => (
            <div
              key={index}
              className="w-full h-28 bg-blue-200 rounded-lg flex items-center justify-center"
            >
              <p className="text-base font-semibold text-gray-600">Image {index + 1}</p>
            </div>
          ))}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-blue-600 text-white p-3 mt-auto">
        <div className="flex justify-between items-center text-sm">
          <p>&copy; 2024 My Website</p>
          <nav className="flex space-x-2">
            <a href="#about" className="hover:underline">
              About
            </a>
            <a href="#contact" className="hover:underline">
              Contact
            </a>
            <a href="#privacy" className="hover:underline">
              Privacy Policy
            </a>
          </nav>
        </div>
      </footer>
    </div>
  );
};

export default Sections;
