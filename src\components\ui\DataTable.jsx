import React, { useEffect, useState } from "react";
import { useTable, usePagination } from "react-table";

const EnhancedServerSideTable = ({
  title = "Table Title",
  columns,
  fetchData,
  loading,
  data,
  totalPages,
  currentPage,
  pageSize,
  onPageChange,
  onPageSizeChange,
  onSearch,
  buttonLabel = "",
  onButtonClick,
  size = "md",
  isStriped = true,
}) => {
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    rows,
    prepareRow,
  } = useTable(
    {
      columns,
      data,
      manualPagination: true,
      pageCount: totalPages,
    },
    usePagination
  );

  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    if (onSearch) {
      onSearch(searchTerm);
    }
  }, [searchTerm]);

  const sizeClasses = {
    sm: "text-sm px-4 py-2",
    md: "text-md px-6 py-3",
    lg: "text-lg px-8 py-4",
  };

  const calculateSerialNumber = (rowIndex) => {
    return (currentPage - 1) * pageSize + rowIndex + 1;
  };

  return (
    <div className="dark:bg-gray-900 dark:text-gray-100 bg-white rounded-lg p-6">
      {title && (
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold dark:text-gray-100">{title}</h2>
          <div className="flex items-center space-x-3">
            {onSearch && (
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search..."
                className="w-56 h-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 px-3 py-2 text-sm"
              />
            )}
            {onButtonClick && buttonLabel && (
              <button
                onClick={onButtonClick}
                className="bg-blue-500 hover:bg-blue-700 text-white rounded-md dark:bg-blue-600 dark:hover:bg-blue-800 px-4 py-2 text-sm h-10"
              >
                {buttonLabel}
              </button>
            )}
          </div>
        </div>
      )}
      <div className="overflow-x-auto border rounded-lg shadow-sm dark:border-gray-700">
        <table
          {...getTableProps()}
          className="min-w-full divide-y divide-gray-200 dark:divide-gray-600 bg-white dark:bg-gray-800"
        >
          <thead className="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th className={`text-left font-semibold uppercase tracking-wider dark:text-white ${sizeClasses[size]}`}>
                #
              </th>
              {headerGroups.map((headerGroup) =>
                headerGroup.headers.map((column) => (
                  <th
                    {...column.getHeaderProps()}
                    className={`text-left font-semibold uppercase tracking-wider dark:text-gray-100 ${sizeClasses[size]}`}
                  >
                    {column.render("Header")}
                  </th>
                ))
              )}
            </tr>
          </thead>
          <tbody
            {...getTableBodyProps()}
            className={`divide-y divide-gray-200 dark:divide-gray-600 ${isStriped ? "bg-gray-50 dark:bg-gray-800" : ""}`}
          >
            {loading ? (
              Array.from({ length: pageSize }).map((_, index) => (
                <tr key={index} className="animate-pulse">
                  <td className={`text-gray-400 bg-gray-100 dark:bg-gray-700 ${sizeClasses[size]}`}>
                    &nbsp;
                  </td>
                  {columns.map((_, colIndex) => (
                    <td
                      key={colIndex}
                      className={`text-gray-400 bg-gray-100 dark:bg-gray-700 ${sizeClasses[size]}`}
                    >
                      <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mx-auto"></div>
                    </td>
                  ))}
                </tr>
              ))
            ) : rows.length > 0 ? (
              rows.map((row, index) => {
                prepareRow(row);
                return (
                  <tr
                    {...row.getRowProps()}
                    className={`border-b dark:border-b dark:border-gray-600 ${
                      isStriped
                        ? index % 2 === 0
                          ? "bg-gray-100 dark:bg-gray-700"
                          : "bg-white dark:bg-gray-800"
                        : ""
                    }`}
                  >
                    <td className={`text-gray-700 dark:text-white ${sizeClasses[size]}`}>
                      {calculateSerialNumber(index)}
                    </td>
                    {row.cells.map((cell) => (
                      <td
                        {...cell.getCellProps()}
                        className={`text-gray-700 dark:text-gray-200 ${sizeClasses[size]}`}
                      >
                        {cell.render("Cell")}
                      </td>
                    ))}
                  </tr>
                );
              })
            ) : (
              <tr>
                <td colSpan={columns.length + 1} className="text-center py-4">
                  No data found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      
        <div className="flex justify-between items-center mt-6">
          {onPageSizeChange && (
            <div className="flex items-center space-x-4">
              <span className="text-sm dark:text-gray-400">Rows per page:</span>
              <select
                onChange={(e) => onPageSizeChange(Number(e.target.value))}
                className="border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 px-3 py-1"
                value={pageSize}
              >
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={30}>30</option>
                <option value={50}>50</option>
              </select>
            </div>
          )}
          {onPageChange && totalPages && (
            <div className="flex space-x-2">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <button
                  key={page}
                  onClick={() => onPageChange(page)}
                  className={`p-2 w-8 h-8 flex items-center justify-center rounded-full border dark:border-gray-600 ${
                    page === currentPage
                      ? "bg-blue-500 text-white dark:bg-blue-600 dark:hover:bg-blue-800"
                      : "bg-white text-gray-700 dark:bg-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  }`}
                >
                  {page}
                </button>
              ))}
            </div>
          )}
        </div>
      
    </div>
  );
};

export default EnhancedServerSideTable;
