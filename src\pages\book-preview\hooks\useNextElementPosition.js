import { useSelector } from 'react-redux';

/**
 * Returns a function that, when called, calculates the position
 * (x=50, y = bottom of last element + 20) for adding new elements
 * at the end of the page.
 */
export default function useNextElementPosition() {
  // Access the current page’s elements from the Redux store
  const { elements = [] } = useSelector((state) => {
    const currentPageIndex = state.pages.present.currentPage;
    const currentPage =
      state.pages.present.pages[currentPageIndex] || {};
    return { elements: currentPage.elements || [] };
  });

  /**
   * A function that finds the single "lowest element" on the page,
   * then returns a coordinate just below it. If no elements, defaults to (50, 50).
   */
  const calculateNextPosition = () => {
    if (!elements.length) {
      return { x: 30, y: 50 };
    }

    // 1) Find the element with the greatest (y + height)
    const lowestElement = elements.reduce((lowest, current) => {
      const currentBottom = current.position.y + (current.size?.height || 0);
      const lowestBottom =
        lowest ? lowest.position.y + (lowest.size?.height || 0) : 0;
      return currentBottom > lowestBottom ? current : lowest;
    }, null);

    // 2) Return the next position, offset by 20
    return {
      x: 30,
      y: lowestElement
        ? lowestElement.position.y + (lowestElement.size?.height || 0) + 20
        : 50,
    };
  };

  // Return the calculation function
  return calculateNextPosition;
}
