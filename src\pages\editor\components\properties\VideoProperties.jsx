import React, { useState, useEffect } from 'react';
import { isValidYoutubeUrl, isValidVideoUrl, getYoutubeEmbedUrl } from '../../utils/videoUtils';

const VideoProperties = ({ element, handleUpdate }) => {
    const [error, setError] = useState('');
    const [sourceType, setSourceType] = useState('direct');

    useEffect(() => {
        // Detect source type when component mounts
        if (element.content.isYoutube) {
            setSourceType('youtube');
        }
    }, [element.content.isYoutube]);

    const handleSourceChange = (e) => {
        const newUrl = e.target.value;
        setError('');

        if (!newUrl) {
            handleUpdate({
                content: {
                    ...element.content,
                    videoUrl: '',
                    isYoutube: false
                }
            });
            return;
        }

        if (!isValidVideoUrl(newUrl)) {
            setError('Please enter a valid video URL');
            return;
        }

        const isYoutube = isValidYoutubeUrl(newUrl);
        const finalUrl = isYoutube ? getYoutubeEmbedUrl(newUrl) : newUrl;

        handleUpdate({
            content: {
                ...element.content,
                videoUrl: finalUrl,
                isYoutube
            }
        });
    };

    const handleThumbnailChange = (e) => {
        handleUpdate({
            content: {
                ...element.content,
                thumbnail: e.target.value
            }
        });
    };

    const handleSourceTypeChange = (e) => {
        setSourceType(e.target.value);
        // Reset video URL when changing source type
        handleUpdate({
            content: {
                ...element.content,
                videoUrl: '',
                isYoutube: e.target.value === 'youtube'
            }
        });
        setError('');
    };

    return (
        <div className="video-properties space-y-4">
            <div>
                <label className="block mb-2">Video Source Type:</label>
                <select
                    value={sourceType}
                    onChange={handleSourceTypeChange}
                    className="w-full p-2 border rounded"
                >
                    <option value="direct">Direct Video URL</option>
                    <option value="youtube">YouTube Video</option>
                </select>
            </div>

            <div>
                <label className="block mb-2">
                    {sourceType === 'youtube' ? 'YouTube URL:' : 'Video URL:'}
                </label>
                <input
                    type="text"
                    value={element.content.videoUrl || ''}
                    onChange={handleSourceChange}
                    placeholder={sourceType === 'youtube'
                        ? 'Enter YouTube video URL'
                        : 'Enter direct video URL (.mp4, .webm, .ogg)'
                    }
                    className="w-full p-2 border rounded"
                />
                {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
            </div>

            <div>
                <label className="block mb-2">Video Thumbnail URL:</label>
                <input
                    type="text"
                    value={element.content.thumbnail || ''}
                    onChange={handleThumbnailChange}
                    placeholder="Enter thumbnail image URL"
                    className="w-full p-2 border rounded"
                />
            </div>
        </div>
    );
};

export default VideoProperties;