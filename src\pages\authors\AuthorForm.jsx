// File: src/pages/authors/AuthorForm.jsx
import React from "react";
import { Formik, Form } from "formik";
import { useTranslation } from 'react-i18next';
import * as Yup from "yup";
import { TextField, Select, FileUpload, TextArea } from "@/components/inputs";
const AuthorForm = ({ initialValues, onSubmit, isEditMode = false }) => {
  const { t } = useTranslation();

  const validationSchema = Yup.object({
    name: Yup.string().required(t("authorForm.nameRequired")),
    status: Yup.boolean().required(t("authorForm.statusRequired")),
    photo: Yup.mixed().test(
      "fileValidation",
      t("authorForm.unsupportedFileFormatOrSize"),
      function (value) {
        if (isEditMode && !value) return true;
        if (!value) return this.createError({ message: t("authorForm.photoRequired") });
        if (!["image/jpg", "image/jpeg", "image/png"].includes(value?.type))
          return this.createError({ message: t("authorForm.unsupportedFileFormat") });
        if (value?.size > 1024 * 1024)
          return this.createError({ message: t("authorForm.fileSizeTooLarge") });
        return true;
      }
    ),
    bio: Yup.string().required(t("authorForm.bioRequired")),
  });

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={onSubmit}
    >
      <Form>
        <TextField label={t("authorForm.name")} name="name" type="text" required />
        <Select
          label={t("authorForm.status")}
          name="status"
          options={[
            { value: '1', label: t("authorForm.active") },
            { value: '0', label: t("authorForm.inactive") },
          ]}
        />
        <FileUpload
          name="photo"
          label={t("authorForm.uploadPhoto")}
          accept="image/*"
        />
        <TextArea label={t("authorForm.bio")} name="bio" required />
        <button type="submit" className="bg-blue-500 text-white py-2 px-4 rounded">
          {isEditMode ? t("authorForm.updateAuthor") : t("authorForm.addAuthor")}
        </button>
      </Form>
    </Formik>
  );
};

export default AuthorForm;
