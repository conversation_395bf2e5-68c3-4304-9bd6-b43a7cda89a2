import React from 'react';
import { Trash2, ArrowRightCircle } from 'lucide-react';
import Tooltip from '../../../components/ui/Tooltip';
import Modal from '@/components/ui/Modal';
import { formattedDate } from '../../../configs/dashboardConfig';
import { Icon } from '@iconify/react';
import { useBookmarks } from '../hooks/useBookmarks';

const BookmarkModal = ({ isOpen, onClose, handleJumpToPage }) => {
    // Use our custom hook for bookmark operations
    const { bookmarks, isLoading, deleteBookmark } = useBookmarks();

    const handleDeleteBookmark = (bookmarkId) => {
        deleteBookmark(bookmarkId);
    };

    return (
        <Modal activeModal={isOpen} onClose={onClose} title="Bookmarks" className="max-w-3xl min-h-[400px]">
            {isLoading ? (
                <div className="flex items-center justify-center h-60">
                    <p className="text-gray-500 dark:text-gray-400 text-lg">Loading bookmarks...</p>
                </div>
            ) : !bookmarks || bookmarks.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-[300px] space-y-4">
                    <Icon icon="solar:bookmark-broken" className="w-16 h-16 text-gray-400 dark:text-gray-500" />
                    <p className="text-lg font-medium text-gray-500 dark:text-gray-400">No bookmarks found</p>
                    <p className="text-sm text-gray-400 dark:text-gray-500">Add bookmarks to your favorite pages</p>
                </div>
            ) : (
                <div>
                    <h3 className="mb-4 ml-4">All Bookmarks</h3>
                    <ul className="max-h-[700px] overflow-y-auto overflow-x-hidden pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400">
                        {bookmarks.map((bookmark) => (
                            <li key={bookmark.id} className="flex items-center justify-between mb-4 p-4 border rounded-md">
                                <div className="flex-1">
                                    <p className="font-semibold text-sm text-gray-700 dark:text-gray-300">
                                        Taken on: {formattedDate(bookmark.created_at)}
                                    </p>
                                </div>
                                <div className="flex space-x-2">
                                    <Tooltip text="Jump to Bookmark" position="left">
                                        <button
                                            onClick={() => {
                                                handleJumpToPage(bookmark.page_id);
                                                onClose();
                                            }}
                                            className="text-green-500 hover:text-green-700"
                                        >
                                            <ArrowRightCircle size={24} />
                                        </button>
                                    </Tooltip>
                                    <Tooltip text="Delete Bookmark" position="left">
                                        <button
                                            onClick={() => handleDeleteBookmark(bookmark.id)}
                                            className="text-red-500 hover:text-red-700"
                                        >
                                            <Trash2 size={24} />
                                        </button>
                                    </Tooltip>
                                </div>
                            </li>
                        ))}
                    </ul>
                </div>
            )}
        </Modal>
    );
};

export default BookmarkModal;
