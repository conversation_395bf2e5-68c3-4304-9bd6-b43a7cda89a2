// src/components/Tabs.jsx
import React from 'react';

const Tabs = ({
  tabs = [],
  activeTab,
  onTabChange = () => {},
  variant = 'pills',
  className = ''
}) => {
  const handleTabChange = (idx) => {
    onTabChange(idx);
  };

  const variants = {
    default: {
      container: 'border-b dark:border-gray-600',
      tab: {
        active: 'bg-white text-blue-600 border-b-2 border-blue-600 dark:bg-gray-700 dark:text-blue-500 dark:border-blue-500',
        inactive: 'text-gray-500 hover:text-gray-700 hover:bg-gray-50 dark:text-white dark:hover:text-white dark:hover:bg-gray-600'
      },
      content: 'bg-white shadow-sm dark:bg-gray-800 dark:shadow-gray-800'
    },
    pills: {
      container: 'space-x-2',
      tab: {
        active: 'bg-blue-600 text-white dark:bg-blue-500 dark:text-gray-200',
        inactive: 'text-gray-600 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700'
      },
      content: 'bg-white dark:bg-gray-700'
    },
    minimal: {
      container: 'border-b dark:border-gray-600',
      tab: {
        active: 'text-blue-600 border-b-2 border-blue-600 dark:text-blue-500 dark:border-blue-500',
        inactive: 'text-gray-500 hover:text-gray-700 dark:text-white dark:hover:text-white dark:hover:bg-gray-600'
      },
      content: 'bg-transparent'
    }
  };

  const selectedVariant = variants[variant] || variants.default;

  return (
    <div className={`w-full ${className}`}>
      {/* Tab Headers */}
      <div className={`flex border-b dark:border-gray-600 ${selectedVariant.container}`}>
        {tabs.map((tab, idx) => (
          <button
            key={idx}
            onClick={() => handleTabChange(idx)}
            className={`
              px-4 py-2 text-sm font-medium rounded-t-lg transition-all duration-200 
              ${activeTab === idx 
                ? selectedVariant.tab.active 
                : selectedVariant.tab.inactive
              }
            `}
          >
            {tab.icon && (
              <span className="mr-2">
                {tab.icon}
              </span>
            )}
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="pt-4">
        <div className={`rounded-lg ${selectedVariant.content}`}>
          <div className="prose max-w-none">
            {tabs[activeTab]?.content}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Tabs;
