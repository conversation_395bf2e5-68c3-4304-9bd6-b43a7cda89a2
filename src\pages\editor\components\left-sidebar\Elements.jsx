import React, { useState } from "react";
import { Icon } from "@iconify/react";
import { useDispatch, useSelector } from "react-redux";
import { v4 as uuidv4 } from "uuid";
import Tabs from "@/components/ui/Tabs";
import Sections from "./Sections";
import Modal from "@/components/ui/Modal";
import MyVideos from "./MyVideos";
import MyAudios from "./MyAudios";
import { addElement } from "../../store/pagesSlice";
import useNextElementPosition from "../../../book-preview/hooks/useNextElementPosition";

// <-- Import the custom hook that calculates the next (x,y)

const Elements = () => {
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState(0);
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);
  const [isAudioModalOpen, setIsAudioModalOpen] = useState(false);

  // This hook calculates the next "below all existing elements" position
  const calculateNextPosition = useNextElementPosition();
 // Access the width from the Redux store
  const width = useSelector((state) => state.ebook.width);
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  const handleAddAudio = () => {
    setIsAudioModalOpen(true);
  };

  const handleAddMCQ = () => {
    const position = calculateNextPosition();
    dispatch(
      addElement({
        id: uuidv4(),
        type: "mcq",
        content: {
          question: "Enter your question here",
          options: ["Option 1", "Option 2", "Option 3", "Option 4"],
          correctAnswer: 0,
        },
        position,
        size: { width, height: 300 },
      })
    );
  };

  const handleAddTrueFalse = () => {
    const position = calculateNextPosition();
    dispatch(
      addElement({
        id: uuidv4(),
        type: "true-false",
        content: {
          question: "Enter your question here",
          correctAnswer: true,
        },
        position,
        size: { width, height: 200 },
      })
    );
  };

  const handleAddIndex = () => {
    const position = calculateNextPosition();
    dispatch(
      addElement({
        id: uuidv4(),
        type: "index-list",
        content: {
          items: [
            { page: 1, label: "Introduction" },
            { page: 2, label: "Chapter 1" },
            { page: 3, label: "Chapter 2" },
          ],
        },
        position,
        size: { width, height: 200 },
      })
    );
  };

  const handleAddVideo = () => {
    setIsVideoModalOpen(true);
  };

  const handleAddFlashcard = () => {
    const position = calculateNextPosition();
    dispatch(
      addElement({
        id: uuidv4(),
        type: "flashcard",
        content: {
          frontContent: "Enter the front of the card here",
          backContent: "Enter the back of the card here",
          styleOption: "Basic Flip",
        },
        position,
        size: { width: 400, height: 300 },
      })
    );
  };

  const studyAids = [
    {
      id: 2,
      name: "MCQ",
      icon: "mdi:checkbox-multiple-marked",
      onClick: handleAddMCQ,
    },
    {
      id: 3,
      name: "True/False",
      icon: "mdi:check-circle",
      onClick: handleAddTrueFalse,
    },
    {
      id: 4,
      name: "Flashcard",
      icon: "mdi:card-text",
      onClick: handleAddFlashcard,
    },
    {
      id: 5,
      name: "Video",
      icon: "mdi:video",
      onClick: handleAddVideo,
    },
    {
      id: 6,
      name: "Audio",
      icon: "mdi:audio",
      onClick: handleAddAudio,
    },
    {
      id: 7,
      name: "Table of Contents",
      icon: "gg:list",
      onClick: handleAddIndex,
    },
  ];

  const tabs = [
    {
      label: "Study Aids",
      content: (
        <div className="grid grid-cols-2 gap-4">
          {studyAids.map((aid) => (
            <div
              key={aid.id}
              className="flex flex-col items-center border dark:border-gray-600 py-4 w-full cursor-pointer
                         hover:bg-gray-50 dark:hover:bg-gray-800 dark:bg-gray-800"
              onClick={aid.onClick}
            >
              <Icon icon={aid.icon} className="h-6 w-6 mb-1 dark:text-white" />
              <span className="font-medium text-sm dark:text-white">
                {aid.name}
              </span>
            </div>
          ))}
        </div>
      ),
    },
    {
      label: "Sections",
      content: <Sections />,
    },
  ];

  return (
    <div>
      <Tabs tabs={tabs} activeTab={activeTab} onTabChange={handleTabChange} />

      {/* Video Modal */}
      <Modal
        activeModal={isVideoModalOpen}
        onClose={() => setIsVideoModalOpen(false)}
        title="Videos"
      >
        <MyVideos onClose={() => setIsVideoModalOpen(false)} />
      </Modal>

      {/* Audio Modal */}
      <Modal
        activeModal={isAudioModalOpen}
        onClose={() => setIsAudioModalOpen(false)}
        title="Audios"
      >
        <MyAudios onClose={() => setIsAudioModalOpen(false)} />
      </Modal>
    </div>
  );
};

export default Elements;
