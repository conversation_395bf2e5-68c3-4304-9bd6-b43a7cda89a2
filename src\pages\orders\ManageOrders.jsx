import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import useDataFetching from "@/hooks/useDataFetching";
import DataTable from "@/components/ui/DataTable";
import Select from "react-select";
import api from "@/lib/axios";
import { toast } from "sonner";
import OrderFilter from "./OrderFilter";

const ManageOrders = () => {
  const navigate = useNavigate();
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(20);
  const [search, setSearch] = useState("");
  const [status, setStatus] = useState(null);
  const [paymentStatus, setPaymentStatus] = useState(null);

  const { data, isLoading, refetch } = useDataFetching({
    queryKey: ["orders", page, perPage, search, status, paymentStatus],
    endPoint: "/admin/orders",
    params: {
      page,
      per_page: perPage,
      search,
      status,
      payment_status: paymentStatus,
    },
  });

  const handleSearch = (e) => {
    e.preventDefault();
    setPage(1);
    refetch();
  };

  const handleStatusChange = async (orderId, newStatus) => {
    try {
      await api.post(`/admin/orders/${orderId}/update-status`, { status: newStatus });
      refetch();
    } catch (error) {
      toast.error('Failed to update order status');
    }
  };

  const handleView = (order) => {
    navigate(`${order.id}`);
  };

  const statusOptions = [
    { value: 'pending', label: 'Pending' },
    { value: 'processing', label: 'Processing' },
    { value: 'shipped', label: 'Shipped' },
    { value: 'delivered', label: 'Delivered' },
    { value: 'cancelled', label: 'Cancelled' },
    { value: 'refunded', label: 'Refunded' },
    { value: 'failed', label: 'Failed' }
  ];

  const columns = [
    {
      Header: "Tracking Number",
      accessor: "tracking_number",
    },
    {
      Header: "Customer",
      accessor: "user.name",
    },
    {
      Header: "Total Amount",
      accessor: "total_amount",
      Cell: ({ value }) => `$${parseFloat(value).toFixed(2)}`,
    },
    {
      Header: "Items",
      accessor: "total_quantity",
    },
    {
      Header: "Status",
      accessor: "status",
      Cell: ({ row }) => (
        <Select
          options={statusOptions}
          value={statusOptions.find(
            (option) => option.value === row.original.status
          )}
          onChange={(selected) =>
            handleStatusChange(row.original.id, selected.value)
          }
          className="w-32"
        />
      ),
    },
    {
      Header: "Payment Status",
      accessor: "payment_status",
      Cell: ({ value }) => (
        <span
          className={`px-2 py-1 rounded-full text-xs ${
            value === "paid"
              ? "bg-green-100 text-green-800"
              : "bg-yellow-100 text-yellow-800"
          }`}
        >
          {value ? value.charAt(0).toUpperCase() + value.slice(1) : 'Unknown'}
        </span>
      ),
    },
    {
      Header: "Date",
      accessor: "created_at",
      Cell: ({ value }) => new Date(value).toLocaleDateString(),
    },
    {
      Header: "Actions",
      Cell: ({ row }) => (
        <button
          onClick={() => handleView(row.original)}
          className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600"
        >
          View
        </button>
      ),
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-semibold text-gray-800">
            Manage Orders
          </h1>
        </div>

        <OrderFilter
          search={search}
          onSearchChange={setSearch}
          onSearchSubmit={handleSearch}
          status={status}
          onStatusChange={setStatus}
          paymentStatus={paymentStatus}
          onPaymentStatusChange={setPaymentStatus}
        />

        <DataTable
          title={false}
          columns={columns}
          data={data?.data?.data || []}
          loading={isLoading}
          totalPages={data?.data?.last_page || 1}
          currentPage={page}
          pageSize={perPage}
          onPageChange={(newPage) => setPage(newPage)}
          onPageSizeChange={(newPageSize) => setPerPage(newPageSize)}
        />
      </div>
    </div>
  );
};

export default ManageOrders;
