// ImageBorderProperties.jsx
import React from 'react';

const ImageBorderProperties = ({ element, handleStyleChange }) => (
  <div className="flex flex-col">
    <label className="mb-1 dark:text-white ">Opacity:</label>
    <input
      type="number"
      value={element.style?.opacity || 100}
      onChange={(e) => handleStyleChange('opacity', parseInt(e.target.value, 10))}
      className="w-full px-1 py-0.5 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
    />
  </div>
);

export default ImageBorderProperties;
