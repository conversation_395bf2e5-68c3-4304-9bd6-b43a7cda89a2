import React from 'react';

const ShapeElement = ({ element }) => {
  const shapeType = element.content.type;

  const style = {
    width: element?.size?.width || '100px',
    height: element?.size?.height || '100px',
    backgroundColor: element?.style?.fill || 'red',
    border: `${element?.style?.strokeWidth || 0}px solid ${element?.style?.stroke || 'none'}`,
    borderRadius: element?.style?.borderRadius || '0%',  // Default to a rectangle if no border radius
  };

  switch (shapeType) {
    case 'rectangle':
      return <div style={style}></div>;

    case 'circle':
      return <div style={{ ...style, borderRadius: '50%' }}></div>;

    case 'star':
      return (
        <div
          style={{
            ...style,
            clipPath: 'polygon(50% 0%, 60% 40%, 100% 40%, 70% 60%, 80% 100%, 50% 75%, 20% 100%, 30% 60%, 0% 40%, 40% 40%)',
          }}
        ></div>
      );

    case 'hexagon':
      return (
        <div
          style={{
            ...style,
            clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
          }}
        ></div>
      );

    case 'triangle':
      return (
        <div
          style={{
            ...style,
            width: '0',
            height: '0',
            borderLeft: '50px solid transparent',
            borderRight: '50px solid transparent',
            borderBottom: '100px solid ' + (element?.style?.fill || 'red'),
          }}
        ></div>
      );

    default:
      return null;
  }
};

export default ShapeElement;
