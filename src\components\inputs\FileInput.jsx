import { useEffect, useState } from 'react';
import { useField } from 'formik';
import { useDropzone } from 'react-dropzone';
import { X } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const baseURL = import.meta.env.VITE_HOST_URL;

export const FileInput = ({ label, accept, accepts, multiple = false, ...props }) => {
    const { t } = useTranslation();
    const [field, meta, helpers] = useField(props);
    const [preview, setPreview] = useState(null);
    const [formatError, setFormatError] = useState('');

    const onDrop = (acceptedFiles) => {
        if (!acceptedFiles.length) return;
        const file = acceptedFiles[0];
        if (accepts && !file.type.match(accepts)) {
            setFormatError(`Only ${accepts} file types are allowed.`);
            helpers.setValue(null);
            setPreview(null);
            return;
        }
        setFormatError('');
        helpers.setValue(file);
    };

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        multiple,
        accept
    });

    const removeFile = () => {
        helpers.setValue(null);
        setPreview(null);
        setFormatError('');
    };

    useEffect(() => {
        if (typeof field.value === 'string' && field.value !== '') {
            setPreview(`${baseURL}/storage/${field.value}`);
        } else if (field.value instanceof File) {
            const objectUrl = URL.createObjectURL(field.value);
            setPreview(objectUrl);
            return () => URL.revokeObjectURL(objectUrl);
        } else {
            setPreview(null);
        }
    }, [field.value]);

    const renderPreview = () => {
        if (!preview) return null;
        const isPDF = preview.toLowerCase().includes('.pdf') || field.value?.type === 'application/pdf';
        return (
            <div className="relative p-2 bg-gray-100 rounded mt-2">
                {isPDF ? (
                    <embed src={preview} type="application/pdf" width="100%" height="200px" className="rounded" />
                ) : (
                    <img src={preview} alt="Preview" className="h-40 object-contain mx-auto" />
                )}
                <button
                    type="button"
                    onClick={removeFile}
                    className="absolute top-1 right-1 bg-white rounded-full p-1 text-red-600 shadow"
                >
                    <X size={18} />
                </button>
            </div>
        );
    };

    return (
        <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
                {label}
            </label>
            {!preview && (
                <div
                    {...getRootProps()}
                    className={`min-h-[150px] flex flex-col items-center justify-center border-2 border-dashed rounded-lg p-4 text-center cursor-pointer
                    ${isDragActive ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 dark:border-gray-600'}`}
                >
                    <input {...getInputProps()} />
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                        {isDragActive ? t('fileInput.dragActive') : t('fileInput.dragInactive')}
                    </p>
                </div>
            )}
            {renderPreview()}
            {(meta.touched && meta.error) || formatError ? (
                <div className="text-red-600 dark:text-red-400 text-sm mt-1">
                    {meta.error || formatError}
                </div>
            ) : null}
        </div>
    );
};
