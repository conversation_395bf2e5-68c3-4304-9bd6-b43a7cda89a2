// src/redux/slices/scaleSlice.js
import { createSlice } from '@reduxjs/toolkit';
import { PAGE_HEIGHT, PAGE_WIDTH } from '@/configs/pageConfig';

const initialState = {
  size: { width: PAGE_WIDTH, height: PAGE_HEIGHT },
  scale: 1,
  zoomMode: false,
  defaultScale: 1,
};

const scaleSlice = createSlice({
  name: 'scale',
  initialState,
  reducers: {
    calculateScale: (state, action) => {
      const { elementWidth, elementHeight } = action.payload || {};
      const safeElementWidth = elementWidth > 0 ? elementWidth : PAGE_WIDTH;
      const safeElementHeight = elementHeight > 0 ? elementHeight : PAGE_HEIGHT;
      const safeInnerWidth = window.innerWidth || PAGE_WIDTH;
      const safeInnerHeight = window.innerHeight || PAGE_HEIGHT;

      const availableWidth = safeInnerWidth > 20 ? safeInnerWidth - 20 : safeInnerWidth;
      const availableHeight = safeInnerHeight > 90 ? safeInnerHeight - 90 : safeInnerHeight;

      const scaleX = availableWidth / safeElementWidth;
      const scaleY = availableHeight / safeElementHeight;

      state.scale = Math.min(scaleX, scaleY) || 1;
    },
    setScale: (state, action) => {
      state.scale = action.payload;
      state.size = {
        width: PAGE_WIDTH * state.scale,
        height: PAGE_HEIGHT * state.scale,
      };
      state.zoomMode = state.scale !== state.defaultScale;
    },
    resetScale: (state) => {
      const safeInnerWidth = window.innerWidth || PAGE_WIDTH;
      const safeInnerHeight = window.innerHeight || PAGE_HEIGHT;

      const availableWidth = safeInnerWidth > 20 ? safeInnerWidth - 20 : safeInnerWidth;
      const availableHeight = safeInnerHeight > 90 ? safeInnerHeight - 90 : safeInnerHeight;
      const aspectRatio = PAGE_WIDTH / PAGE_HEIGHT;

      let newWidth, newHeight;
      if (availableWidth / aspectRatio <= availableHeight) {
        newWidth = availableWidth;
        newHeight = availableWidth / aspectRatio;
      } else {
        newHeight = availableHeight;
        newWidth = availableHeight * aspectRatio;
      }

      const scaleFactor = Math.min(newWidth / PAGE_WIDTH, newHeight / PAGE_HEIGHT) || 1;
      state.scale = scaleFactor;
      state.size = { width: newWidth, height: newHeight };
      state.defaultScale = scaleFactor;
      state.zoomMode = false;
    },
    toggleZoomMode: (state, action) => {
      state.zoomMode = action.payload;
    },
  },
});

export const {
  setScale,
  resetScale,
  toggleZoomMode,
  calculateScale
} = scaleSlice.actions;

export default scaleSlice.reducer;
