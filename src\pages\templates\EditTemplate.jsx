// Editor.jsx

import React, { useRef, useEffect } from "react";
import { Icon } from "@iconify/react";
import Topbar from "../editor/components/Topbar";
import Sidebar from "../editor/components/LeftSidebar";
import RightSidebar from "../editor/components/RightSidebar";
import Page from "../editor/components/Page";
import { useDispatch, useSelector } from "react-redux";
import { setCurrentPage, setPages, setScale } from "../editor/store/pagesSlice";
import useDataFetching from "@/hooks/useDataFetching";
import { useParams } from "react-router-dom";
import api from "@/lib/axios";
import { persistor } from "@/store/store";
import Loading from "@/components/Loading";
import { ActionCreators } from 'redux-undo';
import { v4 as uuidv4 } from 'uuid'; // Ensure uuidv4 is imported

export default function Editor() {
  const dispatch = useDispatch();
  
  // Selectors accessing `present` state
  const pages = useSelector((state) => state.pages.present.pages);
  const currentPage = useSelector((state) => state.pages.present.currentPage);
  const scale = useSelector((state) => state.pages.present.scale);
  const isPreview = useSelector((state) => state.toolbar.isPreviewActive); // Assuming toolbar is another slice
  const pageRefs = useRef([]);

  // Fetch eBooks data
  const { id } = useParams();
  const { data: ebooksData, isLoading: ebooksLoading } = useDataFetching({
    queryKey: ['ebooksData', id],
    endPoint: `admin/templates/${id}`,
  });

  // Initialize pages from fetched data
  useEffect(() => {
    if (ebooksData?.data?.pages) {
      const formattedPages = ebooksData.data.pages.map(page => ({
        ...page,
        id: page.id || uuidv4(), // Ensure each page has an ID
        elements: page.elements || [],
        style: typeof page.style === 'string' && page.style.startsWith('{') ? JSON.parse(page.style) : page.style || {},
        name: page.name || `Page ${pageIndex + 1}`, // Optional: Add a default name if not present
      }));
      dispatch(setPages(formattedPages));
    }
  }, [ebooksData, dispatch]);

  // Persisted Purge on Mount

  // Auto-update page on elements change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (pages[currentPage]) {
        const pageToUpdate = { 
          ...pages[currentPage], 
          style: JSON.stringify(pages[currentPage].style) 
        };
        api.put(`admin/template-pages/${pages[currentPage]?.id}`, pageToUpdate, { showToast: false });
      }
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [pages, currentPage]);

  // Handle Page Selection
  const handlePageSelect = (index) => {
    dispatch(setCurrentPage(index));
    if (pageRefs.current[index]) {
      pageRefs.current[index].scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Handle Undo and Redo
  useEffect(() => {
    const handleKeyDown = (e) => {
      // For Windows/Linux and Mac
      const isCtrlOrCmd = e.ctrlKey || e.metaKey;

      if (isCtrlOrCmd && e.key === 'z') {
        e.preventDefault();
        dispatch(ActionCreators.undo());
      }
      if (isCtrlOrCmd && (e.key === 'y' || (isCtrlOrCmd && e.shiftKey && e.key === 'z'))) {
        e.preventDefault();
        dispatch(ActionCreators.redo());
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    // Cleanup on unmount
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [dispatch]);

  if (ebooksLoading) {
    return <Loading />;
  }

  // Ensure pages are loaded
  if (!pages || pages.length === 0) {
    return <div>No pages available.</div>;
  }

  return (
    <div className="flex flex-col h-screen bg-gray-100 dark:bg-gray-900">
      {/* Topbar */}
      <Topbar />

      {/* Main Content */}
      <div className="flex flex-grow overflow-hidden">
        {/* Left Sidebar */}
        {!isPreview && <Sidebar />}

        {/* Pages Container */}
        <div className="flex-1 relative overflow-auto" style={{ scrollbarWidth: 'thin' }}>
          <div
            className="flex flex-col items-center"
            style={{
              transform: `scale(${scale})`,
              transformOrigin: "top center",
              display: "flex",
              alignItems: "center",
            }}
          >
            {pages.map((page, index) => (
              <div
                key={page.id}
                ref={(el) => (pageRefs.current[index] = el)}
                className={`m-6 mb-4 ${index === currentPage
                  ? "border border-blue-500 shadow-md"
                  : "shadow-lg"
                  } cursor-pointer`}
                onClick={() => dispatch(setCurrentPage(index))}
              >
                <Page 
                  key={page.id} 
                  page={page} 
                  index={index} 
                  height={ebooksData?.data?.height} 
                  width={ebooksData?.data?.width} 
                />
              </div>
            ))}
          </div>
        </div>

        {/* Right Sidebar */}
        {!isPreview && <RightSidebar handlePageSelect={handlePageSelect} />}
      </div>

      {/* Scale Controller */}
      <div className="absolute bottom-4 right-4 flex items-center space-x-2 bg-white p-2 rounded shadow">
        <Icon icon="mdi:magnify" />
        <input
          type="range"
          min="0.5"
          max="2"
          step="0.1"
          value={scale}
          onChange={(e) => dispatch(setScale(parseFloat(e.target.value)))}
          className="w-48"
        />
        <span>{(scale * 100).toFixed(0)}%</span>
      </div>
    </div>
  );
}
