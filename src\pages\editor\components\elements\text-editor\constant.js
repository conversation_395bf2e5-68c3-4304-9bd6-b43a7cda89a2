export const FONT_SIZES = [8, 9, 10, 11, 12, 14, 16, 18, 22]

export const HEADING_STYLES = `
  h1 { font-size: 2.5em; font-weight: bold; }
  h2 { font-size: 2em; font-weight: bold; }
  h3 { font-size: 1.5em; font-weight: bold;}
  h4 { font-size: 1.25em; font-weight: bold; }
  h5 { font-size: 1.1em; font-weight: bold; }
`

export const EDITOR_CONFIG = {
  menubar: false,
  forced_root_block: "div",
  toolbar_mode: "wrap",
  block_formats: "Paragraph=p; Heading 1=h1; Heading 2=h2; Heading 3=h3; Heading 4=h4; Heading 5=h5",
  font_family_formats:
    "Andale Mono=andale mono,times; Arial=arial,helvetica,sans-serif; Arial Black=arial black,avant garde; Book Antiqua=book antiqua,palatino; Comic Sans MS=comic sans ms,sans-serif; Courier New=courier new,courier; Georgia=georgia,palatino; Helvetica=helvetica; Impact=impact,chicago; Kalpurush=kalpurush; SolaimanLipi=SolaimanLipi; FN Himu Regular=FN Himu Regular; NotoSerifBengali=NotoSerifBengali; Symbol=symbol; Tahoma=tahoma,arial,helvetica,sans-serif; Terminal=terminal,monaco; Times New Roman=times new roman,times; Trebuchet MS=trebuchet ms,geneva; Verdana=verdana,geneva; Webdings=webdings; Wingdings=wingdings,zapf dingbats",
  line_height_formats: "1 1.2 1.5 1.75 2 2.5 3",
  image_advtab: true,
  paste_data_images: true,
  paste_retain_style_properties: "all",
  paste_webkit_styles: "all",
  paste_remove_styles_if_webkit: false,
  paste_merge_formats: true,
  visual: true,
  link_assume_external_targets: true,
  default_link_target: '_blank',
  link_rel: 'noopener',
  link_auto_unlink: true,
  plugins: [
    "advlist",
    "autolink",
    "lists",
    "link",
    "image",
    "charmap",
    "anchor",
    "searchreplace",
    "visualblocks",
    "code",
    "fullscreen",
    "insertdatetime",
    "media",
    "table",
    "preview",
    "paste",
    "lineheight"
  ],
  extended_valid_elements:
    "svg[*],path[*],polyline[*],span[*],div[*],p[*],strong[*],em[*],u[*],strike[*],font[*],li[*],ol[*],ul[*],table[*],thead[*],tbody[*],tr[*],td[*],th[*],h1[*],h2[*],h3[*],h4[*],h5[*],h6[*]",
  valid_children: "+body[svg]"
}
