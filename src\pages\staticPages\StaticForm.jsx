// File: src/pages/authors/AuthorForm.jsx
import React from "react";
import { Formik, Form } from "formik";
import { useTranslation } from "react-i18next";
import * as Yup from "yup";
import {
  TextField,
  Select,
  FileUpload,
  TextArea,
} from "@/components/inputs";
import TextEditor from "@/components/inputs/TextEditor";
const StaticForm = ({ initialValues, onSubmit, isEditMode = false }) => {
  const { t } = useTranslation();

  const validationSchema = Yup.object({
    title: Yup.string().required(t("authorForm.nameRequired")),
    status: Yup.boolean().required(t("authorForm.statusRequired")),

    // bio: Yup.string().required(t("authorForm.bioRequired")),
  });

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={onSubmit}
    >
      <Form>
        <TextField label={t("title")} name="title" type="text" required />
        <TextField
          label={t("staticForm.slug")}
          name="slug"
          type="text"
          required
        />
        <Select
          label={t("authorForm.status")}
          name="status"
          options={[
            { value: "1", label: t("authorForm.active") },
            { value: "0", label: t("authorForm.inactive") },
          ]}
        />

        <div className="">
          <TextEditor
            label={t("staticForm.content")}
            name="content"
            // placeholder={t("staticForm.contentPlaceholder")}
            rows={5}
          />
        </div>
        <button
          type="submit"
          className="bg-blue-500 text-white py-2 px-4 rounded"
        >
          {isEditMode ? t("static.editStatic") : t("static.addStaticPage")}
        </button>
      </Form>
    </Formik>
  );
};

export default StaticForm;
