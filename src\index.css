@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;
@font-face {
  font-family: 'Kalpurush';
  src: url('/fonts/Kalpurush.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'SolaimanLipi_20-04-07';
  src: url('/fonts/SolaimanLipi_20-04-07.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'FN Himu Regular';
  src: url('/fonts/FN Himu Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'NotoSerifBengali-VariableFont_wdth,wght';
  src: url('/fonts/NotoSerifBengali-VariableFont_wdth,wght.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
.mce-edit-focus {
    outline: none !important;
    box-shadow: none !important;
  }
  ul {
    list-style-type: disc;
    margin: 0;
    padding: 0;
  }

  ol {
    list-style-type: decimal;
    margin: 0;
    padding: 0;
  }

  ul li, ol li {
    margin-left: 20px;
    padding-left: 5px;
  }
  .flashcard-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .flashcard {
    width: 100%;
    height: 100%;
    position: relative;
    cursor: pointer;
    margin: 0 auto;
  }
  
  /* Basic Flip Card */
  .basic-flip {
    perspective: 1000px;
  }
  
  .basic-flip .card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    transition: transform 0.6s;
    transform-style: preserve-3d;
  }
  
  .basic-flip.flipped .card-inner {
    transform: rotateY(180deg);
  }
  
  .basic-flip .front,
  .basic-flip .back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  .basic-flip .front {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
  }
  
  .basic-flip .back {
    background: white;
    transform: rotateY(180deg);
    border: 2px solid #667eea;
  }
  
  /* Fade Card */
  .fade-card .front,
  .fade-card .back {
    position: absolute;
    width: 100%;
    height: 100%;
    transition: opacity 0.5s ease;
    border-radius: 15px;
    padding: 1.5rem;
  }
  
  .fade-card .front {
    background: linear-gradient(135deg, #ff6b6b, #feca57);
    opacity: 1;
  }
  
  .fade-card.flipped .front {
    opacity: 0;
  }
  
  .fade-card .back {
    background: white;
    border: 2px solid #ff6b6b;
    opacity: 0;
  }
  
  .fade-card.flipped .back {
    opacity: 1;
  }
  
  /* Slide Card */
  .slide-card {
    overflow: hidden;
  }
  
  .slide-card .front,
  .slide-card .back {
    position: absolute;
    width: 100%;
    height: 100%;
    transition: transform 0.5s ease;
    border-radius: 15px;
    padding: 1.5rem;
  }
  
  .slide-card .front {
    background: linear-gradient(135deg, #20bf6b, #0fb9b1);
    transform: translateX(0);
  }
  
  .slide-card.flipped .front {
    transform: translateX(-100%);
  }
  
  .slide-card .back {
    background: white;
    border: 2px solid #20bf6b;
    transform: translateX(100%);
  }
  
  .slide-card.flipped .back {
    transform: translateX(0);
  }
  
  /* 3D Flip Card */
  .flip-3d {
    perspective: 1500px;
  }
  
  .flip-3d .card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    transition: transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform-style: preserve-3d;
  }
  
  .flip-3d .front,
  .flip-3d .back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 15px;
    padding: 1.5rem;
  }
  
  .flip-3d .front {
    background: linear-gradient(135deg, #fc5c7d, #6a82fb);
  }
  
  .flip-3d .back {
    background: white;
    border: 2px solid #fc5c7d;
    transform: rotateY(180deg);
  }
  
  .flip-3d.flipped .card-inner {
    transform: rotateY(180deg);
  }
  
  /* Tilt Card */
  .tilt-card {
    perspective: 1000px;
  }
  
  .tilt-card .card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    transition: transform 0.6s;
    transform-style: preserve-3d;
  }
  
  .tilt-card.flipped .card-inner {
    transform: rotateX(180deg);
  }
  
  .tilt-card .front,
  .tilt-card .back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  
  .tilt-card .front {
    background: linear-gradient(135deg, #4834d4, #686de0);
  }
  
  .tilt-card .back {
    background: white;
    border: 2px solid #4834d4;
    transform: rotateX(180deg);
  }
  
  /* Common styles */
  .card-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .front .card-content {
    color: white;
    font-size: 1.25rem;
    font-weight: bold;
  }
  
  .back .card-content {
    color: #2d3436;
    font-size: 1rem;
    line-height: 1.5;
  }

  .tox-tinymce {
    position: absolute !important;
     /* Center vertically */
    top: 5px !important;
    /* Center horizontally */
    left: 115px !important; 
    /* Center horizontally */
    right: -50% !important; 
    transform: translate(-50%, -0%) !important; /* Adjust for exact centering */
    width: 90% !important; /* Adjust width as needed */
    max-width: 950px !important; /* Set a max-width to prevent overflow */
    margin: 0 auto !important; /* Center the editor */
  }
  .tox-editor-header{
    top: 5px !important;
    left: 0px !important; 

  }
  
  .tox-toolbar__group {
    flex-wrap: wrap !important; /* Allow toolbar items to wrap */
    justify-content: center !important; /* Center toolbar items */
  }
  
  .tox-toolbar__primary {
    justify-content: center !important; /* Center the primary toolbar */
    gap: 8px !important; /* Add spacing between toolbar items */
  }


  /* .tox-tinymce {
    position: absolute !important;
    top: 60% !important;
    right: 0% !important; 
    width: 90% !important; 
    max-width: 350px !important;
    margin: 0 auto !important; 
  } */
  /* .tox-editor-header{
    top: 5px !important;
    left: 0px !important; 

  } */
 
  
  /* .tox-toolbar__group {
    flex-wrap: wrap !important; 
    justify-content: center !important;
  } */
  
  /* .tox-toolbar__primary {
    justify-content: center !important;
    gap: 8px !important; 
  } */
  /* Add this to your existing CSS */
/* .tox-tinymce-inline {
  right: 0px !important;
  left: auto !important;
} */

/* This will target the specific element with the inline style */
/* .tox-tinymce-inline[style] {
  right: 0px !important;
  left: auto !important;
} */


/* ============================================ */
/* Inner Shadow CSS for react-pageflip        */
/* (Final Version - Using --left/--right)     */
/* ============================================ */

/* Base styles applied via specific class selectors */
/* No separate base rule needed now */

/* Inner shadow for the LEFT page */
/* Targets the element with --left class */
/* Applies shadow to its RIGHT edge (inner edge) */
.double-page-view .stf__item.--left::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 30px; /* ADJUST: How far the shadow spreads */
  z-index: 100; /* Keep high enough */
  pointer-events: none; /* Don't block interaction */
  box-shadow: none; /* Ensure no leftover box-shadow */

  /* --- Position & Style --- */
  right: 0; /* Position shadow on the RIGHT edge */
  background: linear-gradient(to left, rgba(0, 0, 0, 0.15) 0%, transparent 75%); /* ADJUST: Opacity (0.2) */
}

/* Inner shadow for the RIGHT page */
/* Targets the element with --right class */
/* Applies shadow to its LEFT edge (inner edge) */
.double-page-view .stf__item.--right::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 30px; /* ADJUST: How far the shadow spreads */
  z-index: 100; /* Keep high enough */
  pointer-events: none; /* Don't block interaction */
  box-shadow: none; /* Ensure no leftover box-shadow */

  /* --- Position & Style --- */
  left: 0; /* Position shadow on the LEFT edge */
  background: linear-gradient(to right, rgba(0, 0, 0, 0.15) 0%, transparent 75%); /* ADJUST: Opacity (0.2) */
}