import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { Edit3, Settings } from 'lucide-react';
import { updateElement } from '../store/pagesSlice'; // adjust the import path if needed

const AiButtons = ({ initialMessage, id }) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleSendMessage = async (improveOnly = false) => {
    setLoading(true);
    setError(null);

    const systemPrompt = improveOnly
      ? 'You are a helpful assistant. Only improve the grammar and style of the HTML content without changing its structure, and respond with the HTML code only. Do not add any extra description or commentary.'
      : 'You are a helpful assistant. Generate an HTML code section with a <section>, <h2> heading, and a <p> description based on the topic. Include appropriate inline styling. Respond with only the HTML code.';

    const url = "https://generativelanguage.googleapis.com/v1beta2/models/gemini-1.5:generateText"; // Replace with the actual Google API endpoint

    const payload = JSON.stringify({
      prompt: improveOnly
        ? `Improve the following HTML content for grammar and style:\n\n${initialMessage}`
        : `Generate an HTML section with a <section>, <h2> heading, and a <p> description based on the following topic:\n\n${initialMessage}`,
      maxTokens: 150,
      temperature: 0.7,
    });

    try {
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Authorization": `Bearer AIzaSyAfskGobi9zkPDzQgolymArkKFpU6zyWOY`, // Secure API key in environment variable
          "Content-Type": "application/json",
        },
        body: payload,
      });

      if (!response.ok) throw new Error("Failed to fetch response from AI");

      const data = await response.json();
      const aiMessage = data.candidates[0]?.output || "No valid HTML content found";

      // Extract only the HTML part
      const htmlMatch = aiMessage.match(/<section[\s\S]*<\/section>/);
      const htmlContent = htmlMatch ? htmlMatch[0] : "No valid HTML content found";

      // Dispatch the updateElement action to Redux
      dispatch(updateElement({ id, updates: { content: htmlContent } }));
    } catch (err) {
      setError(err.message);
      console.error("Error fetching response:", err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <div className="flex items-center mb-2 space-x-4">
        <button
          onClick={() => handleSendMessage(true)}
          className="flex items-center p-2 bg-green-500 text-white rounded-lg hover:bg-green-600"
          disabled={loading}
        >
          <Edit3 size={16} />
        </button>
        <button
          onClick={() => handleSendMessage(false)}
          className="flex items-center p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          disabled={loading}
        >
          <Settings size={16} />
        </button>
      </div>
      {loading && <div className="text-center text-gray-500">Loading...</div>}
      {error && <div className="text-red-500 mt-4">{error}</div>}
    </>
  );
};

export default AiButtons;
