import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import LocalStorageBackend from 'i18next-localstorage-backend';

import enTranslation from './locales/en.json';
import bnTranslation from './locales/bn.json';
import arTranslation from './locales/ar.json';
import koTranslation from './locales/ko.json'; // Import Korean translation
import jaTranslation from './locales/ja.json'; // Import Japanese translation

i18n
  .use(LocalStorageBackend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    fallbackLng: 'en',
    debug: true,
    resources: {
      en: {
        translation: enTranslation,
      },
      bn: {
        translation: bnTranslation,
      },
      ar: {
        translation: arTranslation,
      },
      ko: { // Add Korean translation
        translation: koTranslation,
      },
      ja: { // Add Japanese translation
        translation: jaTranslation,
      },
    },
    interpolation: {
      escapeValue: false, // not needed for react as it escapes by default
    },
  });

export default i18n;
