import React from 'react';
import { Link2, Unlink } from "lucide-react";

const CommonProperties = ({ style, handleStyleChange }) => {
  const [linked, setLinked] = React.useState(true);

  const toggleLink = () => setLinked(!linked);

  return (
    <div>
      <div className="space-y-4 mt-6">
        <div className="flex flex-col">
          <label className="mb-1 dark:text-white">Padding:</label>
          <div className="flex items-center justify-between mb-4">
            <div className="grid grid-cols-4 gap-4 w-full">
              <div className="flex flex-col">
                <label className="mb-1 dark:text-white">Top:</label>
                <input
                  type="number"
                  value={style.paddingTop}
                  onChange={(e) => handleStyleChange('paddingTop', parseInt(e.target.value, 10))}
                  className="w-full px-1 py-0.5 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="flex flex-col">
                <label className="mb-1 dark:text-white">Left:</label>
                <input
                  type="number"
                  value={style.paddingLeft}
                  onChange={(e) => handleStyleChange('paddingLeft', parseInt(e.target.value, 10))}
                  className="w-full px-1 py-0.5 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="flex flex-col">
                <label className="mb-1 dark:text-white">Bottom:</label>
                <input
                  type="number"
                  value={style.paddingBottom}
                  onChange={(e) => handleStyleChange('paddingBottom', parseInt(e.target.value, 10))}
                  className="w-full px-1 py-0.5 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="flex flex-col">
                <label className="mb-1 dark:text-white">Right:</label>
                <input
                  type="number"
                  value={style.paddingRight}
                  onChange={(e) => handleStyleChange('paddingRight', parseInt(e.target.value, 10))}
                  className="w-full px-1 py-0.5 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            <div className="ml-4 mt-7">
              <button
                onClick={toggleLink}
                className={`p-1 rounded-full border ${linked ? "text-blue-500" : "text-white"} focus:outline-none focus:ring-2 focus:ring-blue-500`}
              >
                {linked ? <Link2 className="w-4 h-4" /> : <Unlink className="w-4 h-4" />}
              </button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <label className="mb-1 dark:text-white">Background Color:</label>
          <input
            type="color"
            value={style?.backgroundColor || '#ffffff'}
            onChange={(e) => handleStyleChange('backgroundColor', e.target.value)}
            className="w-full h-8 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Border Section */}
        <div className="flex flex-col mb-4">
          <label className="mb-1 dark:text-white">Border:</label>
          <div className="flex items-center gap-4">
            <input
              type="number"
              value={style?.borderWidth || 1}
              onChange={(e) => handleStyleChange('borderWidth', parseInt(e.target.value, 10))}
              className="w-16 px-1 py-0.5 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <select
              value={style?.borderStyle || 'solid'}
              onChange={(e) => handleStyleChange('borderStyle', e.target.value)}
              className="px-2 py-0.5 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="solid">Solid</option>
              <option value="dashed">Dashed</option>
              <option value="dotted">Dotted</option>
              <option value="double">Double</option>
              <option value="groove">Groove</option>
              <option value="ridge">Ridge</option>
              <option value="inset">Inset</option>
              <option value="outset">Outset</option>
            </select>
            <input
              type="color"
              value={style?.borderColor || '#000000'}
              onChange={(e) => handleStyleChange('borderColor', e.target.value)}
              className="w-16 h-8 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Border Radius Section */}
        <div className="flex flex-col mb-4">
          <label className="mb-1 dark:text-white">Border Radius:</label>
          <div className="flex items-center gap-4">
            <input
              type="number"
              value={style?.borderRadius || 0}
              onChange={(e) => handleStyleChange('borderRadius', parseInt(e.target.value, 10))}
              className="w-16 px-1 py-0.5 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <span className="text-sm dark:text-white">px</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CommonProperties;
