import React from 'react';

const AudioProperties = ({ element, handleUpdate }) => {
  const handleSourceChange = (e) => {
    handleUpdate({ 
      content: { 
        ...element.content, 
        audioUrl: e.target.value 
      } 
    });
  };

  const handleStyleChange = (property, value) => {
    handleUpdate({
      style: {
        ...element.style,
        [property]: value
      }
    });
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-200">
          Audio Source
        </label>
        <input
          type="text"
          value={element.content.audioUrl || ''}
          onChange={handleSourceChange}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-200">
          Player Style
        </label>
        <select 
          value={element.style?.variant || 'default'}
          onChange={(e) => handleStyleChange('variant', e.target.value)}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
        >
          <option value="default">Default</option>
          <option value="minimal">Minimal</option>
          <option value="custom">Custom</option>
        </select>
      </div>
    </div>
  );
};

export default AudioProperties;