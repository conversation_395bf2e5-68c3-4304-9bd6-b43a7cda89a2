import React from 'react';
import Select from 'react-select';

const OrderFilter = ({
  search,
  onSearchChange,
  onSearchSubmit,
  status,
  onStatusChange,
  paymentStatus,
  onPaymentStatusChange
}) => {
  const handleClearFilters = () => {
    onSearchChange('');
    onStatusChange(null);
    onPaymentStatusChange(null);
  };

  const statusOptions = [
    { value: 'pending', label: 'Pending' },
    { value: 'processing', label: 'Processing' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  const paymentStatusOptions = [
    { value: 'pending', label: 'Pending' },
    { value: 'paid', label: 'Paid' },
    { value: 'failed', label: 'Failed' }
  ];

  const customStyles = {
    control: (base) => ({
      ...base,
      backgroundColor: 'white',
      borderColor: '#D1D5DB',
      boxShadow: 'none',
      '&:hover': {
        borderColor: '#3B82F6',
      },
    }),
    menu: (base) => ({
      ...base,
      backgroundColor: 'white',
      border: '1px solid #D1D5DB',
    }),
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isSelected
        ? '#3B82F6'
        : state.isFocused
          ? '#F3F4F6'
          : 'transparent',
      color: state.isSelected ? 'white' : '#1F2937',
      '&:hover': {
        backgroundColor: state.isSelected ? '#3B82F6' : '#F3F4F6',
      },
    }),
  };

  return (
    <div className="flex flex-col md:flex-row gap-4 mb-6 w-full">
      <div className="w-full md:w-1/3">
        <label className="block text-sm font-medium mb-1 dark:text-gray-200">
          Search Orders
        </label>
        <form onSubmit={onSearchSubmit} className="flex items-center gap-2">
          <input
            type="text"
            placeholder="Search by tracking number or customer name..."
            value={search}
            onChange={(e) => onSearchChange(e.target.value)}
            className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </form>
      </div>

      <div className="w-full md:w-1/3">
        <label className="block text-sm font-medium mb-1 dark:text-gray-200">
          Status
        </label>
        <Select
          options={statusOptions}
          value={statusOptions.find(option => option.value === status)}
          onChange={(selected) => onStatusChange(selected?.value || null)}
          styles={customStyles}
          isClearable
        />
      </div>

      <div className="w-full md:w-1/3">
        <label className="block text-sm font-medium mb-1 dark:text-gray-200">
          Payment Status
        </label>
        <Select
          options={paymentStatusOptions}
          value={paymentStatusOptions.find(option => option.value === paymentStatus)}
          onChange={(selected) => onPaymentStatusChange(selected?.value || null)}
          styles={customStyles}
          isClearable
        />
      </div>

      {(search || status || paymentStatus) && (
        <button
          type="button"
          onClick={handleClearFilters}
          className="md:self-end p-2 text-gray-500 hover:text-gray-700 focus:outline-none mb-2 md:mb-0"
          title="Clear filters"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      )}
    </div>
  );
};

export default OrderFilter;