import React from 'react';
import { SelectWithoutFormik } from '@/components/inputs/SelectWithoutFormik';
import { Icon } from '@iconify/react';
import Select from 'react-select';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';

const EbookFilter = ({ authorId, publisherId, onAuthorChange, onPublisherChange, search, onSearchChange, onSearchSubmit, bookType, onBookTypeChange, status, onStatusChange, approvalStatus, onApprovalStatusChange }) => {
  const role = useSelector((state) => state.auth?.user?.role);
  const { t } = useTranslation();

  const handleClearFilters = () => {
    onAuthorChange(null);
    onPublisherChange(null);
    onSearchChange('');
    onBookTypeChange(null);
    onStatusChange(null);
    onApprovalStatusChange(null);
  };

  const bookTypeOptions = [
    { value: 'ebook', label: 'eBook' },
    { value: 'pdf', label: 'PDF' }
  ];

  const statusOptions = [
    { value: 'draft', label: 'Draft' },
    { value: 'published', label: 'Published' },
    { value: 'archived', label: 'Archived' }
  ];

  const approvalStatusOptions = [
    { value: 'pending', label: 'Pending' },
    { value: 'approved', label: 'Approved' },
    { value: 'rejected', label: 'Rejected' }
  ];

  const customStyles = {
    control: (base) => ({
      ...base,
      backgroundColor: 'white',
      borderColor: '#D1D5DB',
      boxShadow: 'none',
      '&:hover': {
        borderColor: '#3B82F6',
      },
    }),
    menu: (base) => ({
      ...base,
      backgroundColor: 'white',
      border: '1px solid #D1D5DB',
    }),
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isSelected
        ? '#3B82F6'
        : state.isFocused
          ? '#F3F4F6'
          : 'transparent',
      color: state.isSelected ? 'white' : '#1F2937',
      '&:hover': {
        backgroundColor: state.isSelected ? '#3B82F6' : '#F3F4F6',
      },
    }),
  };

  return (
    <div className="flex flex-col md:flex-row gap-4 mb-6 w-full">
      <div className="w-full md:w-1/4">
        <label className="block text-sm font-medium mb-1 dark:text-gray-200">
          {t("Search eBooks")}
        </label>
        <form onSubmit={onSearchSubmit} className="flex items-center gap-2">
          <input
            type="text"
            placeholder="Search ebooks..."
            value={search}
            onChange={(e) => onSearchChange(e.target.value)}
            className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </form>
      </div>
      {role === 'admin' && (
        <>
          <div className="w-full md:w-1/4">
            <SelectWithoutFormik
              label={t("Author")}
              name="author_filter"
              isMulti={false}
              endPoint="authors?pagination=false"
              queryKey="authors_filter"
              valueKey="id"
              labelKey="name"
              dataAccessKey="data"
              value={authorId}
              onChange={(selected) => onAuthorChange(selected?.value || null)}
            />
          </div>
          <div className="w-full md:w-1/4">
            <SelectWithoutFormik
              label={t("Publisher")}
              name="publisher_filter"
              isMulti={false}
              endPoint="publishers?pagination=false"
              queryKey="publishers_filter"
              valueKey="id"
              labelKey="name"
              dataAccessKey="data"
              value={publisherId}
              onChange={(selected) => onPublisherChange(selected?.value || null)}
            />
          </div>
        </>
      )}
      <div className="w-full md:w-1/4">
        <label className="block text-sm font-medium mb-1 dark:text-gray-200">
          {t("Book Type")}
        </label>
        <Select
          options={bookTypeOptions}
          value={bookTypeOptions.find(option => option.value === bookType)}
          onChange={(selected) => onBookTypeChange(selected?.value || null)}
          styles={customStyles}
          isClearable
        />
      </div>
      <div className="w-full md:w-1/4">
        <label className="block text-sm font-medium mb-1 dark:text-gray-200">
          {t("Status")}
        </label>
        <Select
          options={statusOptions}
          value={statusOptions.find(option => option.value === status)}
          onChange={(selected) => onStatusChange(selected?.value || null)}
          styles={customStyles}
          isClearable
        />
      </div>
      <div className="w-full md:w-1/4">
        <label className="block text-sm font-medium mb-1 dark:text-gray-200">
          {t("Approval Status")}
        </label>
        <Select
          options={approvalStatusOptions}
          value={approvalStatusOptions.find(option => option.value === approvalStatus)}
          onChange={(selected) => onApprovalStatusChange(selected?.value || null)}
          styles={customStyles}
          isClearable
        />
      </div>
      {(search || authorId || publisherId || bookType || status || approvalStatus) && (
        <button
          type="button"
          onClick={handleClearFilters}
          className="p-2 text-gray-500 hover:text-gray-700 focus:outline-none"
          title="Clear filters"
        >
          <Icon icon="mdi:close-circle" width="20" height="20" />
        </button>
      )}
    </div>
  );
};

export default EbookFilter;