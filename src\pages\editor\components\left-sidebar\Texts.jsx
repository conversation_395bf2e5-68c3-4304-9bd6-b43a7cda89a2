import React from "react";
import { Icon } from "@iconify/react";
import { useDispatch, useSelector } from "react-redux";
import { addElement, updateElement, setEditingElementId } from "../../store/pagesSlice";
import useNextElementPosition from "../../../book-preview/hooks/useNextElementPosition";

const Texts = () => {
  const dispatch = useDispatch();
  const editingElementId = useSelector(
    (state) => state.pages.present.editingElementId
  );
  const elements = useSelector(
    (state) =>
      state.pages.present.pages[state.pages.present.currentPage]?.elements || []
  );
  const calculateNextPosition = useNextElementPosition();

  // Access the width from the Redux store and subtract 40 pixels (20 on each side)
  const width = useSelector((state) => state.ebook.width) - 60;

  const handleAddText = (item, height) => {
    if (editingElementId) {
      const editingEl = elements.find((el) => el.id === editingElementId);
      if (editingEl) {
        dispatch(
          updateElement({
            id: editingElementId,
            updates: {
              content: editingEl.content + item.html,
              size: { ...editingEl.size, height: editingEl.size.height + height },
            },
          })
        );
      } else {
        dispatch(setEditingElementId(null));
        const position = calculateNextPosition();
        dispatch(
          addElement({
            type: "text",
            content: item.html,
            position,
            size: { width, height },
          })
        );
      }
      return;
    }
    const position = calculateNextPosition();
    dispatch(
      addElement({
        type: "text",
        content: item.html,
        position,
        size: { width, height },
      })
    );
  };

  const handleDragStart = (e, item) => {
    const position = calculateNextPosition();
    e.dataTransfer.setData("type", "text");
    e.dataTransfer.setData("content", item.html);
    e.dataTransfer.setData("position", JSON.stringify(position));
    e.dataTransfer.setData(
      "size",
      JSON.stringify({ width, height: item.height }) // Use width from Redux store minus 40 pixels (20 on each side)
    );
  };

  const items = [
    { name: 'Heading', icon: 'jam:header', html: '<h1>Heading</h1>', height: 60 },
    { name: 'Paragraph', icon: 'mdi:format-paragraph', html: '<p>Paragraph</p>', height: 40 },
    { name: 'Ordered List', icon: 'mdi:format-list-numbered', html: '<ol><li>Item 1</li><li>Item 2</li></ol>', height: 50 },
    { name: 'Unordered List', icon: 'mdi:format-list-bulleted', html: '<ul><li>Item 1</li><li>Item 2</li></ul>', height: 50 },
    { name: 'Definition List', icon: 'mdi:format-list-bulleted-type', html: '<dl class="space-y-4"><dt class="font-semibold text-lg">Term 1</dt><dd class="ml-4">Definition 1</dd></dl>', height: 100 },
    { name: 'Link', icon: 'mdi:link', html: '<a href="#" class="text-blue-600 hover:text-blue-800 underline">Link</a>', height: 30 },
    { name: 'Divider', icon: 'mdi:minus', html: '<hr class="my-4 border-t border-gray-300" />', height: 20 },
    { name: 'Blockquote', icon: 'mdi:format-quote-close', html: '<blockquote class="pl-4 border-l-4 border-gray-300 italic text-gray-700">Blockquote</blockquote>', height: 60 },
    { name: 'Table', icon: 'mdi:table', html: '<table class="w-full border-collapse border border-gray-300"><tr><th class="border border-gray-300 p-2">Header 1</th><th class="border border-gray-300 p-2">Header 2</th></tr><tr><td class="border border-gray-300 p-2">Cell 1</td><td class="border border-gray-300 p-2">Cell 2</td></tr></table>', height: 150 },
  ];

  return (
    <div className="flex items-center justify-center bg-white dark:bg-gray-800">
      <div className="grid grid-cols-3 gap-2 bg-white dark:bg-gray-700">
        {items.map((item, index) => (
          <div
            key={index}
            className="border dark:border-gray-600 dark:bg-gray-800
                       bg-white text-center p-4 flex flex-col items-center
                       cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"
            draggable
            onClick={() => handleAddText(item, item.height)}
            onDragStart={(e) => handleDragStart(e, item)}
          >
            <Icon icon={item.icon} className="text-xl mb-1 dark:text-white text-gray-800" />
            <div className="text-sm font-medium dark:text-white text-gray-800">
              {item.name}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Texts;
