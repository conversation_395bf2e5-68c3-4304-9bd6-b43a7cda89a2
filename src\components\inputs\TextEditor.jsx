import React, { useRef, useEffect } from 'react';
import { useField } from 'formik';
import { Editor } from '@tinymce/tinymce-react';

export const TextEditor = ({ label, rows = 3, ...props }) => {
  const editorRef = useRef(null);
  const [field, meta, helpers] = useField(props);
  
  // Define a row height multiplier (adjust as needed)
  const rowHeight = 40;
  const editorHeight = rows * rowHeight;

  const handleEditorChange = (content) => {
    helpers.setValue(content);
  };

  // Sync editor content with Formik's field value
  useEffect(() => {
    if (editorRef.current) {
      const currentContent = editorRef.current.getContent();
      if (currentContent !== field.value) {
        editorRef.current.setContent(field.value || '');
      }
    }
  }, [field.value]);

  return (
    <div className="mb-4">
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
          {label}
        </label>
      )}
      <Editor
        tinymceScriptSrc="/tinymce/tinymce.min.js"
        onInit={(evt, editor) => {
          editorRef.current = editor;
          editor.setContent(field.value || '');
        }}
        value={field.value}
        init={{
          height: editorHeight,
          menubar: false,
          inline: false,
          forced_root_block: 'p',
          plugins: 'link image code',
          toolbar:
            'undo redo | bold italic | alignleft aligncenter alignright | image | code',
          placeholder: props.placeholder || 'Enter text here...',
          automatic_uploads: true,
          file_picker_types: 'image',
          file_picker_callback: (callback, value, meta) => {
            if (meta.filetype === 'image') {
              const input = document.createElement('input');
              input.setAttribute('type', 'file');
              input.setAttribute('accept', 'image/*');
              input.onchange = function () {
                const file = this.files[0];
                const reader = new FileReader();
                reader.onload = function () {
                  const id = 'blobid' + new Date().getTime();
                  const blobCache = tinymce.activeEditor.editorUpload.blobCache;
                  const base64 = reader.result.split(',')[1];
                  const blobInfo = blobCache.create(id, file, base64);
                  blobCache.add(blobInfo);
                  callback(blobInfo.blobUri(), { alt: file.name });
                };
                reader.readAsDataURL(file);
              };
              input.click();
            }
          },
        }}
        onEditorChange={handleEditorChange}
        onBlur={() => helpers.setTouched(true)}
        {...props}
      />
      {meta.touched && meta.error && (
        <div className="text-red-600 dark:text-red-400 text-sm mt-1">
          {meta.error}
        </div>
      )}
    </div>
  );
};

export default TextEditor;
