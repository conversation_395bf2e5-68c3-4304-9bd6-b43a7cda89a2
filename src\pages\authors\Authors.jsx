// File: src/pages/Authors/Authors.jsx
import React, { useState, useEffect } from "react";
import { useTranslation } from 'react-i18next';
import useDataFetching from "@/hooks/useDataFetching";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import AuthorForm from "./AuthorForm";
import api from "@/lib/axios";
import { useQueryClient } from "@tanstack/react-query";
import { Edit, Trash } from 'lucide-react';
import Confirm from "@/components/ui/Confirm"; // Import Confirm

const AuthorsPage = () => {
  const { t } = useTranslation();
  const [state, setState] = useState({
    page: 1,
    pageSize: 10,
    search: "",
    debouncedSearch: "",
    isModalOpen: false,
    isEditMode: false,
    currentAuthor: null,
  });

  const queryClient = useQueryClient();

  const { data: authorsData, isLoading, refetch } = useDataFetching({
    queryKey: ["authorList", state.page, state.pageSize, state.debouncedSearch],
    endPoint: `admin/authors?page=${state.page}&per_page=${state.pageSize}&search=${state.debouncedSearch}`,
  });

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setState((prev) => ({ ...prev, debouncedSearch: state.search }));
    }, 500);

    return () => clearTimeout(delayDebounceFn);
  }, [state.search]);

  const handleModalOpen = (editMode = false, author = null) => {
    setState((prev) => ({
      ...prev,
      isModalOpen: true,
      isEditMode: editMode,
      currentAuthor: author,
    }));
  };

  const handleModalClose = () => {
    setState((prev) => ({ ...prev, isModalOpen: false, currentAuthor: null }));
  };

  const handleDeleteClick = async (authorId) => {
    Confirm(async () => {
      try {
        await api.delete(`admin/authors/${authorId}`);
        queryClient.invalidateQueries("authorList");
      } catch (error) {
        console.error("Error deleting author:", error);
      }
    });
  };

  const handleFormSubmit = async (values, { resetForm }) => {
    console.log("handleFormSubmit called with values:", values);
    const formData = new FormData();
    formData.append("name", values.name);
    formData.append("is_active", values.status ? 1 : 0);
    formData.append("bio", values.bio);
    if (typeof values.photo === "object") {
      formData.append("photo", values.photo);
    }

    if (state.isEditMode) {
      formData.append("_method", "PUT");
    }

    try {
      const url = state.isEditMode
        ? `admin/authors/${state.currentAuthor.id}`
        : "admin/authors";
      await api.post(url, formData);
      queryClient.invalidateQueries("authorList");
      resetForm();
      handleModalClose();
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  return (
    <>
      <DataTable
        title={t("authors.authorsList")}
        columns={[
          {
            Header: t("authors.name"),
            accessor: "name",
          },
          {
            Header: t("authors.photo"),
            accessor: "photo",
            Cell: ({ value }) => (
              <img src={`${import.meta.env.VITE_HOST_URL}/storage/${value}`} alt="photo" className="h-10 w-10 object-cover" />
            ),
          },
          {
            Header: t("authors.status"),
            accessor: "is_active",
            Cell: ({ value }) => (
              <span
                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${value ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                  }`}
              >
                {value ? t("authors.active") : t("authors.inactive")}
              </span>
            ),
          },
          {
            Header: t("authors.action"),
            accessor: "id",
            Cell: ({ value, row }) => (
              <div className="flex justify-center">
                <button
                  className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-2 rounded-lg"
                  onClick={() => handleModalOpen(true, row.original)}
                >
                  <Edit size={16} />
                </button>
                <button
                  className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-2 rounded-lg ml-2"
                  onClick={() => handleDeleteClick(value)}
                >
                  <Trash size={16} />
                </button>
              </div>
            ),
          },
        ]}
        data={authorsData?.data?.data || []}
        fetchData={refetch}
        loading={isLoading}
        totalPages={authorsData?.data?.total_pages || 1}
        currentPage={authorsData?.data?.current_page || 1}
        pageSize={state.pageSize}
        onPageChange={(page) => setState((prev) => ({ ...prev, page }))}
        onPageSizeChange={(pageSize) => setState((prev) => ({ ...prev, pageSize }))}
        onSearch={(search) => setState((prev) => ({ ...prev, search }))}
        buttonLabel={t("authors.addAuthor")}
        onButtonClick={() => handleModalOpen(false)}
      />

      {state.isModalOpen && (
        <Modal
          activeModal={state.isModalOpen}
          onClose={handleModalClose}
          title={state.isEditMode ? t("authors.editAuthor") : t("authors.addAuthor")}
        >
          <AuthorForm
            initialValues={{
              name: state.currentAuthor?.name || "",
              status: state.currentAuthor?.is_active ? '1' : '0',
              photo: state.currentAuthor?.thumbnail_url || null, // should be photo_url
              bio: state.currentAuthor?.bio || "",
            }}
            onSubmit={handleFormSubmit}
            isEditMode={state.isEditMode}
          />
        </Modal>
      )}
    </>
  );
};

export default AuthorsPage;
