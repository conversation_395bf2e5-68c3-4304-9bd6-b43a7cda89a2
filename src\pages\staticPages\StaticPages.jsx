// File: src/pages/Authors/Authors.jsx
import React, { useState, useEffect } from "react";
import { useTranslation } from 'react-i18next';
import useDataFetching from "@/hooks/useDataFetching";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import StaticForm from "./StaticForm";
import api from "@/lib/axios";
import { useQueryClient } from "@tanstack/react-query";
import { Edit, Trash } from 'lucide-react';
import Confirm from "@/components/ui/Confirm"; // Import Confirm

const StaticPages = () => {
  const { t } = useTranslation();
  const [state, setState] = useState({
    page: 1,
    pageSize: 10,
    search: "",
    debouncedSearch: "",
    isModalOpen: false,
    isEditMode: false,
    currentStaticPage: null,
  });

  const queryClient = useQueryClient();

  const { data: authorsData, isLoading, refetch } = useDataFetching({
    queryKey: ["staticPageList", state.page, state.pageSize, state.debouncedSearch],
    endPoint: `admin/static-pages?page=${state.page}&per_page=${state.pageSize}&search=${state.debouncedSearch}`,
  });

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setState((prev) => ({ ...prev, debouncedSearch: state.search }));
    }, 500);

    return () => clearTimeout(delayDebounceFn);
  }, [state.search]);

  const handleModalOpen = (editMode = false, author = null) => {
    setState((prev) => ({
      ...prev,
      isModalOpen: true,
      isEditMode: editMode,
      currentStaticPage: author,
    }));
  };

  const handleModalClose = () => {
    setState((prev) => ({ ...prev, isModalOpen: false, currentStaticPage: null }));
  };

  const handleDeleteClick = async (authorId) => {
    Confirm(async () => {
      try {
        await api.delete(`admin/static-pages/${authorId}`);
        queryClient.invalidateQueries("staticPageList");
      } catch (error) {
        console.error("Error deleting author:", error);
      }
    });
  };

  const handleFormSubmit = async (values, { resetForm }) => {
    console.log("handleFormSubmit called with values:", values);
    const formData = new FormData();
    formData.append("title", values.title);
    formData.append("slug", values.slug);
    formData.append("status", values.status ? 1 : 0);
    formData.append("content", values.content);
    

    if (state.isEditMode) {
      formData.append("_method", "PUT");
    }

    try {
      const url = state.isEditMode
        ? `admin/static-pages/${state.currentStaticPage.id}`
        : "admin/static-pages";
      await api.post(url, formData);
      queryClient.invalidateQueries("staticPageList");
      resetForm();
      handleModalClose();
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  return (
    <>
      <DataTable
        title={t("static.page")}
        columns={[
          {
            Header: t("title"),
            accessor: "title",
          },
        //   {
        //     Header: t("authors.photo"),
        //     accessor: "photo",
        //     Cell: ({ value }) => (
        //       <img src={`${import.meta.env.VITE_HOST_URL}/storage/${value}`} alt="photo" className="h-10 w-10 object-cover" />
        //     ),
        //   },
          {
            Header: t("authors.status"),
            accessor: "status",
            Cell: ({ value }) => (
              <span
                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${value ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                  }`}
              >
                {value ? t("authors.active") : t("authors.inactive")}
              </span>
            ),
          },
          {
            Header: t("authors.action"),
            accessor: "id",
            Cell: ({ value, row }) => (
              <div className="flex justify-center">
                <button
                  className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-2 rounded-lg"
                  onClick={() => handleModalOpen(true, row.original)}
                >
                  <Edit size={16} />
                </button>
                <button
                  className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-2 rounded-lg ml-2"
                  onClick={() => handleDeleteClick(value)}
                >
                  <Trash size={16} />
                </button>
              </div>
            ),
          },
        ]}
        data={authorsData?.data?.data || []}
        fetchData={refetch}
        loading={isLoading}
        totalPages={authorsData?.data?.total_pages || 1}
        currentPage={authorsData?.data?.current_page || 1}
        pageSize={state.pageSize}
        onPageChange={(page) => setState((prev) => ({ ...prev, page }))}
        onPageSizeChange={(pageSize) => setState((prev) => ({ ...prev, pageSize }))}
        onSearch={(search) => setState((prev) => ({ ...prev, search }))}
        buttonLabel={t("static.addStaticPage")}
        onButtonClick={() => handleModalOpen(false)}
      />

      {state.isModalOpen && (
        <Modal
          activeModal={state.isModalOpen}
          onClose={handleModalClose}
          title={state.isEditMode ? t("static.editStatic") : t("static.addStaticPage")}
        >
          <StaticForm
            initialValues={{
              title: state.currentStaticPage?.title || "",
              slug: state.currentStaticPage?.slug || "",
              status: state.currentStaticPage?.status ? '1' : '0',
            //   photo: state.currentStaticPage?.thumbnail_url || null, // should be photo_url
              content: state.currentStaticPage?.content || "",
            }}
            onSubmit={handleFormSubmit}
            isEditMode={state.isEditMode}
          />
        </Modal>
      )}
    </>
  );
};

export default StaticPages;
