// src/pages/ManageEbooks.jsx
import React, { useState, useEffect } from "react";
import useDataFetching from "@/hooks/useDataFetching";
import api from "@/lib/axios";
import { useNavigate } from "react-router-dom";
import Modal from "@/components/ui/Modal";
import EbookInfoView from "./EbookInfoView";
import EbookForm from "./EbookForm";
import DataTable from "@/components/ui/DataTable";
import Confirm from "@/components/ui/Confirm";
import { toast } from "sonner";
import { Edit, Trash2, Eye, BookOpen, Info, Edit3 } from "lucide-react";
import EbookFilter from "./EbookFilter";
import Select from "react-select";
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

const ManageEbooks = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(20);
  const [search, setSearch] = useState("");
  const [authorId, setAuthorId] = useState(null);
  const [publisherId, setPublisherId] = useState(null);
  const [bookType, setBookType] = useState(null);
  const [status, setStatus] = useState(null);
  const [approvalStatus, setApprovalStatus] = useState(null);
  const navigate = useNavigate();
  const [selectedBook, setSelectedBook] = useState(null);
  const [isInfoModalOpen, setIsInfoModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const { data, isLoading, refetch, error } = useDataFetching({
    queryKey: [
      "ebook",
      page,
      perPage,
      search,
      authorId,
      publisherId,
      bookType,
      status,
      approvalStatus,
    ],
    endPoint: "/admin/ebooks",
    params: {
      page,
      per_page: perPage,
      search,
      author_id: authorId,
      publisher_id: publisherId,
      book_type: bookType,
      status,
      approval_status: approvalStatus,
    },
  });

  useEffect(() => {
    if (error) {
      toast.error("Failed to fetch ebooks. Please try again.");
    }
  }, [error]);

  const updateStatus = useMutation({
    mutationFn: async ({ id, approval_status }) => {
      await api.patch(`/admin/ebooks/${id}/update-status`, { approval_status });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pending-ebooks'] });
      refetch(); // Refetch the data to update the table
    },
    onError: () => {
      toast.error(t('ebooks.statusUpdateError'));
    },
  });

  const handleSearch = (e) => {
    e.preventDefault();
    setPage(1);
    refetch();
  };

  const handleAuthorChange = (selectedAuthorId) => {
    setAuthorId(selectedAuthorId);
    setPage(1);
    refetch();
  };

  const handlePublisherChange = (selectedPublisherId) => {
    setPublisherId(selectedPublisherId);
    setPage(1);
    refetch();
  };

  const handleBookTypeChange = (selectedBookType) => {
    setBookType(selectedBookType);
    setPage(1);
    refetch();
  };

  const handleStatusChange = (selectedStatus) => {
    setStatus(selectedStatus);
    setPage(1);
    refetch();
  };

  const handleApprovalStatusChange = (selectedStatus) => {
    setApprovalStatus(selectedStatus);
    setPage(1);
    refetch();
  };

  const handleDelete = (e, bookId) => {
    Confirm(
      async () => {
        try {
          await api.delete(`/admin/ebooks/${bookId}`);
          refetch();
        } catch (error) {
          console.error("Error deleting ebook:", error);
        }
      },
      t('ebook.delete.confirm.title'),
      t('ebook.delete.confirm.text'),
      t('ebook.delete.confirm.description'),
      t('ebook.delete.confirm.cancel'),
      t('ebook.delete.confirm.confirm')
    );
  };

  const handleView = (book) => {
    navigate(`/flipbooks/${book.id}`);
  };

  const handleViewBasicInfo = (book) => {
    setSelectedBook(book);
    setIsInfoModalOpen(true);
  };

  const handleEditBasicInfo = (book) => {
    setSelectedBook(book);
    setIsEditModalOpen(true);
  };

  const handleDesignWithEditor = (book) => {
    navigate(`/ebooks/edit/${book.id}`);
  };

  const handleSaveAsTemplate = async (book) => {
    try {
      await api.post(`/admin/ebooks/${book.id}/save-templates`);
    } catch (error) {
      console.error("Error saving template:", error);
    }
  };

  const handleModalClose = () => {
    setIsInfoModalOpen(false);
    setIsEditModalOpen(false);
    setSelectedBook(null);
  };

  const handleCreateBook = () => {
    setSelectedBook(null);
    setIsEditModalOpen(true);
  };

  const handleApproval = async (book, newStatus) => {
    updateStatus.mutate({ id: book.id, approval_status: newStatus });
  };

  const columns = [
    {
      Header: "Title",
      accessor: "title",
    },
    {
      Header: "Author",
      accessor: "authors",
      Cell: ({ value }) => {
        return value.map(author => author.name).join(", ");
      }
    },
    {
      Header: "Publisher",
      accessor: "publisher",
    },
    {
      Header: "Book Type",
      accessor: "book_type",
    },
    {
      Header: "Status",
      accessor: "status",
    },
    {
      Header: "Approval",
      Cell: ({ row }) => {
        const options = [
          { value: "pending", label: "Pending" },
          { value: "approved", label: "Approved" },
          { value: "rejected", label: "Rejected" },
        ];
        return (
          <Select
            options={options}
            value={options.find(
              (option) => option.value === row.original.approval_status
            )}
            onChange={(selectedOption) =>
              handleApproval(row.original, selectedOption.value)
            }
            className="w-32"
          />
        );
      },
    },
    {
      Header: "Actions",
      Cell: ({ row }) => (
        <div className="flex space-x-2">
          <button
            onClick={() => handleView(row.original)}
            className="p-1 bg-blue-500 text-white rounded relative group"
            title={t('ebook.button.tooltip.view')}
          >
            <Eye size={16} />
            <span className="absolute bottom-full left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
              {t('ebook.button.tooltip.view')}
            </span>
          </button>
          <button
            onClick={() => handleViewBasicInfo(row.original)}
            className="p-1 bg-green-500 text-white rounded relative group"
            title={t('ebook.button.tooltip.viewInfo')}
          >
            <Info size={16} />
            <span className="absolute bottom-full left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
              {t('ebook.button.tooltip.viewInfo')}
            </span>
          </button>
          <button
            onClick={() => handleEditBasicInfo(row.original)}
            className="p-1 bg-yellow-500 text-white rounded relative group"
            title={t('ebook.button.tooltip.editInfo')}
          >
            <Edit3 size={16} />
            <span className="absolute bottom-full left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
              {t('ebook.button.tooltip.editInfo')}
            </span>
          </button>
          <button
            onClick={() => handleDesignWithEditor(row.original)}
            className={`p-1 ${row.original.book_type === 'pdf' ? 'bg-gray-400 cursor-not-allowed' : 'bg-purple-500'} text-white rounded relative group`}
            disabled={row.original.book_type === 'pdf'}
            title={t('ebook.button.tooltip.design')}
          >
            <Edit size={16} />
            <span className="absolute bottom-full left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
              {t('ebook.button.tooltip.design')}
            </span>
          </button>
          <button
            onClick={() => handleSaveAsTemplate(row.original)}
            className={`p-1 ${row.original.book_type === 'pdf' ? 'bg-gray-400 cursor-not-allowed' : 'bg-indigo-500'} text-white rounded relative group`}
            disabled={row.original.book_type === 'pdf'}
            title={t('ebook.button.tooltip.template')}
          >
            <BookOpen size={16} />
            <span className="absolute bottom-full left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
              {t('ebook.button.tooltip.template')}
            </span>
          </button>
          <button
            onClick={(e) => handleDelete(e, row.original.id)}
            className="p-1 bg-red-500 text-white rounded relative group"
            title={t('ebook.button.tooltip.delete')}
          >
            <Trash2 size={16} />
            <span className="absolute bottom-full left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
              {t('ebook.button.tooltip.delete')}
            </span>
          </button>
        </div>
      ),
    },
  ];

  return (
    <div className="min-h-screen rounded bg-white p-6 dark:bg-[#111827]">
      <div className="flex flex-row items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white">eBooks</h2>
        <button
          type="button"
          onClick={handleCreateBook}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 dark:bg-green-700 dark:hover:bg-green-800"
        >
          {t('ebook.button.create')}
        </button>
      </div>

      <EbookFilter
        authorId={authorId}
        publisherId={publisherId}
        onAuthorChange={handleAuthorChange}
        onPublisherChange={handlePublisherChange}
        search={search}
        onSearchChange={setSearch}
        onSearchSubmit={handleSearch}
        bookType={bookType}
        onBookTypeChange={handleBookTypeChange}
        status={status}
        onStatusChange={handleStatusChange}
        approvalStatus={approvalStatus}
        onApprovalStatusChange={handleApprovalStatusChange}
      />

      <DataTable
        title={false}
        columns={columns}
        data={data?.data?.data || []}
        loading={isLoading}
        totalPages={data?.data?.total_pages || 1}
        currentPage={page}
        pageSize={perPage}
        onPageChange={(newPage) => setPage(newPage)}
        onPageSizeChange={(newPageSize) => setPerPage(newPageSize)}
      />

      <Modal
        activeModal={isInfoModalOpen}
        onClose={handleModalClose}
        title={selectedBook ? t('ebook.modal.title.view') : ""}
      >
        <EbookInfoView book={selectedBook} />
      </Modal>

      {isEditModalOpen && (
        <Modal
          activeModal={isEditModalOpen}
          onClose={handleModalClose}
          title={
            selectedBook ? t('ebook.modal.title.edit') : t('ebook.modal.title.create')
          }
          className="w-full max-w-3xl"
        >
          <EbookForm
            handleModalClose={handleModalClose}
            initialValues={selectedBook}
          />
        </Modal>
      )}
    </div>
  );
};

export default ManageEbooks;
