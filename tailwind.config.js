/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  darkMode: 'class',
  theme: {
    extend: {
      fontFamily: {roboto: ['<PERSON><PERSON>', 'sans-serif'],
        gara<PERSON>: ['<PERSON><PERSON><PERSON>', 'serif'],
        helvetica: ['Helvetica', 'sans-serif'],
        openSans: ['Open Sans', 'sans-serif'],
        baskerville: ['Baskerville', 'serif'],
        georgia: ['Georgia', 'serif'],
        montserrat: ['Montserrat', 'sans-serif'],
        lato: ['Lato', 'sans-serif'],
        palatino: ['Palatino Linotype', 'serif'],
        timesNewRoman: ['Times New Roman', 'serif'],
        caslon: ['Caslon', 'serif'],
        verdana: ['Verdana', 'sans-serif'],
        futura: ['Futura', 'sans-serif'],
        merriweather: ['Merriweather', 'serif'],
        os<PERSON>: ['<PERSON>', 'sans-serif'],
        raleway: ['Raleway', 'sans-serif'],
        sourceSansPro: ['Source Sans Pro', 'sans-serif'],
        alegreya: ['Alegreya', 'serif'],
        bembo: ['Bembo', 'serif'],
        crimson: ['Crimson', 'serif'],
        etna: ['Etna', 'sans-serif'],
        sans: ['Open Sans', 'Arial', 'sans-serif'],
        solaimanLipi: ['SolaimanLipi', 'sans-serif'],
        siyamRupali: ['Siyam Rupali', 'serif'],
        nikosh: ['Nikosh', 'serif'],
        vijaya: ['Vijaya', 'serif'],

        
      },
      animation: {
        'fade-in': 'fadeIn 0.2s ease-in-out',
        'slide-in': 'slideIn 0.2s ease-in-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideIn: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
      },
      colors: {
        blue: {
          50: '#E6E3F7',    // Lightest
          100: '#C2BFF3',
          200: '#9E9BF0',
          300: '#7A78ED',
          400: '#5654E9',
          500: '#4841BF',    // Base color
          600: '#3B368F',
          700: '#2E2560',
          800: '#21192F',
          900: '#14121A',    // Darkest
        },
      },
    },
  },
  plugins: [],
};