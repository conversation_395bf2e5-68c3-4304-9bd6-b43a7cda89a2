import React, { useCallback } from 'react';
import { Code, Image, Type, Plus, MousePointer } from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import { addElement, addPage, toggleCode, selectTool } from '../store/pagesSlice'; // Assume selectTool is an action
import { getSelectedTool } from '../store/pagesSlice'; // Ensure this import is correct

export const Toolbar = () => {
  const dispatch = useDispatch();
  const selectedTool = useSelector(getSelectedTool);

  const handleSelectTool = useCallback((tool) => {
    dispatch(selectTool(tool));
  }, [dispatch]);

  const handleAddText = useCallback(() => {
    handleSelectTool('text');
    dispatch(addElement({
      type: 'text',
      content: 'Double click to edit',
      position: { x: 50, y: 50 },
      size: { width: 200, height: 50 },
      style: {
        fontSize: '16px',
        fontFamily: 'sans-serif'
      }
    }));
  }, [dispatch, handleSelectTool]);

  const handleAddImage = useCallback(async () => {
    handleSelectTool('image');
    try {
      const response = await fetch('https://picsum.photos/300/200');
      const imageUrl = response.url;
      dispatch(addElement({
        type: 'image',
        content: imageUrl,
        position: { x: 50, y: 50 },
        size: { width: 300, height: 200 }
      }));
    } catch (error) {
      console.error('Error fetching image:', error);
    }
  }, [dispatch, handleSelectTool]);

  return (
    <div className="h-12 bg-gray-800 flex items-center px-4 text-white flex">
      <div className="flex space-x-4">
        <button
          className={`p-2 rounded flex items-center gap-2 ${selectedTool === 'select' ? 'bg-blue-500' : 'hover:bg-gray-700'}`}
          onClick={() => handleSelectTool('select')}
        >
          <MousePointer size={20} />
          <span>Select</span>
        </button>
        <button
          className={`p-2 rounded flex items-center gap-2 ${selectedTool === 'text' ? 'bg-blue-500' : 'hover:bg-gray-700'}`}
          onClick={handleAddText}
        >
          <Type size={20} />
          <span>Text</span>
        </button>
        <button
          className={`p-2 rounded flex items-center gap-2 ${selectedTool === 'image' ? 'bg-blue-500' : 'hover:bg-gray-700'}`}
          onClick={handleAddImage}
        >
          <Image size={20} />
          <span>Image</span>
        </button>
        <div className="w-px bg-gray-600" />
        <button
          className={`p-2 rounded flex items-center gap-2 ${selectedTool === 'page' ? 'bg-blue-500' : 'hover:bg-gray-700'}`}
          onClick={() => {
            handleSelectTool('page');
            dispatch(addPage());
          }}
        >
          <Plus size={20} />
          <span>Page</span>
        </button>
        <div className="w-px bg-gray-600" />
        <button
          className={`p-2 rounded flex items-center gap-2 ${selectedTool === 'code' ? 'bg-blue-500' : 'hover:bg-gray-700'}`}
          onClick={() => {
            handleSelectTool('code');
            dispatch(toggleCode());
          }}
        >
          <Code size={20} />
          <span>Code</span>
        </button>
      </div>
    </div>
  );
};
