import React, { useState } from "react";
import { Page } from "react-pdf";
import DrawingLayer from "./DrawingLayer";
import { useSelector } from "react-redux";

export default function PdfPageWithNotes({
  pageNumber,
  ebookId,
  onPageClick,
  scale, // coming from your dynamic hook
}) {
  const noteTakingLayer = useSelector((state) => state.ebook.noteTakingLayer);
  const [pageDims, setPageDims] = useState({ width: 0, height: 0 });

  const onRenderSuccess = (pdfPage) => {
    const viewport = pdfPage.getViewport({ scale: 1 });
    setPageDims({
      width: viewport.width,
      height: viewport.height,
    });
  };

  // Multiply the calculated scale by an extra factor to enlarge the PDF pages.
  const multiplier = 1.3; // Adjust this value as needed.
  const effectiveScale = scale * multiplier;
  
  // Outer container dimensions reserve space for the effective scale.
  const scaledWidth = pageDims.width * effectiveScale || 0;
  const scaledHeight = pageDims.height * effectiveScale || 0;

  return (
    <div
      style={{
        width: scaledWidth,
        height: scaledHeight,
        overflow: "hidden",
        marginBottom: "2rem",
        border: "1px solid #ccc", // ensures a clear border on all sides
      }}
      onClick={onPageClick}
    >
      <div
        style={{
          transform: `scale(${effectiveScale})`,
          transformOrigin: "top left",
          width: pageDims.width,
          height: pageDims.height,
        }}
      >
        <Page
          pageNumber={pageNumber}
          renderTextLayer={false}
          width={pageDims.width}
          onRenderSuccess={onRenderSuccess}
          className="bg-white shadow-md rounded"
        />
        {noteTakingLayer && (
          <DrawingLayer
            ebookId={ebookId}
            pageId={pageNumber}
            width={pageDims.width}
            height={pageDims.height}
          />
        )}
      </div>
    </div>
  );
}
