import React from 'react';
import { Book, Users, BookOpen } from 'lucide-react';
import useDataFetching from '@/hooks/useDataFetching';
import { useTranslation } from "react-i18next";

export default function Dashboard() {
  const { t } = useTranslation();
  
  const { data: dashboards, loading } = useDataFetching({
    queryKey: 'ebookRecent',
    endPoint: 'admin/dashboard',
  });

  const totalUnpublishedBooks = dashboards?.data?.total_unpublished_books || 0;
  const totalPublishedBooks = dashboards?.data?.total_published_books || 0;
  const totalEbooks = dashboards?.data?.total_ebooks || 0;
  const totalGenres = dashboards?.data?.total_genres || 0;
  const recentEbooks = dashboards?.data?.recent_ebooks || [];
  const pendingEbooks = dashboards?.data?.pending_ebooks || [];

  const renderRows = (data) => {
    return data.map((ebook, index) => (
      <tr key={index} className={index % 2 === 0 ? 'bg-gray-100 dark:bg-gray-700' : 'bg-white dark:bg-gray-800'}>
        <td className="px-6 py-4 text-gray-700 dark:text-gray-200">{index + 1}</td>
        <td className="px-6 py-4 text-gray-700 dark:text-gray-200">{ebook.title}</td>
        <td className="px-6 py-4 text-gray-700 dark:text-gray-200">{ebook.author}</td>
        <td className="px-6 py-4 text-gray-700 dark:text-gray-200">{ebook.genre?.name}</td>
      </tr>
    ));
  };

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-gray-800 dark:text-white">{t("dashboard.title")}</h2>

      <div className="grid gap-6 mb-8 md:grid-cols-4">
        {[
          { title: t("dashboard.totalUnpublishedBooks"), value: totalUnpublishedBooks, icon: Book },
          { title: t("dashboard.totalPublishedBooks"), value: totalPublishedBooks, icon: BookOpen },
          { title: t("dashboard.totalEbooks"), value: totalEbooks, icon: Book },
          { title: t("dashboard.totalGenres"), value: totalGenres, icon: Users },
        ].map((stat) => (
          <div key={stat.title} className="p-6 bg-white rounded-lg shadow-sm dark:bg-gray-800">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-md font-medium text-gray-600 dark:text-gray-400">{stat.title}</p>
                <p className="text-2xl font-semibold text-gray-700 dark:text-gray-200">{stat.value}</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-full dark:bg-blue-900/20">
                <stat.icon className="w-6 h-6 text-blue-500" />
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid gap-6 mb-8 md:grid-cols-2">
        <div>
          <div className="p-6 bg-white rounded-lg shadow-sm dark:bg-gray-800">
            <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-4">{t("dashboard.recentEbooks")}</h3>
            <div className="overflow-x-auto border rounded-lg shadow-sm dark:border-gray-700">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600 bg-white dark:bg-gray-800">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th className="text-left font-semibold uppercase tracking-wider px-6 py-3 dark:text-white">#</th>
                    <th className="text-left font-semibold uppercase tracking-wider px-6 py-3 dark:text-white">{t("dashboard.titleColumn")}</th>
                    <th className="text-left font-semibold uppercase tracking-wider px-6 py-3 dark:text-white">{t("dashboard.authorColumn")}</th>
                    <th className="text-left font-semibold uppercase tracking-wider px-6 py-3 dark:text-white">{t("dashboard.genreColumn")}</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                  {loading ? (
                    <tr>
                      <td colSpan={4} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                        {t("dashboard.loading")}
                      </td>
                    </tr>
                  ) : recentEbooks.length > 0 ? (
                    renderRows(recentEbooks)
                  ) : (
                    <tr>
                      <td colSpan={4} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                        {t("dashboard.noData")}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div>
          <div className="p-6 bg-white rounded-lg shadow-sm dark:bg-gray-800">
            <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-4">{t("dashboard.pendingEbooks")}</h3>
            <div className="overflow-x-auto border rounded-lg shadow-sm dark:border-gray-700">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600 bg-white dark:bg-gray-800">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th className="text-left font-semibold uppercase tracking-wider px-6 py-3 dark:text-white">#</th>
                    <th className="text-left font-semibold uppercase tracking-wider px-6 py-3 dark:text-white">{t("dashboard.titleColumn")}</th>
                    <th className="text-left font-semibold uppercase tracking-wider px-6 py-3 dark:text-white">{t("dashboard.authorColumn")}</th>
                    <th className="text-left font-semibold uppercase tracking-wider px-6 py-3 dark:text-white">{t("dashboard.genreColumn")}</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                  {loading ? (
                    <tr>
                      <td colSpan={4} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                        {t("dashboard.loading")}
                      </td>
                    </tr>
                  ) : pendingEbooks.length > 0 ? (
                    renderRows(pendingEbooks)
                  ) : (
                    <tr>
                      <td colSpan={4} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                        {t("dashboard.noData")}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
