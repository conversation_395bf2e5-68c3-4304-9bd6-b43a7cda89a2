import React, { useRef, useState, useEffect } from "react";
import { useField } from "formik";
import { Icon } from "@iconify/react";

const InputFile = ({
    name,
    label = "Browse",
    placeholder = "Choose a file or drop it here...",
    multiple,
    accept = "image/*",
    className = "",
    id,
    title = "",
    description,
    classLabel = "",
    horizontal = false,
    fileName, // this is passed down from the form
    onImageSelect, // Callback to pass selected image to parent
}) => {
    const [field, , helpers] = useField({ name });
    const [selectedFiles, setSelectedFiles] = useState([]);
    const fileInputRef = useRef(null);

    useEffect(() => {
        if (fileName) {
            setSelectedFiles([{ name: fileName }]); // Create a mock file object
        }
    }, [fileName]);

    const handleChange = (event) => {
        const files = Array.from(event.currentTarget.files);
        setSelectedFiles(files);
        helpers.setValue(multiple ? files : files[0]);

        if (files.length > 0 && files[0].type.startsWith("image/")) {
            const reader = new FileReader();
            reader.onloadend = () => {
                // Pass the image URL to the parent via the onImageSelect callback
                onImageSelect(reader.result);
            };
            reader.readAsDataURL(files[0]);
        }
    };

    const handleRemoveFile = (event, fileToRemove) => {
        event.stopPropagation();
        const updatedFiles = selectedFiles.filter((file) => file !== fileToRemove);
        setSelectedFiles(updatedFiles);
        helpers.setValue(multiple ? updatedFiles : null);

        if (fileInputRef.current) {
            fileInputRef.current.value = "";
        }

        // Clear the image in the parent component as well
        if (onImageSelect) {
            onImageSelect(null);
        }
    };

    const handleLabelClick = () => {
        if (fileInputRef.current) {
            fileInputRef.current.click();
        }
    };

    return (
        <div>
            <div className="filegroup">
                {title && (
                    <label
                        className={`block mb-1 text-gray-700 dark:text-gray-200 ${classLabel} ${
                            horizontal ? "flex-0 mr-6 md:w-[100px] w-[60px] break-words" : ""
                        }`}
                    >
                        {title}
                    </label>
                )}
                <div className={`w-full h-[40px] file-control border rounded-md border-[#bdbdbd] flex items-center ${className}`}>
                    <input
                        type="file"
                        ref={fileInputRef}
                        onChange={handleChange}
                        className="hidden"
                        name={name}
                        id={id}
                        multiple={multiple}
                        accept={accept}
                    />
                    <div
                        className="flex-1 overflow-hidden text-ellipsis whitespace-nowrap cursor-pointer"
                        onClick={handleLabelClick}
                    >
                        {selectedFiles.length > 0 ? (
                            selectedFiles.map((file, index) => (
                                <span key={index} className="flex items-center text-slate-400 dark:text-white ml-3">
                                    {file.name}
                                    <button
                                        type="button"
                                        onClick={(event) => handleRemoveFile(event, file)}
                                        className="ml-2 text-red-500"
                                    >
                                        <Icon icon="heroicons:x-mark" width={18} />
                                    </button>
                                </span>
                            ))
                        ) : (
                            <span className="text-slate-400 ml-3">{placeholder}</span>
                        )}
                    </div>
                    <span
                        className="file-name flex-none cursor-pointer border-l px-4 border-[#bdbdbd] dark:border-slate-700 h-full inline-flex items-center bg-slate-100 dark:bg-slate-900 text-slate-600 dark:text-slate-400 text-base rounded-tr rounded-br font-normal"
                        onClick={handleLabelClick}
                    >
                        {label}
                    </span>
                </div>
            </div>
            {description && <span className="input-description">{description}</span>}
        </div>
    );
};

export default InputFile;
