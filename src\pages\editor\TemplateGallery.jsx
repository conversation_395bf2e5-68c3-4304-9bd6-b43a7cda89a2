import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import TemplateCard from "@/components/ui/TemplateCard";
import EbookCardSkeleton from "@/components/ui/EbookCardSkeleton";
import useDataFetching from "@/hooks/useDataFetching";
import api from "@/lib/axios";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

const Templates = () => {
  const { bookId } = useParams();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { data: books, isLoading } = useDataFetching({
    queryKey: ["templates"],
    endPoint: "/admin/templates",
  });

  const handleUse = async (template) => {
    try {
      await api.post(`admin/ebooks/use-templates`, { ebook_id: bookId, template_id: template.id });

      // Invalidate all possible query patterns that might contain ebook data
      queryClient.invalidateQueries({ queryKey: ['ebooksData'] });
      // queryClient.invalidateQueries({ queryKey: ['ebook'] });
      // queryClient.invalidateQueries({ queryKey: ['single-ebook'] });

      // Show success message
      toast.success("Template applied successfully!");

      // Navigate back to editor
      navigate(`/ebooks/edit/${bookId}`);
    } catch (error) {
      console.error("Error using template:", error);
      toast.error("Failed to apply template. Please try again.");
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-700 mb-4">Select a Template</h2>

      <div className="grid grid-cols-3 gap-6">
        {isLoading
          ? Array.from({ length: 5 }, (_, index) => <EbookCardSkeleton key={index} />)
          : books?.data?.data?.length > 0 ? (
              books?.data?.data?.map((template) => (
                <TemplateCard key={template.id} book={template} onUse={() => handleUse(template)} />
              ))
            ) : (
              <div className="col-span-full text-center text-gray-500">No templates found.</div>
            )}
      </div>
    </div>
  );
};

export default Templates;
