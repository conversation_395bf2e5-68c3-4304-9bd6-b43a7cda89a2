import React, { useState, useEffect } from "react";
import { useField, useFormikContext } from "formik";
import { useDropzone } from "react-dropzone";

const ImageInputField = ({ label, name, required }) => {
  const { setFieldValue } = useFormikContext();
  const [field, meta] = useField(name);

  const [preview, setPreview] = useState(null);

  useEffect(() => {
    // Update preview based on field value changes
    if (typeof field.value === "string" && field.value !== "") {
      setPreview(`${import.meta.env.VITE_HOST_URL}/storage/${field.value}`);
    } else if (field.value instanceof File) {
      const objectUrl = URL.createObjectURL(field.value);
      setPreview(objectUrl);
      return () => URL.revokeObjectURL(objectUrl);
    } else {
      setPreview(null);
    }
  }, [field.value]);

  const onDrop = (acceptedFiles) => {
    const file = acceptedFiles[0];
    if (file) {
      setFieldValue(name, file);
    }
  };

  const removeFile = (e) => {
    e.stopPropagation();
    setFieldValue(name, "");
  };

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: "image/*",
    maxFiles: 1,
  });

  return (
    <div className="flex flex-col gap-2 flex-1 my-2">
      {label && (
        <label
          htmlFor={name}
          className="block text-[#000000] text-base font-normal mb-2"
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <div
        {...getRootProps()}
        className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-md p-4 bg-gray-50 hover:bg-gray-100 cursor-pointer h-32"
      >
        <input {...getInputProps()} />
        {preview ? (
          <div className="flex flex-col items-center">
            <img
              src={preview}
              alt="Preview"
              className="h-24 w-24 object-cover border rounded-md"
            />
            <button
              type="button"
              onClick={removeFile}
              className="mt-2 px-4 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 focus:outline-none"
            >
              {typeof field.value === "string" ? "Remove Existing" : "Remove"}
            </button>
          </div>
        ) : (
          <p className="text-sm text-gray-500">Drag & drop an image here, or click to select</p>
        )}
      </div>
      {meta.touched && meta.error && (
        <div className="text-sm text-red-600">{meta.error}</div>
      )}
    </div>
  );
};

export default ImageInputField;
