// Utility function to align elements to the left
export const alignLeft = (elements) => {
    const minX = Math.min(...elements.map(el => el.position?.x || 0));
    elements.forEach(el => {
        el.position.x = minX;
    });
};

// Utility function to center elements horizontally
export const alignCenter = (elements, pageWidth) => {
    const centerX = pageWidth / 2;
    elements.forEach(el => {
        el.position.x = centerX - (el.size?.width || 0) / 2;
    });
};

// Utility function to align elements to the right
export const alignRight = (elements) => {
    const maxX = Math.max(...elements.map(el => (el.position?.x || 0) + (el.size?.width || 0)));
    elements.forEach(el => {
        el.position.x = maxX - (el.size?.width || 0);
    });
};

// Utility function to justify elements across the page
export const justifyElements = (elements, pageWidth) => {
    const totalElementWidth = elements.reduce((sum, el) => sum + (el.size?.width || 0), 0);
    const spaceBetween = (pageWidth - totalElementWidth) / (elements.length - 1);

    let currentX = 0;
    elements.forEach(el => {
        el.position.x = currentX;
        currentX += (el.size?.width || 0) + spaceBetween;
    });
};

// Utility function to align elements to the top
export const alignTop = (elements) => {
    const minY = Math.min(...elements.map(el => el.position?.y || 0));
    elements.forEach(el => {
        el.position.y = minY;
    });
};

// Utility function to align elements to the bottom
export const alignBottom = (elements) => {
    const maxY = Math.max(...elements.map(el => (el.position?.y || 0) + (el.size?.height || 0)));
    elements.forEach(el => {
        el.position.y = maxY - (el.size?.height || 0);
    });
};

// Utility function to center elements vertically
export const alignMiddle = (elements, pageHeight) => {
    const centerY = pageHeight / 2;
    elements.forEach(el => {
        el.position.y = centerY - (el.size?.height || 0) / 2;
    });
};
