import React, { useState } from "react";
import Modal from "@/components/ui/Modal";

const JumpToPage = ({ totalPages, onJump, isPdf = false }) => {
    const [pageInput, setPageInput] = useState("");
    const [modalMessage, setModalMessage] = useState("");
    const [isModalOpen, setIsModalOpen] = useState(false);

    const handleJump = () => {
        const pageNumber = parseInt(pageInput, 10);
        const adjustedTotalPages = isPdf ? totalPages : (totalPages > 1 ? totalPages - 1 : 0);

        if (!isNaN(pageNumber) && pageNumber >= 1 && pageNumber <= adjustedTotalPages) {
            onJump(pageNumber);
            setPageInput('');
        } else {
            setModalMessage(`Please enter a valid page number between 1 and ${adjustedTotalPages}`);
            setIsModalOpen(true);
        }
    };

    return (
        <>
            <div className="flex items-center border dark:bg-slate-800 shadow-lg rounded-lg w-24 h-6">
                <input
                    type="number"
                    className="border-r dark:border-gray-700 rounded-l-md text-center w-2/3 h-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Page"
                    value={pageInput}
                    onChange={(e) => setPageInput(e.target.value)}
                />
                <button
                    className="bg-blue-500 text-white rounded-r-md text-sm font-semibold w-1/3 h-full hover:bg-blue-600 transition-all"
                    onClick={handleJump}
                >
                    Go
                </button>
            </div>

            <Modal
                activeModal={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                title="Invalid Page Number"
            >
                <p className="text-gray-700 dark:text-gray-300">
                    {modalMessage}
                </p>
                <div className="flex justify-end">
                    <button
                        className="bg-blue-600 px-4 py-1 text-white rounded dark:bg-blue-800 dark:border-b dark:border-blue-700"
                        onClick={() => setIsModalOpen(false)}
                    >
                        Close
                    </button>
                </div>
            </Modal>
        </>
    );
};

export default JumpToPage;