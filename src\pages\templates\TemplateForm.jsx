import React, { useState } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import {
  TextField,
  TextArea,
  Select,
  FileUpload,
  SelectWithQuery,
} from '@/components/inputs';
import api from '@/lib/axios';
import { useQueryClient } from '@tanstack/react-query';

const TemplateForm = ({ initialValues = {}, handleModalClose = () => {} }) => {
  const queryClient = useQueryClient();

  const defaultValues = {
    title: '',
    description: '',
    is_public: 0,
    genre_id: '',
    status: 'published',
    book_type: 'ebook',
    cover_image: '',
    pdf_file: '',
    width: 794,
    height: 1123,
    orientation: 'portrait',
    margins: {
      top: 10,
      bottom: 10,
      left: 10,
      right: 10,
    },
  };

  const cleanInitialValues = {
    ...initialValues,
    cover_image: initialValues?.cover_image ?? '',
    pdf_file: initialValues?.pdf_file ?? '',
  };

  const mergedValues = { ...defaultValues, ...cleanInitialValues };

  const validationSchema = Yup.object().shape({
    title: Yup.string().required('Title is required'),
    status: Yup.string().required('Status is required'),
    book_type: Yup.string().required('Book type is required'),
    is_public: Yup.number().required('Visibility is required'),
    width: Yup.number().required('Width is required').positive(),
    height: Yup.number().required('Height is required').positive(),
  });

  const handleSubmit = async (values, { resetForm }) => {
    const formData = new FormData();
    const keysToAppend = Object.keys(initialValues?.id ? initialValues : values);

    keysToAppend.forEach((key) => {
      if (key === 'margins') {
        formData.append(key, JSON.stringify(values[key]));
      } else if (key === 'cover_image' || key === 'pdf_file') {
        if (typeof values[key] === 'string') {
          formData.append(key, ''); // Append empty string if not a file
        } else {
          formData.append(key, values[key]);
        }
      } else {
        formData.append(key, values[key]);
      }
    });

    if (initialValues?.id) {
      formData.append('_method', 'PUT');
    }

    try {
      const url = initialValues?.id
        ? `/admin/ebooks/${initialValues.id}`
        : '/admin/ebooks';
      await api.post(url, formData);
      resetForm();
      handleModalClose();
      queryClient.invalidateQueries('ebooks');
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <Formik
      initialValues={mergedValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
    >
      {({ values, dirty, isSubmitting }) => (
        <Form className="grid grid-cols-2 gap-x-4">
          <TextField label="Title" name="title" />
          <div className="col-span-2">
            <TextArea label="Description" name="description" />
          </div>
          <Select
            label="Visibility"
            name="is_public"
            options={[
              { value: 1, label: 'Public' },
              { value: 0, label: 'Private' },
            ]}
          />
          <SelectWithQuery
            label="Category"
            returnSingleValue
            name="genre_id"
            endPoint="admin/genres"
            queryKey="genres"
          />
          <Select
            label="Status"
            name="status"
            options={[
              { value: 'draft', label: 'Draft' },
              { value: 'published', label: 'Published' },
            ]}
          />
          <Select
            label="Book Type"
            name="book_type"
            options={[
              { value: 'pdf', label: 'PDF' },
              { value: 'ebook', label: 'eBook' },
            ]}
          />
          {values.book_type === 'ebook' && (
            <>
              <TextField
                label="Width"
                name="width"
                value={values.width}
              />
              <TextField
                label="Height"
                name="height"
                value={values.height}
              />
              <Select
                label="Orientation"
                name="orientation"
                options={[
                  { value: 'portrait', label: 'Portrait' },
                  { value: 'landscape', label: 'Landscape' },
                ]}
              />
            </>
          )}
          <div className="col-span-2">
            <FileUpload label="Cover Image" name="cover_image" />
          </div>
          {values.book_type === 'pdf' && (
            <div className="col-span-2">
              <FileUpload label="PDF File" name="pdf_file" />
            </div>
          )}
          <button
            type="submit"
            className={`col-span-2 py-2 px-4 rounded ${dirty && !isSubmitting ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600 cursor-not-allowed'}`}
            disabled={!dirty || isSubmitting}
          >
            {initialValues?.id ? 'Update eBook' : 'Submit eBook'}
          </button>
        </Form>
      )}
    </Formik>
  );
};

export default TemplateForm;
