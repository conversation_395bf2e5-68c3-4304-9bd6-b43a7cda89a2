// PixabayImages.jsx
import React, { useState, useEffect } from 'react';
import ImageGrid from './ImageGrid';

const PixabayImages = () => {
  const [query, setQuery] = useState('');
  const [images, setImages] = useState([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Replace with your Pixabay API Key
  const API_KEY = '**********************************';

  const fetchImages = async () => {
    setLoading(true);
    setError(null);

    const url = `https://pixabay.com/api/?key=${API_KEY}&q=${encodeURIComponent(
      query
    )}&image_type=photo&per_page=10&page=${page}`;

    try {
      const response = await fetch(url);
      const data = await response.json();
      if (data.hits) {
        setImages(data.hits);
      } else {
        setImages([]);
        setError('No images found');
      }
    } catch (error) {
      setError('Failed to fetch images');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchImages();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page]);

  const handleSearch = (e) => {
    e.preventDefault();
    setPage(1);
    fetchImages();
  };

  return (
    <div>
      {/* Search Bar */}
      <form onSubmit={handleSearch} style={{ marginBottom: '10px' }}>
        <input
          type="text"
          placeholder="Search images..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          style={{ width: '70%', padding: '5px' }}
        />
        <button type="submit" style={{ padding: '5px 10px' }}>
          Search
        </button>
      </form>

      {/* Error Message */}
      {error && <p style={{ color: 'red' }}>{error}</p>}

      {/* Loading Indicator */}
      {loading ? (
        <p>Loading...</p>
      ) : (
        <>
          {/* Image Grid */}
          <ImageGrid images={images} isPixabay />

          {/* Pagination */}
          <div style={{ marginTop: '10px', textAlign: 'center' }}>
            {page > 1 && (
              <button onClick={() => setPage(page - 1)} style={{ marginRight: '10px' }}>
                Previous
              </button>
            )}
            <button onClick={() => setPage(page + 1)}>Next</button>
          </div>
        </>
      )}
    </div>
  );
};

export default PixabayImages;
