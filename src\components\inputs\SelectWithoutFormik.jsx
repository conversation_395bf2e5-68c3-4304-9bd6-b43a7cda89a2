import React from 'react';
import ReactSelect from 'react-select';
import useDataFetching from '@/hooks/useDataFetching';

export const SelectWithoutFormik = ({
  label,
  isMulti = false,
  valueKey = 'id',
  labelKey = 'name',
  dataAccessKey = 'data.data',
  endPoint,
  queryKey,
  params = {},
  value,
  onChange,
  ...props
}) => {
  const { data, isLoading } = useDataFetching({
    queryKey,
    endPoint,
    params,
  });

  const resolveData = (data, key) =>
    key.split('.').reduce((acc, part) => acc?.[part], data) || [];

  const options = resolveData(data, dataAccessKey).map((item) => ({
    value: item[valueKey],
    label: item[labelKey],
    original: item, // To bind the full object later
  }));

  const customStyles = {
    control: (base) => ({
      ...base,
      backgroundColor: 'white',
      borderColor: '#D1D5DB',
      boxShadow: 'none',
      '&:hover': {
        borderColor: '#3B82F6',
      },
    }),
    menu: (base) => ({
      ...base,
      backgroundColor: 'white',
      border: '1px solid #D1D5DB',
    }),
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isSelected
        ? '#3B82F6'
        : state.isFocused
          ? '#F3F4F6'
          : 'transparent',
      color: state.isSelected ? 'white' : '#1F2937',
      '&:hover': {
        backgroundColor: state.isSelected ? '#3B82F6' : '#F3F4F6',
      },
    }),
  };

  // Calculate the current value for the select component
  const currentValue = isMulti
    ? options.filter((option) => value?.includes(option.value))
    : options.find((option) => option.value === value) || null;

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium mb-1 dark:text-gray-200">
        {label}
      </label>
      <ReactSelect
        isClearable
        options={options}
        isLoading={isLoading}
        isMulti={isMulti}
        value={currentValue}
        onChange={onChange}
        styles={customStyles}
        className="react-select-container"
        classNamePrefix="react-select"
        {...props}
      />
    </div>
  );
};