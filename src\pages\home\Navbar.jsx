import { useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Menu, X, Sun, Moon, LogOut, LogIn, ChevronDown } from "lucide-react";
import logo from "@/assets/ebookLogo.png";
import { useTranslation } from "react-i18next";
import LanguageSwitcher from "@/components/LanguageSwitcher";
import { useDispatch, useSelector } from "react-redux";
import { logout } from "@/store/authSlice";
import { useAuth } from "../../hooks/useAuth";

function Navbar({ darkMode, toggleDarkMode }) {
  const [isOpen, setIsOpen] = useState(false);
  const { logout } = useAuth();

  const isLoggedIn = useSelector((state) => state.auth.isAuthenticated);
  const user = useSelector((state) => state.auth.user);
  const [profileMenuOpen, setProfileMenuOpen] = useState(false);
  const profileMenuRef = useRef(null);
  const location = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  const handleLogout = () => {
    logout();
  };

  const menuItems = [
    { title: t("features"), href: "#features" },
    { title: t("how_it_works"), href: "#how-it-works" },
    { title: t("books"), href: "#books" },
    { title: t("pricing"), href: "#pricing" },
    { title: t("contact"), href: "#contact" },
  ];

  const handleClick = (href) => {
    setIsOpen(false);
    if (href.startsWith("#")) {
      if (location.pathname !== "/") {
        navigate(`/${href}`);
      } else {
        const element = document.querySelector(href);
        if (element) {
          element.scrollIntoView({ behavior: "smooth" });
        }
      }
    }
  };

  return (
    <nav className="fixed top-5 left-0 right-0 w-full z-50 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 rounded-full max-w-7xl mx-auto">
      <div className="flex justify-between h-16 px-4 sm:px-6 lg:px-8">
        <div className="flex items-center">
          <Link
            to="/"
            className="text-2xl font-bold text-gray-900 dark:text-white"
          >
            <img src={logo} alt="Logo" className="h-8 mr-2" />
          </Link>
        </div>

        {/* Desktop Menu */}
        <div className="hidden md:flex items-center space-x-6">
          {menuItems.map((item) => (
            <a
              key={item.title}
              href={
                location.pathname === "/ebooks" ? `/${item.href}` : item.href
              }
              onClick={() => handleClick(item.href)}
              className="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400"
            >
              {item.title}
            </a>
          ))}
        </div>
        <div className="flex items-center space-x-6">
          <LanguageSwitcher />
          <button
            onClick={toggleDarkMode}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            {darkMode ? (
              <Sun className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            ) : (
              <Moon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            )}
          </button>
          {isLoggedIn ? (
            <div className="relative" ref={profileMenuRef}>
              <button
                onClick={() => setProfileMenuOpen((prev) => !prev)}
                className="flex items-center space-x-2 px-4 py-2 rounded text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-300 flex items-center justify-center">
                  {user && user.profile_picture ? (
                    <img
                      src={user.profile_picture}
                      alt="Profile"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <span className="text-gray-600 text-sm font-medium">
                      {user?.name?.charAt(0).toUpperCase() || "U"}
                    </span>
                  )}
                </div>
                <span>{user?.name || "User"}</span>
                <ChevronDown
                  className={`w-4 h-4 transition-transform ${
                    profileMenuOpen ? "rotate-180" : ""
                  }`}
                />
              </button>
              <AnimatePresence>
                {profileMenuOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute right-0 top-12 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg rounded-lg overflow-hidden"
                  >
                    <Link
                      to="/admin"
                      className="block px-4 py-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      {t("Dashboard")}
                    </Link>
                    <Link
                      to="/admin/settings/profile"
                      className="block px-4 py-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      {t("Profile")}
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
                    >
                      {t("Logout")}
                    </button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          ) : (
            <Link
              to="/login"
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <LogIn className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            </Link>
          )}
          <Link
            to="/editor/1/1"
            className="px-4 py-2 text-sm font-medium rounded-full text-white bg-blue-600 hover:bg-blue-700"
          >
            {t("try_ebook_builder")}
          </Link>
        </div>

        {/* Mobile Menu Button */}
        <div className="flex items-center md:hidden">
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            {isOpen ? (
              <X className="w-6 h-6 text-gray-600 dark:text-gray-300" />
            ) : (
              <Menu className="w-6 h-6 text-gray-600 dark:text-gray-300" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isOpen && (
        <div className="mx-3 md:hidden absolute top-[calc(100%+4px)] left-0 right-0 bg-gray-100 dark:bg-gray-800 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 rounded-lg">
          <div className="px-2 pt-2 pb-3 space-y-1">
            {menuItems.map((item) => (
              <a
                key={item.title}
                href={
                  location.pathname === "/ebooks" ? `/${item.href}` : item.href
                }
                onClick={() => handleClick(item.href)}
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400"
              >
                {item.title}
              </a>
            ))}
            <div className="px-3 py-2">
              <LanguageSwitcher />
            </div>
            <div className="px-3 py-2">
              <button
                onClick={toggleDarkMode}
                className="flex items-center space-x-2 w-full rounded-md px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400"
              >
                {darkMode ? (
                  <Sun className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                ) : (
                  <Moon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                )}
              </button>
              {isLoggedIn ? (
                <button
                  onClick={handleLogout}
                  className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <LogOut className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                </button>
              ) : (
                <Link
                  to="/login"
                  className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <LogIn className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                </Link>
              )}
            </div>
            <div className="px-3 py-2">
              <Link
                to="/editor/1/1"
                className="block w-full text-center px-4 py-2 text-sm font-medium rounded-full text-white bg-blue-600 hover:bg-blue-700"
              >
                {t("try_ebook_builder")}
              </Link>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}

export default Navbar;
