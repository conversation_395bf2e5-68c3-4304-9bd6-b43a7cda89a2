import React from 'react';
import { useState } from 'react';

const ShapeProperties = ({ element, handleUpdate }) => {
    const { style } = element;
    const [fillColor, setFillColor] = useState(style?.fill || '#000000');
    const [strokeColor, setStrokeColor] = useState(style?.stroke || '#000000');
    const [strokeWidth, setStrokeWidth] = useState(style?.strokeWidth || '2');

    const handleStyleChange = (property, value) => {
        handleUpdate({
            style: { ...style, [property]: value }
        });
    };

    const handleFillColorChange = (e) => {
        setFillColor(e.target.value);
        handleStyleChange('fill', e.target.value);
    };

    const handleStrokeColorChange = (e) => {
        setStrokeColor(e.target.value);
        handleStyleChange('stroke', e.target.value);
    };

    const handleStrokeWidthChange = (e) => {
        setStrokeWidth(e.target.value);
        handleStyleChange('strokeWidth', e.target.value);
    };

    return (
        <div className="flex flex-col dark:text-white">
            <div className="flex flex-col gap-2">
                <div className="grid grid-cols-2 gap-4">
                    <label className="text-white">Fill Color:</label>
                    <input
                        type="color"
                        value={fillColor}
                        onChange={handleFillColorChange}
                        className="w-full"
                    />
                </div>
                <div className="grid grid-cols-2 gap-4">
                    <label className="text-white">Stroke Color:</label>
                    <input
                        type="color"
                        value={strokeColor}
                        onChange={handleStrokeColorChange}
                        className="w-full"
                    />
                </div>
                <div className="grid grid-cols-2 gap-4">
                    <label className="text-white">Stroke Width:</label>
                    <input
                        type="number"
                        value={strokeWidth}
                        onChange={handleStrokeWidthChange}
                        className="w-full"
                    />
                </div>
            </div>
        </div>
    );
};

export default ShapeProperties; 