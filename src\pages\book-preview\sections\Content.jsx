import React, { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Howl } from 'howler';
import { useParams } from 'react-router-dom';
import pageFlipSound from '@/assets/audio/page-flip-47177.mp3';
import FlipBookContainer from '../components/FlipBookContainer';
import PdfFlipBookContainer from '../components/PdfFlipBookContainer';
import Pagination from '../components/Pagination';
import Toolbar from '../components/Toolbar';
import PdfToolbar from '../components/PdfToolbar';
import { calculateScale } from '../store/scaleSlice';

const Content = () => {
  const [clickedPageId, setClickedPageId] = useState(null);
  const dispatch = useDispatch();
  const { size, scale } = useSelector((state) => state.scale);
  const data = useSelector((state) => state.ebook?.ebookData);
  const [pageIndex, setPageIndex] = useState(0);
  const bookRef = useRef(null);
  const [numOfPages, setNumOfPages] = useState(0);
  const { id } = useParams();

  const goToPage = (index) => {
    if (bookRef.current?.pageFlip) {
      bookRef?.current?.pageFlip()?.flip(index);
    }
  };

  const handleFlip = (e) => {
    setPageIndex(e.data);
    const sound = new Howl({ src: [pageFlipSound] });
    sound.play();
  };

  useEffect(() => {
    const handleResize = () => {
      dispatch(
        calculateScale({
          elementWidth: parseInt(data?.width),
          elementHeight: parseInt(data?.height),
        })
      );
    };

    window.addEventListener('resize', handleResize);
    dispatch(
      calculateScale({
        elementWidth: parseInt(data?.width),
        elementHeight: parseInt(data?.height),
      })
    );

    return () => {
      window.removeEventListener('resize', handleResize);
      bookRef.current = null;
    };
  }, [dispatch, id, data?.height, data?.width]);

  useEffect(() => {
    goToPage(pageIndex);
  }, [pageIndex]);

  useEffect(() => {
    setPageIndex(0);
    // Handle both paginated and non-paginated data structures
    if (data?.pages?.data) {
      // Paginated structure
      setNumOfPages(data.pages.data.length);
    } else if (data?.pages) {
      // Non-paginated structure
      setNumOfPages(data.pages.length);
    } else {
      setNumOfPages(0);
    }
  }, [data]);

  return (
    <div className={`relative my-auto ${window.innerWidth > 768 ? 'mt-16' : ''}`}>
      {/* Conditionally Render Toolbars */}
      {data?.book_type === 'ebook' ? <Toolbar clickedPageId={clickedPageId} /> : <PdfToolbar />}

      {/* Flipbook Container */}
      {data?.book_type === 'ebook' && (
        <FlipBookContainer
          setNumOfPages={setNumOfPages}
          data={data}
          pageIndex={pageIndex}
          onFlip={handleFlip}
          bookRef={bookRef}
          setPageIndex={setPageIndex}
          setClickedPageId={setClickedPageId}
        />
      )}

      {/* PDF Viewer Container */}
      {data?.book_type === 'pdf' && (
        <PdfFlipBookContainer
          pdfUrl={data.pdf_file_url}
          pageIndex={pageIndex}
          setPageIndex={setPageIndex}
        />
      )}
    </div>
  );
};

export default Content;
