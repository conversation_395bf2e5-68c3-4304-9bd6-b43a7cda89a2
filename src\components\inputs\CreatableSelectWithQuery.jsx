import React, { useState } from 'react';
import { useField } from 'formik';
import CreatableSelect from 'react-select/creatable';
import useDataFetching from '@/hooks/useDataFetching';
import api from '@/lib/axios';

export const CreatableSelectWithQuery = ({
    label,
    isMulti = false,
    valueKey = 'id',
    labelKey = 'name',
    dataAccessKey = 'data.data',
    endPoint,
    createEndpoint,
    queryKey,
    params = {},
    returnSingleValue = false,
    onCreate,
    ...props
}) => {
    const [field, meta, helpers] = useField(props);
    const [localOptions, setLocalOptions] = useState([]);
    const { data, isLoading, refetch } = useDataFetching({ queryKey, endPoint, params });
    const resolveData = (data, key) =>
        key.split('.').reduce((acc, part) => acc?.[part], data) || [];
    const fetchedOptions = resolveData(data, dataAccessKey).map((item) => ({
        value: item[valueKey],
        label: item[labelKey],
        original: item,
    }));
    const options = [...fetchedOptions, ...localOptions];
    const customStyles = {
        control: (base) => ({
            ...base,
            backgroundColor: 'white',
            borderColor: '#D1D5DB',
            boxShadow: 'none',
            '&:hover': { borderColor: '#3B82F6' },
        }),
        menu: (base) => ({
            ...base,
            backgroundColor: 'white',
            border: '1px solid #D1D5DB',
        }),
        option: (base, state) => ({
            ...base,
            backgroundColor: state.isSelected
                ? '#3B82F6'
                : state.isFocused
                    ? '#F3F4F6'
                    : 'transparent',
            color: state.isSelected ? 'white' : '#1F2937',
            '&:hover': {
                backgroundColor: state.isSelected ? '#3B82F6' : '#F3F4F6',
            },
        }),
    };

    const handleCreate = async (inputValue) => {
        if (onCreate) {
            onCreate(inputValue);
            return;
        }
        try {
            const endpointToUse = createEndpoint ? createEndpoint : endPoint.split('?')[0];
            await api.post(endpointToUse, { [labelKey]: inputValue });
            const { data: newData } = await refetch();
            const newFetchedOptions = resolveData(newData, dataAccessKey)
                .map((item) => ({
                    value: item[valueKey],
                    label: item[labelKey],
                    original: item,
                }));
            const newOption = newFetchedOptions.find(
                (opt) => opt.label === inputValue
            );
            if (newOption) {
                if (isMulti) {
                    helpers.setValue([
                        ...(field.value || []),
                        newOption.value,
                    ]);
                } else {
                    helpers.setValue(
                        returnSingleValue
                            ? newOption.value
                            : newOption.original
                    );
                }
            }
            setLocalOptions([]);
        } catch (error) {
            console.error('Error creating new option:', error);
        }
    };

    const currentValue = isMulti
        ? options.filter((option) => field.value?.includes(option.value))
        : options.find((option) => option.value === field.value) || null;

    return (
        <div className="mb-4">
            <label className="block text-sm font-medium mb-1 dark:text-gray-200">
                {label}
            </label>
            <CreatableSelect
                options={options}
                isLoading={isLoading}
                isMulti={isMulti}
                value={currentValue}
                onChange={(selected) => {
                    if (isMulti) {
                        helpers.setValue(selected?.map((item) => item.value) || []);
                    } else {
                        helpers.setValue(
                            returnSingleValue ? selected?.value || '' : selected?.original || null
                        );
                    }
                }}
                onCreateOption={handleCreate}
                styles={customStyles}
                className="react-select-container"
                classNamePrefix="react-select"
                {...props}
            />
            {meta.touched && meta.error && (
                <div className="text-red-600 text-sm mt-1 dark:text-red-400">
                    {meta.error}
                </div>
            )}
        </div>
    );
};
