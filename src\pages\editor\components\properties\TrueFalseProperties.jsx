import React from 'react';
import { useDispatch } from 'react-redux';
import { updateElement } from '../../store/pagesSlice';

const TrueFalseProperties = ({ element, handleUpdate }) => {
  const dispatch = useDispatch();
  const { content } = element;

  const handleCorrectAnswerChange = (answer) => {
    dispatch(
      updateElement({
        id: element.id,
        updates: {
          content: {
            ...content,
            correctAnswer: answer,
          },
        },
      })
    );
  };

  return (
    <div className="space-y-4">
      <div>
        <label htmlFor={`question-${element.id}`} className="block text-sm font-medium mb-1">
          Question
        </label>
        <input
          id={`question-${element.id}`}
          type="text"
          value={content.question}
          onChange={(e) =>
            handleUpdate({
              content: { ...content, question: e.target.value },
            })
          }
          className="w-full p-2 border rounded-md"
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Correct Answer</label>
        <div className="flex items-center mt-2">
          <input
            id={`true-${element.id}`}
            type="radio"
            name={`tf-${element.id}`}
            value="true"
            checked={content.correctAnswer === true}
            onChange={() => handleCorrectAnswerChange(true)}
            className="mr-2"
          />
          <label htmlFor={`true-${element.id}`} className="text-sm font-medium">
            True
          </label>
        </div>
        <div className="flex items-center mt-2">
          <input
            id={`false-${element.id}`}
            type="radio"
            name={`tf-${element.id}`}
            value="false"
            checked={content.correctAnswer === false}
            onChange={() => handleCorrectAnswerChange(false)}
            className="mr-2"
          />
          <label htmlFor={`false-${element.id}`} className="text-sm font-medium">
            False
          </label>
        </div>
      </div>
    </div>
  );
};

export default TrueFalseProperties;
