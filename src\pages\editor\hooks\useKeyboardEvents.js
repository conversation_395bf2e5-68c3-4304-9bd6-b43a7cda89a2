// useKeyboardEvents.jsx
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  updateElement,
  deleteElement,
  deleteElements,
  copyElements,
  cutElements,
  pasteElements,
} from '../store/pagesSlice';

/**
 * Custom hook to handle keyboard events for the Page component.
 *
 * @param {Array} elements - Array of all elements in the page.
 * @param {Array} selectedElements - Array of currently selected element IDs.
 * @param {string|null} editableElementId - ID of the element currently being edited.
 */
const useKeyboardEvents = (elements, selectedElements, editableElementId) => {
  const dispatch = useDispatch();
  const copiedElements = useSelector((state) => state.pages.present.copiedElements);

  useEffect(() => {
    const handleKeyDown = (e) => {
      // Define keys that should be allowed even when editing
      const isPaste = (e.key === 'v' || e.key === 'V') && (e.ctrlKey || e.metaKey);
      const isCopy = (e.key === 'c' || e.key === 'C') && (e.ctrlKey || e.metaKey);
      const isCut = (e.key === 'x' || e.key === 'X') && (e.ctrlKey || e.metaKey);

      // If editing an element, only allow paste, copy, and cut actions
      if (editableElementId && !isPaste && !isCopy && !isCut) return;

      // Check if the focus is on an input, textarea, or select element
      const activeElement = document.activeElement;
      if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA' || activeElement.tagName === 'SELECT')) {
        return; // If it is, do not prevent default behavior
      }

      // If no elements are selected, do nothing for movement and deletion keys
      const isMovementKey = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key);
      const isDeleteKey = ['Delete', 'Backspace'].includes(e.key);
      const isModifierKey = e.ctrlKey || e.metaKey;

      // Handle movement keys
      if (isMovementKey && selectedElements.length > 0) {
        e.preventDefault();
        selectedElements.forEach((selectedElementId) => {
          const selectedElement = elements.find((el) => el.id === selectedElementId);
          if (selectedElement) {
            const delta = 5;
            const newPosition = { ...selectedElement.position };

            switch (e.key) {
              case 'ArrowUp':
                newPosition.y -= delta;
                break;
              case 'ArrowDown':
                newPosition.y += delta;
                break;
              case 'ArrowLeft':
                newPosition.x -= delta;
                break;
              case 'ArrowRight':
                newPosition.x += delta;
                break;
              default:
                break;
            }

            dispatch(updateElement({ id: selectedElementId, updates: { position: newPosition } }));
          }
        });
        return;
      }

      switch (e.key) {
        case 'Delete':
        case 'Backspace':
          if (selectedElements.length > 0 && !editableElementId) {
            e.preventDefault();
            dispatch(deleteElements());
          }
          return;
        case 'c':
        // case 'C':
        //   if (isCopy) { // Handle Ctrl + C or Cmd + C for copying
        //     e.preventDefault();
        //     dispatch(copyElements(selectedElements));
        //   }
          return;
        case 'x':
        case 'X':
          if (isCut) { // Handle Ctrl + X or Cmd + X for cutting
            e.preventDefault();
            dispatch(cutElements(selectedElements));
          }
          return;
        case 'v':
        case 'V':
          if (isPaste) { // Handle Ctrl + V or Cmd + V for pasting
            if (copiedElements.length > 0) {
              e.preventDefault();
              dispatch(pasteElements());
            }
          }
          return;
        default:
          return; // Do nothing for other keys
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    // Cleanup the event listener on unmount
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [dispatch, elements, selectedElements, editableElementId, copiedElements]);

  return null; // This hook does not return anything
};

export default useKeyboardEvents;
