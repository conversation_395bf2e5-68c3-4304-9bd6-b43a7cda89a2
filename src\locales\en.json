{"trusted_by": "Trusted by 1000+ educators", "future_of": "The Future of", "digital_education": "Digital Education", "create_stunning_ebooks": "Create stunning interactive eBooks that transform the way students learn, engage, and retain information.", "try_ebook_builder": "Try eBook Builder", "read_books": "Read Books", "books_created": "10k+ Books Created", "coppa_compliant": "COPPA Compliant", "interactive_learning_platform": "Interactive Learning Platform", "interactive_content": "Interactive Content", "interactive_content_desc": "Engage with multimedia elements", "smart_assessment": "Smart Assessment", "smart_assessment_desc": "Track learning progress", "features": "Features", "how_it_works": "How It Works", "books": "Books", "contact": "Contact", "bookSection.exploreLibrary": "Explore Our Digital Library", "bookSection.browseDescription": "Browse through our collection of interactive eBooks and discover new knowledge", "bookSection.error": "Something went wrong. Please try again later.", "bookSection.showMore": "Show More", "home.transformContent": "Transform Your Content", "home.engagingPublications": "Everything you need to create engaging digital publications", "home.howItWorksTitle": "How It Works", "home.howItWorksDescription": "Create and publish your interactive eBook in four simple steps", "home.getInTouch": "Get in Touch", "home.readyToStart": "Ready to Start Creating?", "home.joinPublishers": "Join thousands of publishers creating interactive content", "home.startPublishing": "Start Publishing", "home.features.interactiveEbooks.title": "Interactive eBooks", "home.features.interactiveEbooks.description": "Create engaging digital books with interactive elements and multimedia content", "home.features.mcqAssessments.title": "MCQ & Assessments", "home.features.mcqAssessments.description": "Build comprehensive quizzes and assessments to enhance learning outcomes", "home.features.flashcards.title": "Flashcards", "home.features.flashcards.description": "Design dynamic flashcards for effective memorization and revision", "home.features.accessibility.title": "Accessibility", "home.features.accessibility.description": "Ensure your content reaches everyone with built-in accessibility features", "home.features.richMedia.title": "Rich Media", "home.features.richMedia.description": "Embed videos and interactive media to create immersive learning experiences", "home.features.publisherTools.title": "Publisher Tools", "home.features.publisherTools.description": "Professional publishing tools for content distribution and management", "home.howItWorks.step1.title": "Create Your Content", "home.howItWorks.step1.description": "Start with your existing content or create new material using our intuitive editor", "home.howItWorks.step2.title": "Add Interactivity", "home.howItWorks.step2.description": "Enhance your content with quizzes, flashcards, and multimedia elements", "home.howItWorks.step3.title": "Customize Design", "home.howItWorks.step3.description": "Choose from beautiful templates or create your own custom design", "home.howItWorks.step4.title": "Publish & Share", "home.howItWorks.step4.description": "Publish your interactive eBook and share it with your audience", "home.sendMessage": "Send Message", "footer.product": "Product", "footer.features": "Features", "footer.howItWorks": "How it Works", "footer.pricing": "Pricing", "footer.faq": "FAQ", "footer.company": "Company", "footer.about": "About", "footer.blog": "Blog", "footer.careers": "Careers", "footer.contact": "Contact", "footer.resources": "Resources", "footer.documentation": "Documentation", "footer.templates": "Templates", "footer.examples": "Examples", "footer.support": "Support", "footer.legal": "Legal", "footer.privacy": "Privacy", "footer.terms": "Terms", "footer.security": "Security", "footer.allRightsReserved": "All rights reserved.", "authorForm.name": "Name", "authorForm.nameRequired": "Name is required", "authorForm.status": "Status", "authorForm.statusRequired": "Status is required", "authorForm.active": "Active", "authorForm.inactive": "Inactive", "authorForm.uploadPhoto": "Upload Photo", "authorForm.unsupportedFileFormatOrSize": "Unsupported File Format or Size", "authorForm.photoRequired": "Photo is required", "authorForm.unsupportedFileFormat": "Unsupported File Format", "authorForm.fileSizeTooLarge": "File Size is too large", "authorForm.bio": "Bio", "authorForm.bioRequired": "Bio is required", "authorForm.updateAuthor": "Update Author", "authorForm.addAuthor": "Add Author", "authors.authorsList": "Authors List", "authors.name": "Name", "authors.photo": "Photo", "authors.status": "Status", "authors.active": "Active", "authors.inactive": "Inactive", "authors.action": "Action", "authors.addAuthor": "Add Author", "authors.editAuthor": "Edit Author", "inquiries.title": "Inquiries", "inquiries.name": "Name", "inquiries.email": "Email", "inquiries.subject": "Subject", "inquiries.status": "Status", "inquiries.createdAt": "Created At", "inquiries.action": "Action", "inquiries.modalTitle": "Inquiry Details", "genreForm.name": "Name", "genreForm.status": "Status", "genreForm.active": "Active", "genreForm.inactive": "Inactive", "genreForm.uploadThumbnail": "Upload Thumbnail", "genreForm.addGenre": "Add Category", "genreForm.updateGenre": "Update Category", "genreForm.validation.nameRequired": "Name is required", "genreForm.validation.statusRequired": "Status is required", "genreForm.validation.thumbnailRequired": "Thumbnail is required", "genreForm.validation.unsupportedFormat": "Unsupported File Format", "genreForm.validation.fileTooLarge": "File Size is too large", "genreForm.validation.fileInvalid": "Unsupported File Format or Size", "genresPage.title": "Genres List", "genresPage.name": "Name", "genresPage.image": "Image", "genresPage.status": "Status", "genresPage.active": "Active", "genresPage.inactive": "Inactive", "genresPage.action": "Action", "genresPage.addGenre": "Add Category", "genresPage.editGenre": "Edit Category", "genresPage.submitError": "Error submitting form", "genresPage.deleteError": "Error deleting genre", "userForm.name": "Name", "userForm.email": "Email", "userForm.password": "Password", "userForm.role": "Role", "userForm.isActive": "Is Active?", "userForm.profilePicture": "Profile Picture", "userForm.active": "Active", "userForm.inactive": "Inactive", "userForm.admin": "Admin", "userForm.author": "Author", "userForm.user": "User", "userForm.createUser": "Create User", "userForm.updateUser": "Update User", "userForm.validation.nameRequired": "Name is required", "userForm.validation.emailRequired": "Email is required", "userForm.validation.invalidEmail": "Invalid email address", "userForm.validation.passwordRequired": "Password is required", "userForm.validation.passwordMin": "Password must be at least 6 characters", "userForm.validation.roleRequired": "Role is required", "userForm.submitError": "Error submitting form", "usersList.title": "Users List", "usersList.name": "Name", "usersList.email": "Email", "usersList.role": "Role", "usersList.status": "Status", "usersList.active": "Active", "usersList.inactive": "Inactive", "usersList.action": "Action", "usersList.addUser": "Add User", "usersList.editUser": "Edit User", "usersList.deleteError": "Error deleting user", "userView.loading": "Loading...", "userView.user": "User", "userView.backToList": "Back to Users list", "userView.name": "Name", "userView.email": "Email", "userView.role": "Role", "userView.profilePicture": "Profile Picture", "userView.status": "Status", "userView.active": "Active", "userView.inactive": "Inactive", "accessDenied.title": "Access Denied", "accessDenied.message": "You don't have permission to access this page.", "accessDenied.goHome": "Go to Homepage", "profile.title": "Profile Settings", "profile.profilePicture": "Profile Picture", "profile.uploadProfilePicture": "Upload Profile Picture", "profile.fullName": "Full Name", "profile.currentPassword": "Current Password", "profile.newPassword": "New Password", "profile.confirmNewPassword": "Confirm New Password", "profile.role": "Role", "profile.saving": "Saving...", "profile.saveChanges": "Save Changes", "profile.updateSuccess": "Profile updated successfully!", "profile.updateFail": "Failed to update profile. Please try again.", "profile.updateError": "Error updating profile", "profile.validation.nameRequired": "Full Name is required", "profile.validation.passwordMin": "Password must be at least 6 characters", "profile.validation.newPasswordRequired": "New password is required", "profile.validation.confirmPasswordRequired": "Confirm password is required", "profile.validation.passwordMismatch": "Passwords must match", "profile.mobile": "Mobile", "profile.bio": "Bio", "profile.address": "Address", "security.title": "Security Settings", "security.changePassword": "Change Password", "security.currentPassword": "Current Password", "security.newPassword": "New Password", "security.confirmNewPassword": "Confirm New Password", "security.updating": "Updating...", "security.updatePassword": "Update Password", "security.updateSuccess": "Password updated successfully!", "security.updateError": "Error updating password", "security.validation.required": "Required", "security.validation.passwordMin": "Password must be at least 8 characters", "security.validation.passwordMismatch": "Passwords must match", "dashboard.title": "Dashboard Overview", "dashboard.totalUnpublishedBooks": "Total Unpublished Books", "dashboard.totalPublishedBooks": "Total Published Books", "dashboard.totalEbooks": "Total eBooks", "dashboard.totalGenres": "Total Genres", "dashboard.recentEbooks": "Recent eBooks", "dashboard.pendingEbooks": "Pending eBooks", "dashboard.titleColumn": "Title", "dashboard.authorColumn": "Author", "dashboard.genreColumn": "Category", "dashboard.loading": "Loading...", "dashboard.noData": "No data available", "Dashboard": "Dashboard", "Genres": "Genres", "eBook": "eBook", "Templates": "Templates", "Users": "Users", "Inquiries": "Inquiries", "Authors": "Authors", "Settings": "Settings", "Profile": "Profile", "Security": "Security", "Logout": "Logout", "Coupons": "Coupons", "home.pricing.title": "Simple, Transparent Pricing", "home.pricing.subtitle": "Choose the perfect plan for your publishing needs", "home.pricing.popular": "Popular", "home.pricing.basic.name": "Basic", "home.pricing.basic.price": "$29", "home.pricing.basic.description": "Perfect for getting started with interactive ebooks", "home.pricing.basic.features.ebooks": "Up to 5 ebooks", "home.pricing.basic.features.storage": "5GB storage", "home.pricing.basic.features.support": "Email support", "home.pricing.basic.features.analytics": "Basic analytics", "home.pricing.basic.cta": "Get Started", "home.pricing.pro.name": "Professional", "home.pricing.pro.price": "$99", "home.pricing.pro.description": "Everything you need for professional publishing", "home.pricing.pro.features.unlimited": "Unlimited ebooks", "home.pricing.pro.features.advanced": "Advanced interactive features", "home.pricing.pro.features.priority": "Priority support", "home.pricing.pro.features.customization": "Custom branding", "home.pricing.pro.features.collaboration": "Team collaboration", "home.pricing.pro.cta": "Start Free Trial", "home.pricing.enterprise.name": "Enterprise", "home.pricing.enterprise.price": "Custom", "home.pricing.enterprise.description": "Custom solutions for large organizations", "home.pricing.enterprise.features.custom": "Custom solutions", "home.pricing.enterprise.features.dedicated": "Dedicated support", "home.pricing.enterprise.features.sla": "SLA guarantee", "home.pricing.enterprise.features.api": "API access", "home.pricing.enterprise.features.training": "Team training", "home.pricing.enterprise.cta": "Contact Sales", "pricing": "Pricing", "couponsPage.title": "Coupons List", "couponsPage.code": "Code", "couponsPage.discountType": "Discount Type", "couponsPage.appliesTo": "Applies To", "couponsPage.validPeriod": "Valid Period", "couponsPage.status": "Status", "couponsPage.action": "Action", "couponsPage.active": "Active", "couponsPage.inactive": "Inactive", "couponsPage.addCoupon": "Add Coupon", "couponsPage.editCoupon": "Edit Coupon", "couponsPage.item": "<PERSON><PERSON>", "couponsPage.submitError": "Error submitting coupon", "couponsPage.deleteError": "Error deleting coupon", "couponForm.code": "Coupon Code", "couponForm.discountType": "Discount Type", "couponForm.percentage": "Percentage", "couponForm.fixed": "Fixed Amount", "couponForm.discountValue": "Discount Value", "couponForm.appliesTo": "Applies To", "couponForm.entireOrder": "Entire Order", "couponForm.specificItem": "Specific Item", "couponForm.itemId": "Item ID", "couponForm.minOrderAmount": "Minimum Order Amount", "couponForm.maxDiscount": "Maximum Discount", "couponForm.usageLimit": "Usage Limit", "couponForm.startDate": "Start Date", "couponForm.endDate": "End Date", "couponForm.status": "Status", "couponForm.active": "Active", "couponForm.inactive": "Inactive", "couponForm.addCoupon": "Add Coupon", "couponForm.updateCoupon": "Update Coupon", "couponForm.validation.codeRequired": "Coupon code is required", "couponForm.validation.invalidDiscountType": "Invalid discount type", "couponForm.validation.discountTypeRequired": "Discount type is required", "couponForm.validation.discountValueRequired": "Discount value is required", "couponForm.validation.minDiscount": "Discount must be greater than or equal to 0", "couponForm.validation.appliesToRequired": "Applies to is required", "couponForm.validation.invalidAppliesTo": "Invalid applies to value", "couponForm.validation.itemIdRequired": "Item ID is required for item-specific coupons", "couponForm.validation.minOrderAmount": "Minimum order amount must be greater than or equal to 0", "couponForm.validation.minOrderAmountRequired": "Minimum order amount is required", "couponForm.validation.minMaxDiscount": "Maximum discount must be greater than or equal to 0", "couponForm.validation.maxDiscountRequired": "Maximum discount is required", "couponForm.validation.minUsageLimit": "Usage limit must be greater than or equal to 0", "couponForm.validation.usageLimitRequired": "Usage limit is required", "couponForm.validation.startDateRequired": "Start date is required", "couponForm.validation.endDateRequired": "End date is required", "couponForm.validation.endDateAfterStart": "End date must be after start date", "couponForm.validation.invalidStatus": "Invalid status", "couponForm.validation.statusRequired": "Status is required", "publishers.name": "Name", "publishers.mobile": "Mobile", "publishers.email": "Email", "publishers.tradeLicense": "Trade License Number", "publishers.status": "Status", "publishers.active": "Active", "publishers.inactive": "Inactive", "publishers.action": "Action", "publishers.editPublisher": "Edit Publisher", "publishers.addPublisher": "Add Publisher", "publishers.deleteError": "Failed to delete publisher.", "publishers.submitError": "Failed to submit form.", "publishers.title": "Publishers List", "publishers.form.name": "Name", "publishers.form.mobile": "Mobile", "publishers.form.nid": "NID", "publishers.form.nidImage": "NID Image", "publishers.form.proprietorPhoto": "Proprietor Photo", "publishers.form.proprietorSignature": "Proprietor Signature", "publishers.form.presentAddress": "Present Address", "publishers.form.permanentAddress": "Permanent Address", "publishers.form.officePresentAddress": "Office Present Address", "publishers.form.tradeLicenseNumber": "Trade License Number", "publishers.form.tradeLicenseImage": "Trade License Image", "publishers.form.logo": "Logo", "publishers.form.etin": "ETIN", "publishers.form.vatNumber": "VAT Number", "publishers.form.officeMobile": "Office Mobile", "publishers.form.officePhone": "Office Phone", "publishers.form.officeEmail": "Office Email", "publishers.form.officePermanentAddress": "Office Permanent Address", "publishers.form.cancel": "Cancel", "publishers.form.add": "Add", "publishers.form.edit": "Edit", "publishers.form.email": "Email", "publishers.form.password": "Password", "publishers.form.confirmPassword": "Confirm Password", "publishers.form.publisherLogin": "Publisher Login", "publishers.form.passwordMin": "Password must be at least 8 characters", "publishers.form.passwordRequired": "Password is required", "publishers.form.confirmPasswordRequired": "Confirm Password is required", "publishers.form.passwordMismatch": "Passwords do not match", "category.list": "Category List", "static.page": "Static Page List", "static.addStaticPage": "Add Static Page", "title": "Title", "staticForm.content": "Content", "staticForm.slug": "Slug", "static.editStatic": "Update Static Page", "update": "Update", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "approval.status": "Approval Status", "publishers.form.emailRequired": "Email is required", "publishers.form.nameRequired": "Publisher name is required", "publishers.form.invalidEmail": "Invalid email format", "Manage eBooks": "Manage eBooks", "Publishers": "Publishers", "Categories": "Categories", "Orders": "Orders", "Search eBooks": "Search eBooks", "Author": "Author", "Publisher": "Publisher", "Book Type": "Book Type", "Status": "Status", "Approval Status": "Approval Status", "ebook.title": "eBooks", "ebook.button.create": "Create Book", "ebook.modal.title.view": "Book Basic Information", "ebook.modal.title.edit": "Edit Book Basic Information", "ebook.modal.title.create": "Create New Book", "ebook.button.tooltip.view": "View Book", "ebook.button.tooltip.viewInfo": "View Basic Info", "ebook.button.tooltip.editInfo": "Edit Basic Info", "ebook.button.tooltip.design": "Design with Editor", "ebook.button.tooltip.template": "Save as Template", "ebook.button.tooltip.delete": "Delete Book", "ebook.delete.confirm.title": "Are you sure you want to delete this ebook?", "ebook.delete.confirm.text": "This ebook", "ebook.delete.confirm.description": "will be deleted permanently.", "ebook.delete.confirm.cancel": "Cancel", "ebook.delete.confirm.confirm": "Delete", "ebook.form.fields.title": "Title", "ebook.form.fields.authors": "Select Authors", "ebook.form.fields.description": "Description", "ebook.form.fields.categories": "Select Categories", "ebook.form.fields.status": "Status", "ebook.form.fields.status.options.draft": "Draft", "ebook.form.fields.status.options.published": "Published", "ebook.form.fields.bookType": "Book Type", "ebook.form.fields.bookType.options.ebook": "eBook", "ebook.form.fields.bookType.options.pdf": "PDF", "ebook.form.fields.pageDimensions": "Page Dimensions", "ebook.form.fields.orientation": "Orientation", "ebook.form.fields.orientation.options.portrait": "Portrait", "ebook.form.fields.orientation.options.landscape": "Landscape", "ebook.form.fields.pdfFile": "PDF File", "ebook.form.fields.coverImage": "Cover Image", "ebook.form.fields.buttons.update": "Update eBook", "ebook.form.fields.buttons.submit": "Submit eBook", "ebook.form.fields.price": "Price", "ebook.form.fields.discount": "Discount", "ebook.form.fields.finalPrice": "Final Price", "ebook.form.fields.pageDimension": "Page Dimensions", "ebook.form.fields.width": "<PERSON><PERSON><PERSON>", "ebook.form.fields.height": "Height", "fileInput.dragActive": "Drop the file here...", "fileInput.dragInactive": "Drag & drop or click to select", "ebook.modal.createTitle": "Create New Book", "ebook.modal.editTitle": "Edit Book Basic Information"}