# Dependencies
node_modules/
.pnp/
.pnp.js

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
docusaurus-debug.log*
.babelrc
.eslintcache

# Editor settings
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Production build
build/
dist/
out/

# Testing
coverage/
*.lcov

# Miscellaneous
.DS_Store
Thumbs.db
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
public/tinymce

# Linting and formatting
.eslintcache
.prettier/
prettier.log

# Storybook files
.storybook/
*.stories.mdx
*.stories.js
*.stories.jsx
*.stories.ts
*.stories.tsx

# Next.js
.next/
.vercel/
*.vercel

# TypeScript
*.tsbuildinfo

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
pnpm-debug.log*
lerna-debug.log*

# IDE Specific
.vscode/
.idea/

# Parcel cache
.cache

# Jest cache
jest/
jest.config.js
jest.config.ts
