import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Icon } from "@iconify/react";
import api from "@/lib/axios";
import useDataFetching from '@/hooks/useDataFetching';
import { useQueryClient } from '@tanstack/react-query';
import Confirm from '@/components/ui/Confirm';
import { useDispatch } from 'react-redux';
import Modal from '@/components/ui/Modal';

const Uploads = () => {
    const [selected, setSelected] = useState('image');
    const [activeModal, setActiveModal] = useState(false);
    const [currentAudio, setCurrentAudio] = useState(null);
    const queryClient = useQueryClient();
    const dispatch = useDispatch();

    const handleSelect = (type) => {
        setSelected(type);
    };
    const getClassName = (type) => {
        return selected === type
            ? 'text-white border-b-2 border-white pb-1 my-2'
            : 'text-white border-white bg-gray-800 pb-1 my-2';
    };

    const onDrop = useCallback((acceptedFiles) => {
        const file = acceptedFiles[0];
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', selected);

        api.filepost('/admin/attachments', formData)
            .then(response => {
                console.log('File uploaded successfully:', response);
                queryClient.invalidateQueries('attachments');
            })
            .catch(error => {
                console.error('Error uploading file:', error);
            });
    }, [selected, queryClient]);

    const { getRootProps, getInputProps } = useDropzone({ onDrop });

    const handleDelete = (id) => {
        Confirm(() => {
            api.delete(`/admin/attachments/${id}`)
                .then(response => {
                    console.log('File deleted successfully:', response);
                })
                .catch(error => {
                    console.error('Error deleting file:', error);
                })
                .finally(() => {
                    queryClient.invalidateQueries('attachments');
                });
        });
    };

    const handleItemClick = (item) => {
        let tag;
        if (selected === 'image') {
            tag = `<img src="${item.full_url}" alt="${item.name}" style="width: 100%; height: auto;"/>`;
        } else if (selected === 'video') {
            tag = `<video src="${item.full_url}" controls style="width: 100%; height: auto;"/>`;
        } else if (selected === 'audio') {
            tag = `<audio controls>
                <source src="${item.full_url}" type="audio/mpeg">
                Your browser does not support the audio element.
            </audio>`;
        }
        const editor = window.tinymce.activeEditor;
        editor.insertContent(tag);
    };

    const { data, isLoading, isError } = useDataFetching({
        queryKey: 'attachments',
        endPoint: '/admin/attachments'
    });

    if (isLoading) {
        console.log('Loading attachments...');
    }

    if (isError) {
        console.error('Error retrieving attachments:', isError);
    } else {
        console.log('Retrieved attachments successfully:', data);
    }

    const handleAudioClick = (item) => {
        setCurrentAudio(item.full_url);
        setActiveModal(true);
    };

    return (
        <>
            <div {...getRootProps()} className="w-full ">
                <input {...getInputProps()} />
                <button className="py-2 w-full bg-gray-700 text-white rounded hover:bg-gray-500">
                    Upload {selected === 'image' ? 'Image' : selected === 'video' ? 'Video' : 'Audio'}
                </button>
            </div>
            <div className="flex justify-start items-center mb-4 w-full">
                <div className="flex items-center w-full">
                    <button className={`py-2 px-4 w-1/3 border-b-2 ${selected === 'image' ? 'border-blue-500' : 'border-gray-300'}`} onClick={() => handleSelect('image')}>Image</button>
                    <button className={`py-2 px-4 w-1/3 border-b-2 ${selected === 'video' ? 'border-blue-500' : 'border-gray-300'}`} onClick={() => handleSelect('video')}>Video</button>
                    <button className={`py-2 px-4 w-1/3 border-b-2 ${selected === 'audio' ? 'border-blue-500' : 'border-gray-300'}`} onClick={() => handleSelect('audio')}>Audio</button>
                </div>
            </div>

            {data?.data?.data.filter(item => item.type === selected).length === 0 ? (
                <div className="text-center mt-4">No data found</div>
            ) : (
                <>
                    <h3 className="text-gray-400 text-sm uppercase">Uploaded Files</h3>
                    <div className={`grid grid-cols-${selected === 'image' || selected === 'audio' ? 2 : 1} gap-2 mt-4`}>
                        {data?.data?.data.filter(item => item.type === selected).map((item) => (
                            <div
                                key={item.id}
                                className={`border border-white relative flex flex-col items-center`}
                                onClick={() => handleItemClick(item)}
                            >
                                {selected === 'image' && (
                                    <img
                                        src={item.full_url}
                                        alt={item.name}
                                        className={`w-full ${selected === 'image' ? 'col-span-2' : 'col-span-1'}`}
                                    />
                                )}
                                {selected === 'video' && (
                                    <video src={item.full_url} className="w-full h-auto" autoPlay />
                                )}
                                {selected === 'audio' && (
                                    <div className="w-full h-[80px] flex justify-center items-center cursor-pointer">
                                        <Icon icon="bi:play-circle" style={{ fontSize: '40px', cursor: 'pointer' }} onClick={() => handleAudioClick(item)} className="hover:text-yellow-400 text-white" />
                                    </div>
                                )}
                                <button
                                    className="absolute top-0 right-0 p-1 bg-red-500 text-white rounded-full"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handleDelete(item.id);
                                    }}
                                >
                                    <Icon icon="bi:trash" style={{ fontSize: '10px' }} />
                                </button>
                            </div>
                        ))}
                    </div>
                </>
            )}

            <Modal activeModal={activeModal} onClose={() => setActiveModal(false)} title="Audio Player">
                {currentAudio && (
                    <audio controls style={{ width: '100%' }}>
                        <source src={currentAudio} type="audio/mpeg" />
                        Your browser does not support the audio element.
                    </audio>
                )}
            </Modal>
        </>
    );
};

export default Uploads;
