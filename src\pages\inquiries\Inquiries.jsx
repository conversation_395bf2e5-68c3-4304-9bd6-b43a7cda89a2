import React, { useState, useEffect } from "react";
import useDataFetching from "@/hooks/useDataFetching";
import DataTable from "@/components/ui/DataTable";
import Modal from "@/components/ui/Modal";
import InquiryDetails from "./InquiryDetails";
import moment from "moment";
import { Eye } from "lucide-react";
import { useTranslation } from "react-i18next";

const Inquiries = () => {
  const { t } = useTranslation();

  const [state, setState] = useState({
    page: 1,
    pageSize: 10,
    search: "",
    debouncedSearch: "",
  });

  const [selectedInquiry, setSelectedInquiry] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const { data: inquiriesData, isLoading, refetch } = useDataFetching({
    queryKey: ["inquiriesList", state.page, state.pageSize, state.debouncedSearch],
    endPoint: `admin/tickets?page=${state.page}&per_page=${state.pageSize}&search=${state.debouncedSearch}`,
  });

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setState((prev) => ({ ...prev, debouncedSearch: state.search }));
    }, 500);

    return () => clearTimeout(delayDebounceFn);
  }, [state.search]);

  const handleViewClick = (inquiry) => {
    setSelectedInquiry(inquiry);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedInquiry(null);
  };

  return (
    <>
      <DataTable
        title={t("inquiries.title")}
        columns={[
          { Header: t("inquiries.name"), accessor: "name" },
          { Header: t("inquiries.email"), accessor: "email" },
          { Header: t("inquiries.subject"), accessor: "subject" },
          { Header: t("inquiries.status"), accessor: "status" },
          {
            Header: t("inquiries.createdAt"),
            accessor: "created_at",
            Cell: ({ value }) => moment(value).format("MMMM Do YYYY, h:mm:ss a"),
          },
          {
            Header: t("inquiries.action"),
            accessor: "id",
            Cell: ({ row }) => (
              <button
                onClick={() => handleViewClick(row.original)}
                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-2 rounded-lg"
              >
                <Eye />
              </button>
            ),
          },
        ]}
        data={inquiriesData?.data?.data || []}
        fetchData={refetch}
        loading={isLoading}
        totalPages={inquiriesData?.data?.total_pages || 1}
        currentPage={inquiriesData?.data?.current_page || 1}
        pageSize={state.pageSize}
        onPageChange={(page) => setState((prev) => ({ ...prev, page }))} 
        onPageSizeChange={(pageSize) => setState((prev) => ({ ...prev, pageSize }))}
        onSearch={(search) => setState((prev) => ({ ...prev, search }))}
      />

      {isModalOpen && (
        <Modal
          activeModal={isModalOpen}
          onClose={handleCloseModal}
          title={t("inquiries.modalTitle")}
        >
          {selectedInquiry && <InquiryDetails inquiry={selectedInquiry} />}
        </Modal>
      )}
    </>
  );
};

export default Inquiries;
