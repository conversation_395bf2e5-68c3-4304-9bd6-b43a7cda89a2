import React from 'react';
import { PlusCircle, MinusCircle } from 'lucide-react';

const MCQProperties = ({ element, handleUpdate }) => {
  const { content } = element;

  const handleQuestionChange = (value) => {
    handleUpdate({
      content: {
        ...content,
        question: value
      }
    });
  };

  const handleOptionChange = (index, value) => {
    const newOptions = [...content.options];
    newOptions[index] = value;
    handleUpdate({
      content: {
        ...content,
        options: newOptions
      }
    });
  };

  const addOption = () => {
    handleUpdate({
      content: {
        ...content,
        options: [...content.options, `Option ${content.options.length + 1}`]
      }
    });
  };

  const removeOption = (index) => {
    const newOptions = content.options.filter((_, i) => i !== index);
    handleUpdate({
      content: {
        ...content,
        options: newOptions,
        correctAnswer: content.correctAnswer >= index ? content.correctAnswer - 1 : content.correctAnswer
      }
    });
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-1">Question</label>
        <input
          type="text"
          value={content.question}
          onChange={(e) => handleQuestionChange(e.target.value)}
          className="w-full p-2 border rounded-md"
        />
      </div>
      
      <div>
        <div className="flex justify-between items-center mb-2">
          <label className="block text-sm font-medium">Options</label>
          <button
            onClick={addOption}
            className="p-1 text-blue-500 hover:text-blue-600"
          >
            <PlusCircle size={20} />
          </button>
        </div>
        
        {content.options.map((option, index) => (
          <div key={index} className="flex items-center space-x-2 mb-2">
            <input
              type="text"
              value={option}
              onChange={(e) => handleOptionChange(index, e.target.value)}
              className="flex-1 p-2 border rounded-md"
            />
            <button
              onClick={() => removeOption(index)}
              className="p-1 text-red-500 hover:text-red-600"
              disabled={content.options.length <= 2}
            >
              <MinusCircle size={20} />
            </button>
          </div>
        ))}
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Correct Answer</label>
        <select
          value={content.correctAnswer}
          onChange={(e) => handleUpdate({
            content: { ...content, correctAnswer: parseInt(e.target.value) }
          })}
          className="w-full p-2 border rounded-md"
        >
          {content.options.map((_, index) => (
            <option key={index} value={index}>
              Option {index + 1}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default MCQProperties;