import { useField } from 'formik';

export const TextField = ({ label, ...props }) => {
  const [field, meta] = useField(props);

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
        {label}
        {props.required && <span className="text-red-600">*</span>}
      </label>
      <input
        {...field}
        {...props}
        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md 
                 focus:outline-none focus:ring-2 focus:ring-blue-500 
                 bg-white dark:bg-gray-700 
                 text-gray-900 dark:text-gray-100"
      />
      {meta.touched && meta.error ? (
        <div className="text-red-600 dark:text-red-400 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};