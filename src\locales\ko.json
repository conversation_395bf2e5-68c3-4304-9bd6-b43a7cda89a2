{"trusted_by": "1000+명의 교육자가 신뢰합니다", "future_of": "미래의", "digital_education": "디지털 교육", "create_stunning_ebooks": "학생들이 정보를 배우고, 참여하고, 유지하는 방식을 변화시키는 멋진 대화형 전자책을 만드세요.", "try_ebook_builder": "eBook 빌더 사용해보기", "read_books": "책 읽기", "books_created": "10,000+ 권의 책 제작", "coppa_compliant": "COPPA 준수", "interactive_learning_platform": "대화형 학습 플랫폼", "interactive_content": "대화형 콘텐츠", "interactive_content_desc": "멀티미디어 요소와 상호 작용", "smart_assessment": "스마트 평가", "smart_assessment_desc": "학습 진행 상황 추적", "features": "기능", "how_it_works": "작동 방식", "books": "책", "contact": "연락처", "bookSection.exploreLibrary": "디지털 라이브러리 탐색", "bookSection.browseDescription": "대화형 전자책 컬렉션을 탐색하고 새로운 지식을 발견하세요", "bookSection.error": "오류가 발생했습니다. 나중에 다시 시도하십시오.", "bookSection.showMore": "더 보기", "home.transformContent": "콘텐츠 변환", "home.engagingPublications": "매력적인 디지털 출판물을 만드는 데 필요한 모든 것", "home.howItWorksTitle": "작동 방식", "home.howItWorksDescription": "4단계로 대화형 전자책을 만들고 게시하세요", "home.getInTouch": "연락하기", "home.readyToStart": "생성을 시작할 준비가 되셨나요?", "home.joinPublishers": "대화형 콘텐츠를 제작하는 수천 명의 출판사에 참여하세요", "home.startPublishing": "게시 시작", "home.features.interactiveEbooks.title": "대화형 전자책", "home.features.interactiveEbooks.description": "대화형 요소와 멀티미디어 콘텐츠로 매력적인 디지털 책을 만드세요", "home.features.mcqAssessments.title": "MCQ 및 평가", "home.features.mcqAssessments.description": "학습 결과를 향상시키기 위해 포괄적인 퀴즈 및 평가를 구축하세요", "home.features.flashcards.title": "플래시 카드", "home.features.flashcards.description": "효과적인 암기 및 복습을 위해 동적 플래시 카드를 디자인하세요", "home.features.accessibility.title": "접근성", "home.features.accessibility.description": "내장된 접근성 기능을 통해 모든 사람이 콘텐츠에 도달할 수 있도록 하세요", "home.features.richMedia.title": "리치 미디어", "home.features.richMedia.description": "몰입형 학습 경험을 만들기 위해 비디오 및 대화형 미디어를 포함하세요", "home.features.publisherTools.title": "출판사 도구", "home.features.publisherTools.description": "콘텐츠 배포 및 관리를 위한 전문적인 출판 도구", "home.howItWorks.step1.title": "콘텐츠 만들기", "home.howItWorks.step1.description": "기존 콘텐츠로 시작하거나 직관적인 편집기를 사용하여 새로운 자료를 만드세요", "home.howItWorks.step2.title": "상호 작용 추가", "home.howItWorks.step2.description": "퀴즈, 플래시 카드 및 멀티미디어 요소로 콘텐츠를 향상시키세요", "home.howItWorks.step3.title": "디자인 사용자 정의", "home.howItWorks.step3.description": "아름다운 템플릿 중에서 선택하거나 자신만의 사용자 정의 디자인을 만드세요", "home.howItWorks.step4.title": "게시 및 공유", "home.howItWorks.step4.description": "대화형 전자책을 게시하고 청중과 공유하세요", "home.sendMessage": "메시지 보내기", "footer.product": "제품", "footer.features": "기능", "footer.howItWorks": "작동 방식", "footer.pricing": "가격", "footer.faq": "FAQ", "footer.company": "회사", "footer.about": "정보", "footer.blog": "블로그", "footer.careers": "채용 정보", "footer.contact": "연락처", "footer.resources": "자원", "footer.documentation": "문서", "footer.templates": "템플릿", "footer.examples": "예시", "footer.support": "지원", "footer.legal": "법률", "footer.privacy": "개인 정보", "footer.terms": "약관", "footer.security": "보안", "footer.allRightsReserved": "모든 권리 보유.", "authorForm.name": "이름", "authorForm.nameRequired": "이름은 필수입니다", "authorForm.status": "상태", "authorForm.statusRequired": "상태는 필수입니다", "authorForm.active": "활성", "authorForm.inactive": "비활성", "authorForm.uploadPhoto": "사진 업로드", "authorForm.unsupportedFileFormatOrSize": "지원되지 않는 파일 형식 또는 크기", "authorForm.photoRequired": "사진이 필요합니다", "authorForm.unsupportedFileFormat": "지원되지 않는 파일 형식", "authorForm.fileSizeTooLarge": "파일 크기가 너무 큽니다", "authorForm.bio": "바이오", "authorForm.bioRequired": "바이오가 필요합니다", "authorForm.updateAuthor": "작가 업데이트", "authorForm.addAuthor": "작가 추가", "authors.authorsList": "작가 목록", "authors.name": "이름", "authors.photo": "사진", "authors.status": "상태", "authors.active": "활성", "authors.inactive": "비활성", "authors.action": "작업", "authors.addAuthor": "작가 추가", "authors.editAuthor": "작가 수정", "inquiries.title": "문의", "inquiries.name": "이름", "inquiries.email": "이메일", "inquiries.subject": "제목", "inquiries.status": "상태", "inquiries.createdAt": "생성 날짜", "inquiries.action": "작업", "inquiries.modalTitle": "문의 상세 정보", "genreForm.name": "이름", "genreForm.status": "상태", "genreForm.active": "활성", "genreForm.inactive": "비활성", "genreForm.uploadThumbnail": "썸네일 업로드", "genreForm.addGenre": "카테고리 추가", "genreForm.updateGenre": "장르 업데이트", "genreForm.validation.nameRequired": "이름이 필요합니다", "genreForm.validation.statusRequired": "상태가 필요합니다", "genreForm.validation.thumbnailRequired": "썸네일이 필요합니다", "genreForm.validation.unsupportedFormat": "지원되지 않는 파일 형식", "genreForm.validation.fileTooLarge": "파일 크기가 너무 큽니다", "genreForm.validation.fileInvalid": "잘못된 파일 형식 또는 크기", "genresPage.title": "장르 목록", "genresPage.name": "이름", "genresPage.image": "이미지", "genresPage.status": "상태", "genresPage.active": "활성", "genresPage.inactive": "비활성", "genresPage.action": "작업", "genresPage.addGenre": "장르 추가", "genresPage.editGenre": "장르 편집", "genresPage.submitError": "양식 제출 오류", "genresPage.deleteError": "장르 삭제 오류", "userForm.name": "이름", "userForm.email": "이메일", "userForm.password": "비밀번호", "userForm.role": "역할", "userForm.isActive": "활성화됨?", "userForm.profilePicture": "프로필 사진", "userForm.active": "활성", "userForm.inactive": "비활성", "userForm.admin": "관리자", "userForm.author": "저자", "userForm.user": "사용자", "userForm.createUser": "사용자 생성", "userForm.updateUser": "사용자 업데이트", "userForm.validation.nameRequired": "이름이 필요합니다", "userForm.validation.emailRequired": "이메일이 필요합니다", "userForm.validation.invalidEmail": "잘못된 이메일 주소", "userForm.validation.passwordRequired": "비밀번호가 필요합니다", "userForm.validation.passwordMin": "비밀번호는 최소 6자 이상이어야 합니다", "userForm.validation.roleRequired": "역할이 필요합니다", "userForm.submitError": "양식 제출 오류", "usersList.title": "사용자 목록", "usersList.name": "이름", "usersList.email": "이메일", "usersList.role": "역할", "usersList.status": "상태", "usersList.active": "활성", "usersList.inactive": "비활성", "usersList.action": "작업", "usersList.addUser": "사용자 추가", "usersList.editUser": "사용자 편집", "usersList.deleteError": "사용자 삭제 오류", "userView.loading": "로딩 중...", "userView.user": "사용자", "userView.backToList": "사용자 목록으로 돌아가기", "userView.name": "이름", "userView.email": "이메일", "userView.role": "역할", "userView.profilePicture": "프로필 사진", "userView.status": "상태", "userView.active": "활성", "userView.inactive": "비활성", "accessDenied.title": "접근이 거부되었습니다", "accessDenied.message": "이 페이지에 접근할 권한이 없습니다.", "accessDenied.goHome": "홈페이지로 이동", "profile.title": "프로필 설정", "profile.profilePicture": "프로필 사진", "profile.uploadProfilePicture": "프로필 사진 업로드", "profile.fullName": "전체 이름", "profile.currentPassword": "현재 비밀번호", "profile.newPassword": "새 비밀번호", "profile.confirmNewPassword": "새 비밀번호 확인", "profile.role": "역할", "profile.saving": "저장 중...", "profile.saveChanges": "변경 사항 저장", "profile.updateSuccess": "프로필이 성공적으로 업데이트되었습니다!", "profile.updateFail": "프로필 업데이트에 실패했습니다. 다시 시도해주세요.", "profile.updateError": "프로필 업데이트 중 오류 발생", "profile.validation.nameRequired": "전체 이름이 필요합니다", "profile.validation.passwordMin": "비밀번호는 최소 6자 이상이어야 합니다", "profile.validation.newPasswordRequired": "새 비밀번호가 필요합니다", "profile.validation.confirmPasswordRequired": "비밀번호 확인이 필요합니다", "profile.validation.passwordMismatch": "비밀번호가 일치하지 않습니다", "security.title": "보안 설정", "security.changePassword": "비밀번호 변경", "security.currentPassword": "현재 비밀번호", "security.newPassword": "새 비밀번호", "security.confirmNewPassword": "새 비밀번호 확인", "security.updating": "업데이트 중...", "security.updatePassword": "비밀번호 업데이트", "security.updateSuccess": "비밀번호가 성공적으로 업데이트되었습니다!", "security.updateError": "비밀번호 업데이트 중 오류 발생", "security.validation.required": "필수 항목입니다", "security.validation.passwordMin": "비밀번호는 최소 8자 이상이어야 합니다", "security.validation.passwordMismatch": "비밀번호가 일치하지 않습니다", "dashboard.title": "대시보드 개요", "dashboard.totalUnpublishedBooks": "총 미출판 책", "dashboard.totalPublishedBooks": "총 출판된 책", "dashboard.totalEbooks": "총 전자책", "dashboard.totalGenres": "총 장르", "dashboard.recentEbooks": "최근 전자책", "dashboard.pendingEbooks": "보류 중인 전자책", "dashboard.titleColumn": "제목", "dashboard.authorColumn": "저자", "dashboard.genreColumn": "장르", "dashboard.loading": "로딩 중...", "dashboard.noData": "데이터가 없습니다", "Dashboard": "대시보드", "Genres": "장르", "eBook": "이북", "Templates": "템플릿", "Users": "사용자", "Inquiries": "문의사항", "Authors": "저자", "Settings": "설정", "Profile": "프로필", "Security": "보안", "Logout": "로그아웃", "Coupons": "쿠폰", "home.pricing.title": "간단하고 투명한 가격", "home.pricing.subtitle": "출판 요구사항에 맞는 완벽한 플랜을 선택하세요", "home.pricing.popular": "인기", "home.pricing.basic.name": "베이직", "home.pricing.basic.price": "₩39,000", "home.pricing.basic.description": "인터랙티브 전자책을 시작하기에 완벽한 플랜", "home.pricing.basic.features.ebooks": "최대 5개의 전자책", "home.pricing.basic.features.storage": "5GB 저장공간", "home.pricing.basic.features.support": "이메일 지원", "home.pricing.basic.features.analytics": "기본 분석", "home.pricing.basic.cta": "시작하기", "home.pricing.pro.name": "프로페셔널", "home.pricing.pro.price": "₩119,000", "home.pricing.pro.description": "전문 출판에 필요한 모든 것", "home.pricing.pro.features.unlimited": "무제한 전자책", "home.pricing.pro.features.advanced": "고급 인터랙티브 기능", "home.pricing.pro.features.priority": "우선 지원", "home.pricing.pro.features.customization": "맞춤 브랜딩", "home.pricing.pro.features.collaboration": "팀 협업", "home.pricing.pro.cta": "무료 체험 시작", "home.pricing.enterprise.name": "엔터프라이즈", "home.pricing.enterprise.price": "맞춤형", "home.pricing.enterprise.description": "대규모 조직을 위한 맞춤형 솔루션", "home.pricing.enterprise.features.custom": "맞춤형 솔루션", "home.pricing.enterprise.features.dedicated": "전담 지원", "home.pricing.enterprise.features.sla": "SLA 보장", "home.pricing.enterprise.features.api": "API 액세스", "home.pricing.enterprise.features.training": "팀 교육", "home.pricing.enterprise.cta": "영업팀 문의", "couponsPage.title": "쿠폰 목록", "couponsPage.code": "코드", "couponsPage.discountType": "할인 유형", "couponsPage.appliesTo": "적용 대상", "couponsPage.validPeriod": "유효 기간", "couponsPage.status": "상태", "couponsPage.action": "작업", "couponsPage.active": "활성", "couponsPage.inactive": "비활성", "couponsPage.addCoupon": "쿠폰 추가", "couponsPage.editCoupon": "쿠폰 수정", "couponsPage.item": "항목", "couponsPage.submitError": "쿠폰 제출 오류", "couponsPage.deleteError": "쿠폰 삭제 오류", "couponForm.code": "쿠폰 코드", "couponForm.discountType": "할인 유형", "couponForm.percentage": "퍼센트", "couponForm.fixed": "고정 금액", "couponForm.discountValue": "할인 값", "couponForm.appliesTo": "적용 대상", "couponForm.entireOrder": "전체 주문", "couponForm.specificItem": "특정 항목", "couponForm.itemId": "항목 ID", "couponForm.minOrderAmount": "최소 주문 금액", "couponForm.maxDiscount": "최대 할인", "couponForm.usageLimit": "사용 제한", "couponForm.startDate": "시작일", "couponForm.endDate": "종료일", "couponForm.status": "상태", "couponForm.active": "활성", "couponForm.inactive": "비활성", "couponForm.addCoupon": "쿠폰 추가", "couponForm.updateCoupon": "쿠폰 업데이트", "couponForm.validation.codeRequired": "쿠폰 코드가 필요합니다", "couponForm.validation.invalidDiscountType": "잘못된 할인 유형", "couponForm.validation.discountTypeRequired": "할인 유형이 필요합니다", "couponForm.validation.discountValueRequired": "할인 값이 필요합니다", "couponForm.validation.minDiscount": "할인은 0 이상이어야 합니다", "couponForm.validation.appliesToRequired": "적용 대상이 필요합니다", "couponForm.validation.invalidAppliesTo": "잘못된 적용 대상 값", "couponForm.validation.itemIdRequired": "항목별 쿠폰에는 항목 ID가 필요합니다", "couponForm.validation.minOrderAmount": "최소 주문 금액은 0 이상이어야 합니다", "couponForm.validation.minOrderAmountRequired": "최소 주문 금액이 필요합니다", "couponForm.validation.minMaxDiscount": "최대 할인은 0 이상이어야 합니다", "couponForm.validation.maxDiscountRequired": "최대 할인이 필요합니다", "couponForm.validation.minUsageLimit": "사용 제한은 0 이상이어야 합니다", "couponForm.validation.usageLimitRequired": "사용 제한이 필요합니다", "couponForm.validation.startDateRequired": "시작일이 필요합니다", "couponForm.validation.endDateRequired": "종료일이 필요합니다", "couponForm.validation.endDateAfterStart": "종료일은 시작일 이후여야 합니다", "couponForm.validation.invalidStatus": "잘못된 상태", "couponForm.validation.statusRequired": "상태가 필요합니다", "publishers.name": "이름", "publishers.mobile": "휴대전화", "publishers.email": "이메일", "publishers.tradeLicense": "사업자 등록 번호", "publishers.status": "상태", "publishers.active": "활성", "publishers.inactive": "비활성", "publishers.action": "작업", "publishers.editPublisher": "출판사 수정", "publishers.addPublisher": "출판사 추가", "publishers.deleteError": "출판사 삭제 실패.", "publishers.submitError": "양식 제출 실패.", "publishers.title": "출판사 목록", "publishers.form.name": "이름", "publishers.form.mobile": "휴대폰", "publishers.form.nid": "주민등록번호", "publishers.form.nidImage": "주민등록증 이미지", "publishers.form.proprietorPhoto": "소유자 사진", "publishers.form.proprietorSignature": "소유자 서명", "publishers.form.presentAddress": "현재 주소", "publishers.form.permanentAddress": "영구 주소", "publishers.form.officePresentAddress": "사무실 현재 주소", "publishers.form.tradeLicenseNumber": "사업자 등록번호", "publishers.form.tradeLicenseImage": "사업자 등록증 이미지", "publishers.form.logo": "로고", "publishers.form.etin": "전자세금식별번호", "publishers.form.vatNumber": "부가가치세 번호", "publishers.form.officeMobile": "사무실 휴대폰", "publishers.form.officePhone": "사무실 전화", "publishers.form.officeEmail": "사무실 이메일", "publishers.form.officePermanentAddress": "사무실 영구 주소", "publishers.form.cancel": "취소", "publishers.form.add": "추가", "publishers.form.edit": "수정", "publishers.form.email": "이메일", "publishers.form.password": "비밀번호", "publishers.form.confirmPassword": "비밀번호 확인", "publishers.form.publisherLogin": "출판사 로그인", "publishers.form.passwordMin": "비밀번호는 최소 8자 이상이어야 합니다", "publishers.form.passwordRequired": "비밀번호가 필요합니다", "publishers.form.confirmPasswordRequired": "비밀번호 확인이 필요합니다", "publishers.form.passwordMismatch": "비밀번호가 일치하지 않습니다", "category.list": "카테고리 목록", "static.page": "정적 페이지 목록", "static.addStaticPage": "정적 페이지 추가", "title": "제목", "staticForm.content": "콘텐츠", "staticForm.slug": "Slug", "static.editStatic": "정적 페이지 업데이트", "update": "업데이트", "pending": "대기 중", "approved": "승인됨", "rejected": "거부됨", "publishers.form.emailRequired": "이메일 입력은 필수입니다", "publishers.form.nameRequired": "이름이 필요합니다", "publishers.form.invalidEmail": "잘못된 이메일 형식", "Manage eBooks": "eBook 관리", "Publishers": "출판사", "Categories": "카테고리", "Orders": "주문", "Search eBooks": "eBook 검색", "Author": "저자", "Publisher": "출판사", "Book Type": "책 유형", "Status": "상태", "Approval Status": "승인 상태", "ebook.modal.editTitle": "책 기본 정보 수정", "ebook.modal.createTitle": "새 책 만들기", "ebook.form.fields.title": "제목", "ebook.form.fields.authors": "저자 선택", "ebook.form.fields.description": "설명", "ebook.form.fields.categories": "카테고리 선택", "ebook.form.fields.status": "상태", "ebook.form.fields.status.options.draft": "임시저장", "ebook.form.fields.status.options.published": "발행됨", "ebook.form.fields.bookType": "도서 유형", "ebook.form.fields.bookType.options.ebook": "전자책", "ebook.form.fields.bookType.options.pdf": "PDF", "ebook.form.fields.pageDimensions": "페이지 크기", "ebook.form.fields.orientation": "방향", "ebook.form.fields.orientation.options.portrait": "세로", "ebook.form.fields.orientation.options.landscape": "가로", "ebook.form.fields.pdfFile": "PDF 파일", "ebook.form.fields.coverImage": "표지 이미지", "ebook.form.fields.buttons.update": "전자책 수정", "ebook.form.fields.buttons.submit": "전자책 등록", "ebook.form.fields.price": "가격", "ebook.form.fields.discount": "할인", "ebook.form.fields.finalPrice": "최종 가격", "ebook.form.fields.pageDimension": "페이지 크기", "ebook.form.fields.pageDimension.custom": "사용자 지정", "ebook.form.fields.pageDimension.a4": "A4", "ebook.form.fields.pageDimension.a5": "A5", "ebook.form.fields.width": "너비", "ebook.form.fields.height": "높이", "fileInput.dragActive": "여기에 파일을 놓으세요...", "fileInput.dragInactive": "파일을 끌어다 놓거나 클릭하여 선택하세요", "ebook.button.create": "책 만들기", "ebook.title": "전자책", "ebook.modal.title.view": "책 기본 정보", "ebook.modal.title.edit": "책 기본 정보 수정", "ebook.modal.title.create": "새 책 만들기", "ebook.button.tooltip.view": "책 보기", "ebook.button.tooltip.viewInfo": "기본 정보 보기", "ebook.button.tooltip.editInfo": "기본 정보 수정", "ebook.button.tooltip.design": "에디터로 디자인", "ebook.button.tooltip.template": "템플릿으로 저장", "ebook.button.tooltip.delete": "책 삭제", "ebook.delete.confirm.title": "이 전자책을 삭제하시겠습니까?", "ebook.delete.confirm.text": "이 전자책은", "ebook.delete.confirm.description": "영구적으로 삭제됩니다.", "ebook.delete.confirm.cancel": "취소", "ebook.delete.confirm.confirm": "삭제"}