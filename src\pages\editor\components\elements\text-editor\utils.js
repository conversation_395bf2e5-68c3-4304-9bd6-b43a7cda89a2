export const getSelectedTextFontSize = editor => {
    const selection = editor.selection.getNode()
    const style = window.getComputedStyle(selection)
    return parseInt(style.fontSize) || 16
  }
  
  export const applyParagraphSpacing = (editor, action, customValue = null) => {
    const selectedNode = editor.selection.getNode()
    const paragraphs = editor.dom.select("p", selectedNode.parentNode)
    paragraphs.forEach(paragraph => {
      switch (action) {
        case "addSpaceBefore":
          const currentMarginTop =
            parseInt(editor.dom.getStyle(paragraph, "margin-top")) || 0
          editor.dom.setStyle(
            paragraph,
            "margin-top",
            (currentMarginTop + 10) + "px"
          )
          break
        case "removeSpacing":
          editor.dom.setStyle(paragraph, "margin-top", "0")
          editor.dom.setStyle(paragraph, "margin-bottom", "0")
          if (!paragraph.getAttribute("style")) {
            paragraph.removeAttribute("style")
          }
          break
        case "customSpacing":
          if (customValue !== null) {
            editor.dom.setStyle(paragraph, "margin-top", customValue + "px")
          }
          break
      }
    })
    editor.fire("Change")
    editor.focus()
  }
  
  export const handleVerticalText = {
    wrap: editor => {
      const body = editor.getBody()
      if (body.firstElementChild?.classList.contains("vertical-wrapper")) return
      const wrapper = editor.dom.create("div", {
        class: "vertical-wrapper",
        style:
          "writing-mode: vertical-rl; text-orientation: upright; float: right; white-space: pre-wrap; margin: 0; padding: 0; line-height: 1.2;"
      })
      while (body.firstChild) {
        wrapper.appendChild(body.firstChild)
      }
      body.appendChild(wrapper)
    },
    unwrap: editor => {
      const body = editor.getBody()
      const wrapper = body.firstElementChild
      if (wrapper?.classList.contains("vertical-wrapper")) {
        while (wrapper.firstChild) {
          body.insertBefore(wrapper.firstChild, wrapper)
        }
        editor.dom.remove(wrapper)
      }
    }
  }
  