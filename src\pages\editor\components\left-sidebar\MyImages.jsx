import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Icon } from "@iconify/react";
import useDataFetching from '@/hooks/useDataFetching';
import { useQueryClient } from '@tanstack/react-query';
import Confirm from '@/components/ui/Confirm';
import api from '@/lib/axios';
import { useDispatch,useSelector } from 'react-redux';
import { addElement } from '../../store/pagesSlice';

const MyImages = () => {
  const [selected, setSelected] = useState('image');
  const [page, setPage] = useState(1);
  const [uploadProgress, setUploadProgress] = useState({});
  const per_page = 20;
  const queryClient = useQueryClient();
  const dispatch = useDispatch();

  const handleSelect = (type) => setSelected(type);
  const width = useSelector((state) => state.ebook.width) - 60;

  // Handle Multiple Image Upload
  const onDrop = useCallback((acceptedFiles) => {
    // Create an array of promises for each file upload
    const uploadPromises = acceptedFiles.map(file => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', selected);

      // Initialize progress for this file
      setUploadProgress(prev => ({
        ...prev,
        [file.name]: 0
      }));

      return api.post('/admin/attachments', formData, {
        onUploadProgress: (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(prev => ({
            ...prev,
            [file.name]: progress
          }));
        }
      });
    });

    // Execute all uploads in parallel
    Promise.all(uploadPromises)
      .then(() => {
        queryClient.invalidateQueries(['attachments', page]);
        setUploadProgress({}); // Clear progress
      })
      .catch(error => {
        console.error('Error uploading files:', error);
        setUploadProgress({}); // Clear progress on error
      });
  }, [selected, page, queryClient]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({ 
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg']
    },
    multiple: true // Enable multiple file selection
  });

  // Handle Image Deletion
  const handleDelete = (id) => {
    Confirm(() => {
      api.delete(`/admin/attachments/${id}`)
        .then(() => queryClient.invalidateQueries(['attachments', page]))
        .catch(error => console.error('Error deleting file:', error));
    });
  };

  // Handle Image Insertion
  const handleItemClick = (imageUrl) => {
    const img = new Image();
    img.src = imageUrl;
    
    img.onload = () => {
      const originalWidth = img.naturalWidth;
      const originalHeight = img.naturalHeight;
      let finalWidth = originalWidth;
      let finalHeight = originalHeight;

      // If image is wider than page width, scale it down proportionally
      if (originalWidth > width) {
        const scale = width / originalWidth;
        finalWidth = width;
        finalHeight = originalHeight * scale;
      }

      dispatch(
        addElement({
          type: 'image',
          content: imageUrl,
          position: { x: 50, y: 50 },
          size: { width: finalWidth, height: finalHeight },
        })
      );
    };
  };

  // Handle Drag Start
  const handleDragStart = (e, imageUrl) => {
    e.dataTransfer.setData('type', 'image');
    e.dataTransfer.setData('content', imageUrl);
  };

  // Fetch Data with Pagination
  const { data, isLoading, isError } = useDataFetching({
    queryKey: ['attachments', page],
    endPoint: `/admin/attachments?search=${selected}&per_page=${per_page}&page=${page}`,
  });

  const totalPages = data?.data?.total_pages || 1;

  return (
    <>
      {/* Upload Area */}
      <div 
        {...getRootProps()} 
        className={`w-full border-2 border-dashed p-4 text-center rounded-lg cursor-pointer ${
          isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
        }`}
      >
        <input {...getInputProps()} />
        <div className="space-y-2">
          <Icon 
            icon="material-symbols:upload" 
            className="text-3xl text-gray-400 mx-auto"
          />
          <p className="text-sm text-gray-500">
            {isDragActive
              ? "Drop the images here..."
              : " click to select"}
          </p>
          <p className="text-xs text-gray-400">
            Supports: PNG, JPG, JPEG
          </p>
        </div>
      </div>

      {/* Upload Progress */}
      {Object.keys(uploadProgress).length > 0 && (
        <div className="mt-4 space-y-2">
          {Object.entries(uploadProgress).map(([fileName, progress]) => (
            <div key={fileName} className="text-sm">
              <div className="flex justify-between mb-1">
                <span className="truncate">{fileName}</span>
                <span>{progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Loading/Error States */}
      {isLoading ? (
        <div className="text-center mt-4">Loading images...</div>
      ) : isError ? (
        <div className="text-center mt-4 text-red-500">Error loading images.</div>
      ) : (
        <>
          {/* Display Images */}
          {data?.data?.data.length === 0 ? (
            <div className="text-center mt-4">No images found.</div>
          ) : (
            <>
              <h3 className="text-gray-400 text-sm uppercase mt-4">Uploaded Images</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-2 mt-4">
                {data?.data?.data.map((item) => (
                  <div
                    key={item.id}
                    className="border border-white relative flex flex-col items-center cursor-pointer group"
                    onClick={() => handleItemClick(`${import.meta.env.VITE_HOST_URL}/storage/${item.path}`)}
                    draggable
                    onDragStart={(e) => handleDragStart(e, `${import.meta.env.VITE_HOST_URL}/storage/${item.path}`)}
                  >
                    <img
                      src={`${import.meta.env.VITE_HOST_URL}/storage/${item.path}`}
                      alt={item.name}
                      className="w-full object-cover"
                    />
                    <button
                      className="absolute top-1 right-1 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(item.id);
                      }}
                    >
                      <Icon icon="bi:trash" style={{ fontSize: '10px' }} />
                    </button>
                  </div>
                ))}
              </div>

              {/* Pagination Controls */}
              {totalPages > 1 && (
                <div className="flex justify-center items-center mt-4 space-x-4">
                  <button
                    onClick={() => setPage((prev) => Math.max(prev - 1, 1))}
                    disabled={page === 1}
                    className={`px-3 py-1 rounded ${page === 1 ? 'bg-gray-300 cursor-not-allowed' : 'bg-blue-500 text-white hover:bg-blue-600'}`}
                  >
                    Previous
                  </button>
                  <span className="text-gray-700">
                    Page {page} of {totalPages}
                  </span>
                  <button
                    onClick={() => setPage((prev) => Math.min(prev + 1, totalPages))}
                    disabled={page === totalPages}
                    className={`px-3 py-1 rounded ${page === totalPages ? 'bg-gray-300 cursor-not-allowed' : 'bg-blue-500 text-white hover:bg-blue-600'}`}
                  >
                    Next
                  </button>
                </div>
              )}
            </>
          )}
        </>
      )}
    </>
  );
};

export default MyImages;
