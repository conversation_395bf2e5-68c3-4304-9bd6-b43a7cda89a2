export const isValidYoutubeUrl = (url) => {
  const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+/;
  return youtubeRegex.test(url);
};

export const getYoutubeEmbedUrl = (url) => {
  let videoId = '';
  
  if (url.includes('youtube.com')) {
    videoId = url.split('v=')[1];
  } else if (url.includes('youtu.be')) {
    videoId = url.split('youtu.be/')[1];
  }

  if (videoId) {
    const ampersandPosition = videoId.indexOf('&');
    if (ampersandPosition !== -1) {
      videoId = videoId.substring(0, ampersandPosition);
    }
    return `https://www.youtube.com/embed/${videoId}`;
  }
  
  return url;
};

export const isValidVideoUrl = (url) => {
  // Check if it's a valid URL
  try {
    new URL(url);
  } catch {
    return false;
  }

  // Check if it's either a YouTube URL or ends with common video extensions
  const videoExtensions = /\.(mp4|webm|ogg)$/i;
  return isValidYoutubeUrl(url) || videoExtensions.test(url);
};