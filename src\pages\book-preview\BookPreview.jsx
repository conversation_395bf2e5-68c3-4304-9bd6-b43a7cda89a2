// src/pages/book-preview/BookPreview.jsx
import React, { useEffect } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import Loading from "@/components/Loading";
import Content from "./sections/Content";
import Footer from "./sections/Footer";
import { useEbook } from "./hooks/useEbook";
import Toolbar from "./components/Toolbar";

function BookPreview() {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const {
    ebook,
    isLoading,
    isLoadingMore,
    loadingProgress,
    initialDataLoaded,
    error
  } = useEbook();



  const handleJumpToBookmark = (pageId) => {
    // Get the pages array based on the data structure (paginated or not)
    const pagesArray = ebook?.pages?.data || ebook?.pages || [];

    const pageIndex = pagesArray.findIndex((page) => page.id === pageId);
    if (pageIndex !== -1 && ebook?.flipBookRef?.current?.pageFlip) {
      ebook?.flipBookRef?.current?.pageFlip()?.flip(pageIndex);
    }
  };
 useEffect(() => {
  try {
    if (typeof window !== 'undefined') {
      const storedTheme = localStorage.getItem('newtheme');
      // Remove console.log to avoid unnecessary logging
      if (storedTheme === 'dark') {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark'); // Ensure light mode if 'light' or null
      }
    }
  } catch (error) {
    // Silently handle localStorage errors that might occur in some mobile WebViews
    document.documentElement.classList.remove('dark'); // Default to light mode
  }
}, []);
  // 1. Move all hooks to the top level

  // 3. Handle error by redirecting
  useEffect(() => {
    if (error) {
      navigate("/404");
    }
  }, [error, navigate]);

  // Show loading spinner until initial data is loaded
  if (isLoading && !initialDataLoaded) {
    return <Loading />;
  }

  // Background loading indicator for additional pages
  const showProgressBar = isLoadingMore && initialDataLoaded;

  // 6. Render final UI
  return (
    <div className="relative max-h-screen max-w-screen flex justify-center h-[100vh] w-[100vw] overflow-hidden dark:bg-[#111827]">
      {/* Background loading progress bar */}
      {showProgressBar && (
        <div className="fixed top-0 left-0 right-0 z-50">
          <div className="h-1 bg-gray-200">
            <div
              className="h-1 bg-blue-600 transition-all duration-300"
              style={{ width: `${loadingProgress}%` }}
            ></div>
          </div>
        </div>
      )}

      <Toolbar />
      <div className="">
        <Content />
        <div className=" ">
          <Footer
            bookmarks={ebook?.bookmarks}
            onJumpToBookmark={handleJumpToBookmark}
          />
        </div>
      </div>
    </div>
  );
}

export default BookPreview;
