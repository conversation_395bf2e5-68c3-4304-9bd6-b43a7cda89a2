import React, { useRef, useState, useEffect } from 'react';
import {
  Stage,
  Layer,
  Line,
  Rect,
  Circle,
  RegularPolygon,
  Text,
  Transformer,
} from 'react-konva';
import { useDispatch, useSelector } from 'react-redux';
import { setDrawingData, updateShape, setSelectedId, deleteShape, setTool } from '../store/drawingSlice';
import { v4 as uuidv4 } from 'uuid';
import { Group } from 'lucide-react';
import Modal from '@/components/ui/Modal';

const DrawingLayer = ({ ebookId, pageId, width, height }) => {
  const [deleteButtonPos, setDeleteButtonPos] = useState(null);
  const [showTextModal, setShowTextModal] = useState(false);
  const [tempText, setTempText] = useState('');
  const [textPosition, setTextPosition] = useState({ x: 0, y: 0 });
  const dispatch = useDispatch();
  console.log(`Rendering DrawingLayer for eBook ID: ${ebookId}, Page ID: ${pageId}`);
  const brushColor = useSelector((state) => state.drawing.brushColor);
  const brushSize = useSelector((state) => state.drawing.brushSize);
  const currentTool = useSelector((state) => state.drawing.tool);
  const drawings = useSelector((state) => state.drawing.drawings[ebookId]?.[pageId] || []);
  const selectedId = useSelector((state) => state.drawing.selectedId);
  const [currentShape, setCurrentShape] = useState(null);
  const isDrawing = useRef(false);
  const stageRef = useRef();
  const trRef = useRef();

  useEffect(() => {
    if (trRef.current && selectedId) {
      const selectedNode = stageRef.current.findOne(`#${selectedId}`);
      if (selectedNode) {
        trRef.current.nodes([selectedNode]);
        trRef.current.getLayer().batchDraw();
        const box = selectedNode.getClientRect();
        setDeleteButtonPos({ x: box.x + box.width + 10, y: box.y - 20 });
      }
    } else if (trRef.current) {
      trRef.current.nodes([]);
      trRef.current.getLayer().batchDraw();
    }
  }, [selectedId]);

  const handleMouseDown = (e) => {
    if (e.target === e.target.getStage()) {
      dispatch(setSelectedId(null));
      setDeleteButtonPos(null);
    }

    const stage = e.target.getStage();
    const pointer = stage.getPointerPosition();
    isDrawing.current = true;

    if (currentTool === 'brush') {
      const id = uuidv4();
      const newLine = {
        id,
        tool: 'brush',
        points: [pointer.x, pointer.y],
        stroke: brushColor,
        strokeWidth: brushSize,
        lineCap: 'round',
        lineJoin: 'round',
      };
      setCurrentShape(newLine);
    } else if (currentTool === 'rectangle') {
      const id = uuidv4();
      setCurrentShape({
        id,
        tool: 'rectangle',
        x: pointer.x,
        y: pointer.y,
        width: 0,
        height: 0,
        stroke: brushColor,
        strokeWidth: brushSize,
      });
    } else if (currentTool === 'circle') {
      const id = uuidv4();
      setCurrentShape({
        id,
        tool: 'circle',
        x: pointer.x,
        y: pointer.y,
        radius: 0,
        stroke: brushColor,
        strokeWidth: brushSize,
      });
    } else if (currentTool === 'triangle') {
      const id = uuidv4();
      setCurrentShape({
        id,
        tool: 'triangle',
        x: pointer.x,
        y: pointer.y,
        radius: 0,
        rotation: 0,
        stroke: brushColor,
        strokeWidth: brushSize,
      });
    } else if (currentTool === 'text') {
      setTextPosition({ x: pointer.x, y: pointer.y });
      setShowTextModal(true);
      isDrawing.current = false;
    }
    else if (currentTool === 'eraser') {
      const id = uuidv4();
      const newLine = {
        id,
        tool: 'eraser',
        points: [pointer.x, pointer.y],
        stroke: '#000000',
        strokeWidth: brushSize,
        lineCap: 'round',
        lineJoin: 'round',
        globalCompositeOperation: 'destination-out',
      };
      setCurrentShape(newLine);
    }

  };

  const handleMouseMove = (e) => {
    if (!isDrawing.current || !currentShape) return;
    const stage = e.target.getStage();
    const pointer = stage.getPointerPosition();

    if (currentTool === 'brush' || currentTool === 'eraser') {
      setCurrentShape({
        ...currentShape,
        points: currentShape.points.concat([pointer.x, pointer.y]),
      });
    } else if (currentTool === 'rectangle') {
      setCurrentShape({
        ...currentShape,
        width: pointer.x - currentShape.x,
        height: pointer.y - currentShape.y,
      });
    } else if (currentTool === 'circle') {
      const radius = Math.sqrt(
        Math.pow(pointer.x - currentShape.x, 2) + Math.pow(pointer.y - currentShape.y, 2)
      );
      setCurrentShape({
        ...currentShape,
        radius,
      });
    } else if (currentTool === 'triangle') {
      const radius = Math.sqrt(
        Math.pow(pointer.x - currentShape.x, 2) + Math.pow(pointer.y - currentShape.y, 2)
      );
      setCurrentShape({
        ...currentShape,
        radius,
      });
    }
  };

  const handleMouseUp = () => {
    if (isDrawing.current && currentShape) {
      dispatch(setDrawingData({ ebookId, pageId, shape: currentShape }));
      setCurrentShape(null);
    }
    isDrawing.current = false;
  };

  const handleTransform = (id, node) => {
    const attrs = node.getAttrs();
    dispatch(updateShape({ ebookId, pageId, id, attrs }));
  };

  const handleDblClick = (e, shape) => {
    if (shape.tool === 'text') {
      const newText = prompt('Edit text:', shape.text);
      if (newText !== null) {
        dispatch(updateShape({ ebookId, pageId, id: shape.id, attrs: { text: newText } }));
      }
    }
  };

  return (
    <>
      <Stage
        width={width}
        height={height}
        className="absolute top-0 left-0"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onTouchStart={handleMouseDown}
        onTouchMove={handleMouseMove}
        onTouchEnd={handleMouseUp}
        style={{
          pointerEvents: 'auto',
          cursor:
            currentTool === 'eraser'
              ? 'pointer'
              : 'default',
        }}
        ref={stageRef}
      >
        <Layer>
          {drawings.map((shape, i) => {
            if (shape.tool === 'brush') {
              return (
                <Line
                  key={shape.id}
                  id={shape.id}
                  points={shape.points}
                  stroke={shape.stroke}
                  strokeWidth={shape.strokeWidth}
                  tension={0.5}
                  lineCap={shape.lineCap}
                  lineJoin={shape.lineJoin}
                  globalCompositeOperation="source-over"
                  draggable
                  onDragEnd={(e) => {
                    dispatch(
                      updateShape({
                        ebookId,
                        pageId,
                        id: shape.id,
                        attrs: { x: e.target.x(), y: e.target.y() },
                      })
                    );
                  }}
                  onClick={() => dispatch(setSelectedId(shape.id))}
                  onTap={() => dispatch(setSelectedId(shape.id))}
                  onDblClick={(e) => handleDblClick(e, shape)}
                  onDblTap={(e) => handleDblClick(e, shape)}
                />
              );
            } else if (shape.tool === 'eraser') {
              return (
                <Line
                  key={shape.id}
                  id={shape.id}
                  points={shape.points}
                  stroke="#000000"
                  strokeWidth={shape.strokeWidth}
                  lineCap={shape.lineCap}
                  lineJoin={shape.lineJoin}
                  globalCompositeOperation="destination-out"
                  draggable
                  onDragEnd={(e) => {
                    dispatch(
                      updateShape({
                        ebookId,
                        pageId,
                        id: shape.id,
                        attrs: { x: e.target.x(), y: e.target.y() },
                      })
                    );
                  }}
                  onClick={() => dispatch(setSelectedId(shape.id))}
                  onTap={() => dispatch(setSelectedId(shape.id))}
                  onDblClick={(e) => handleDblClick(e, shape)}
                  onDblTap={(e) => handleDblClick(e, shape)}
                />
              );
            }
            else if (shape.tool === 'rectangle') {
              return (
                <Rect
                  key={shape.id}
                  id={shape.id}
                  x={shape.x}
                  y={shape.y}
                  width={shape.width}
                  height={shape.height}
                  stroke={shape.stroke}
                  strokeWidth={shape.strokeWidth}
                  draggable
                  onDragEnd={(e) => {
                    dispatch(
                      updateShape({
                        ebookId,
                        pageId,
                        id: shape.id,
                        attrs: { x: e.target.x(), y: e.target.y() },
                      })
                    );
                  }}
                  onTransformEnd={(e) => {
                    const node = e.target;
                    handleTransform(shape.id, node);
                  }}
                  onClick={() => dispatch(setSelectedId(shape.id))}
                  onTap={() => dispatch(setSelectedId(shape.id))}
                  onDblClick={(e) => handleDblClick(e, shape)}
                  onDblTap={(e) => handleDblClick(e, shape)}
                />
              );
            } else if (shape.tool === 'circle') {
              return (
                <Circle
                  key={shape.id}
                  id={shape.id}
                  x={shape.x}
                  y={shape.y}
                  radius={shape.radius}
                  stroke={shape.stroke}
                  strokeWidth={shape.strokeWidth}
                  draggable
                  onDragEnd={(e) => {
                    dispatch(
                      updateShape({
                        ebookId,
                        pageId,
                        id: shape.id,
                        attrs: { x: e.target.x(), y: e.target.y() },
                      })
                    );
                  }}
                  onTransformEnd={(e) => {
                    const node = e.target;
                    handleTransform(shape.id, node);
                  }}
                  onClick={() => dispatch(setSelectedId(shape.id))}
                  onTap={() => dispatch(setSelectedId(shape.id))}
                  onDblClick={(e) => handleDblClick(e, shape)}
                  onDblTap={(e) => handleDblClick(e, shape)}
                />
              );
            } else if (shape.tool === 'triangle') {
              return (
                <RegularPolygon
                  key={shape.id}
                  id={shape.id}
                  x={shape.x}
                  y={shape.y}
                  sides={3}
                  radius={shape.radius}
                  stroke={shape.stroke}
                  strokeWidth={shape.strokeWidth}
                  draggable
                  onDragEnd={(e) => {
                    dispatch(
                      updateShape({
                        ebookId,
                        pageId,
                        id: shape.id,
                        attrs: { x: e.target.x(), y: e.target.y() },
                      })
                    );
                  }}
                  onTransformEnd={(e) => {
                    const node = e.target;
                    handleTransform(shape.id, node);
                  }}
                  onClick={() => dispatch(setSelectedId(shape.id))}
                  onTap={() => dispatch(setSelectedId(shape.id))}
                  onDblClick={(e) => handleDblClick(e, shape)}
                  onDblTap={(e) => handleDblClick(e, shape)}
                />
              );
            } else if (shape.tool === 'text') {
              return (
                <Text
                  key={shape.id}
                  id={shape.id}
                  x={shape.x}
                  y={shape.y}
                  text={shape.text}
                  fontSize={shape.fontSize}
                  fill={shape.fill}
                  draggable
                  onDragEnd={(e) => {
                    dispatch(
                      updateShape({
                        ebookId,
                        pageId,
                        id: shape.id,
                        attrs: { x: e.target.x(), y: e.target.y() },
                      })
                    );
                  }}
                  onTransformEnd={(e) => {
                    const node = e.target;
                    dispatch(
                      updateShape({
                        ebookId,
                        pageId,
                        id: shape.id,
                        attrs: { fontSize: node.fontSize() },
                      })
                    );
                  }}
                  onClick={() => dispatch(setSelectedId(shape.id))}
                  onTap={() => dispatch(setSelectedId(shape.id))}
                  onDblClick={(e) => handleDblClick(e, shape)}
                  onDblTap={(e) => handleDblClick(e, shape)}
                />
              );
            }
            return null;
          })}
          {currentShape && (currentShape.tool === 'brush' || currentShape.tool === 'eraser') && (
            <Line
              points={currentShape.points}
              stroke={currentShape.tool === 'eraser' ? '#000000' : currentShape.stroke}
              strokeWidth={currentShape.strokeWidth}
              tension={0.5}
              lineCap={currentShape.lineCap}
              lineJoin={currentShape.lineJoin}
              globalCompositeOperation={
                currentShape.tool === 'eraser'
                  ? 'destination-out'
                  : 'source-over'
              }
            />
          )}
          {currentShape && currentShape.tool === 'rectangle' && (
            <Rect
              x={currentShape.x}
              y={currentShape.y}
              width={currentShape.width}
              height={currentShape.height}
              stroke={currentShape.stroke}
              strokeWidth={currentShape.strokeWidth}
            />
          )}
          {currentShape && currentShape.tool === 'circle' && (
            <Circle
              x={currentShape.x}
              y={currentShape.y}
              radius={currentShape.radius}
              stroke={currentShape.stroke}
              strokeWidth={currentShape.strokeWidth}
            />
          )}
          {currentShape && currentShape.tool === 'triangle' && (
            <RegularPolygon
              x={currentShape.x}
              y={currentShape.y}
              sides={3}
              radius={currentShape.radius}
              stroke={currentShape.stroke}
              strokeWidth={currentShape.strokeWidth}
            />
          )}
          {deleteButtonPos && (
            <Group
              x={deleteButtonPos.x}
              y={deleteButtonPos.y}
              onClick={() => {
                dispatch(deleteShape({ ebookId, pageId, shapeId: selectedId }));
                dispatch(setSelectedId(null));
                setDeleteButtonPos(null);
              }}
              onTap={() => {
                dispatch(deleteShape({ ebookId, pageId, shapeId: selectedId }));
                dispatch(setSelectedId(null));
                setDeleteButtonPos(null);
              }}
              onMouseEnter={(e) => e.target.getStage().container().style.cursor = "pointer"}
              onMouseLeave={(e) => e.target.getStage().container().style.cursor = "default"}
            >
              <Rect
                width={30}
                height={30}
                fill="white"
                stroke="red"
                strokeWidth={2}
                cornerRadius={5}
                shadowBlur={4}
                opacity={0.9}
              />
              <Line
                points={[8, 8, 22, 8]}
                stroke="red"
                strokeWidth={3}
                lineCap="round"
              />
              <Rect
                x={10}
                y={10}
                width={10}
                height={12}
                stroke="red"
                strokeWidth={3}
                cornerRadius={2}
              />
              <Line
                points={[12, 14, 12, 20]}
                stroke="red"
                strokeWidth={2}
                lineCap="round"
              />
              <Line
                points={[18, 14, 18, 20]}
                stroke="red"
                strokeWidth={2}
                lineCap="round"
              />
            </Group>
          )}

          <Transformer ref={trRef} />
        </Layer>
      </Stage>
      <Modal
        activeModal={showTextModal}
        onClose={() => {
          setShowTextModal(false);
          setTempText('');
        }}
        title="Add Text"
        footerContent={
          <>
            <button
              onClick={() => {
                setShowTextModal(false);
                setTempText('');
                dispatch(setTool('brush'));
              }}
              className="mr-2 px-4 py-2 rounded bg-gray-400 text-white hover:bg-gray-500"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                if (tempText.trim().length > 0) {
                  const id = uuidv4();
                  const newText = {
                    id,
                    tool: 'text',
                    x: textPosition.x,
                    y: textPosition.y,
                    text: tempText,
                    fontSize: 20,
                    fill: brushColor,
                  };
                  dispatch(setDrawingData({ ebookId, pageId, shape: newText }));
                }
                setShowTextModal(false);
                setTempText('');
                dispatch(setTool('brush'));
              }}
              className="px-4 py-2 rounded bg-blue-500 text-white hover:bg-blue-600"
            >
              Save
            </button>
          </>
        }
      >
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">Enter Text:</label>
          <textarea
            value={tempText}
            onChange={(e) => setTempText(e.target.value)}
            rows={4}
            className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Type here..."
          />
        </div>
      </Modal>

    </>
  );
};

export default DrawingLayer;
