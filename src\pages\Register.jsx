import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Formik } from "formik";
import * as Yup from "yup";
import FormInput from "../components/FormInput";
import InputSelect from "../components/InputSelect";
import { useAuth } from "../hooks/useAuth";
import { Icon } from "@iconify/react";

const registerSchema = Yup.object().shape({
  name: Yup.string().required("Required"),
  email: Yup.string().email("Invalid email").required("Required"),
  password: Yup.string()
    .min(8, "Password must be at least 8 characters")
    .required("Required"),
  password_confirmation: Yup.string()
    .oneOf([Yup.ref("password"), null], "Passwords must match")
    .required("Required"),
  user_type: Yup.string()
    .oneOf(["user", "author", "publisher"], "Invalid user type")
    .required("Required"),
  publisher_name: Yup.string().when("user_type", {
    is: "publisher",
    then: (schema) => schema.required("Publisher name is required"),
    otherwise: (schema) => schema.notRequired(),
  }),
});

const userTypeOptions = [
  // { value: "user", label: "User" },
  { value: "author", label: "Author" },
  { value: "publisher", label: "Publisher" },
];

const Register = () => {
  const { register } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const navigate = useNavigate();

  const handleRegister = async (values) => {
    setIsLoading(true);
    setError(null);
    try {
      await register(values);
    } catch (err) {
      setError(err.response?.data || "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full space-y-8 p-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <div>
          <h2 className="text-center text-3xl font-bold text-gray-900 dark:text-white">
            Create your account
          </h2>
        </div>

        <Formik
          initialValues={{
            name: "",
            email: "",
            password: "",
            password_confirmation: "",
            user_type: "",
            publisher_name: "",
            terms: false,
          }}
          validationSchema={registerSchema}
          onSubmit={handleRegister}
        >
          {({ handleSubmit, values }) => (
            <form onSubmit={handleSubmit} className="mt-8 space-y-6">
              {error && <div className="text-red-500 text-center">{error}</div>}
              <InputSelect
                label="Register as"
                name="user_type"
                options={userTypeOptions}
                placeholder="Select user type"
              />

              {values.user_type === "publisher" && (
                <FormInput
                  label="Publisher Name"
                  name="publisher_name"
                  type="text"
                  placeholder="Enter publisher name"
                />
              )}

              <FormInput
                label="Name"
                name="name"
                type="text"
                placeholder="Enter your name"
              />

              <FormInput
                label="Email Address"
                name="email"
                type="email"
                placeholder="Enter your email"
              />

              <div className="relative">
                <FormInput
                  label="Password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Enter your password"
                />

                <Icon
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-9 text-gray-600 cursor-pointer hover:text-gray-800 transition-colors"
                  icon={
                    showPassword ? "iconamoon:eye-off-light" : "clarity:eye-line"
                  }
                  width="18"
                  height="18"
                />
              </div>

              <div className="relative">
                <FormInput
                  label="Confirm Password"
                  name="password_confirmation"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Confirm your password"
                />

                <Icon
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-4 top-9 text-gray-600 cursor-pointer hover:text-gray-800 transition-colors"
                  icon={
                    showConfirmPassword ? "iconamoon:eye-off-light" : "clarity:eye-line"
                  }
                  width="18"
                  height="18"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="terms"
                  id="terms"
                  className="mr-2"
                />
                <label
                  htmlFor="terms"
                  className="text-sm text-gray-600 dark:text-gray-300"
                >
                  I agree to the{" "}
                  <a
                    href="/terms"
                    className="text-blue-600 hover:text-blue-700"
                  >
                    terms and conditions
                  </a>
                </label>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {isLoading ? "Registering..." : "Register"}
              </button>
            </form>
          )}
        </Formik>
        <div className="mt-4 text-center">
          <p>
            Already have an account?{" "}
            <a href="/login" className="text-green-600 hover:text-green-700">
              Sign in
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Register;
