import React from 'react';
import { useField } from 'formik';
import ReactSelect from 'react-select';
import useDataFetching from '@/hooks/useDataFetching'; // Adjust the path as needed

export const SelectWithQuery = ({
  label,
  isMulti = false,
  valueKey = 'id',
  labelKey = 'name',
  dataAccessKey = 'data.data',
  endPoint,
  queryKey,
  params = {},
  returnSingleValue = false,
  required = false,
  ...props
}) => {
  const [field, meta, helpers] = useField(props);

  const { data, isLoading } = useDataFetching({
    queryKey,
    endPoint,
    params,
  });

  const resolveData = (data, key) =>
    key.split('.').reduce((acc, part) => acc?.[part], data) || [];

  const options = resolveData(data, dataAccessKey).map((item) => ({
    value: item[valueKey],
    label: item[labelKey],
    original: item, // To bind the full object later
  }));

  const customStyles = {
    control: (base) => ({
      ...base,
      backgroundColor: 'white',
      borderColor: '#D1D5DB',
      boxShadow: 'none',
      '&:hover': {
        borderColor: '#3B82F6',
      },
    }),
    menu: (base) => ({
      ...base,
      backgroundColor: 'white',
      border: '1px solid #D1D5DB',
    }),
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isSelected
        ? '#3B82F6'
        : state.isFocused
          ? '#F3F4F6'
          : 'transparent',
      color: state.isSelected ? 'white' : '#1F2937',
      '&:hover': {
        backgroundColor: state.isSelected ? '#3B82F6' : '#F3F4F6',
      },
    }),
  };

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium mb-1 dark:text-gray-200">
        {label}
        {required && <span className="text-red-600">*</span>}
      </label>
      <ReactSelect
        options={options}
        isLoading={isLoading}
        isMulti={isMulti}
        value={
          isMulti
            ? options.filter((option) =>
              field.value?.includes(option.value)
            )
            : options.find((option) => option.value === field.value)
        }
        onChange={(selected) => {
          if (isMulti) {
            helpers.setValue(selected?.map((item) => item.value) || []);
          } else {
            helpers.setValue(returnSingleValue ? selected?.value || '' : selected?.original || null);
          }
        }}
        styles={customStyles}
        className="react-select-container"
        classNamePrefix="react-select"
        {...props}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-600 text-sm mt-1 dark:text-red-400">
          {meta.error}
        </div>
      ) : null}
    </div>
  );
};
