// ImagePicker.jsx
import React, { useState } from 'react';
import PixabayImages from './PixabayImages';
import MyImages from './MyImages';
import Tabs from '@/components/ui/Tabs';

const ImagePicker = () => {

  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  const tabs = [
    { label: "Suggested Image", content: <PixabayImages /> },
    { label: "Your Image", content: <MyImages /> },
  ];

  return (
    <div>
      <Tabs tabs={tabs} activeTab={activeTab}
        onTabChange={handleTabChange} />
    </div>
  );
};

export default ImagePicker;
