import React from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import InputField from "@/components/ui/InputField";
import InputSelect from "@/components/ui/InputSelect";
import ImageInputField from "@/components/ui/ImageInputField";
import { useTranslation } from "react-i18next";

const GenreForm = ({ initialValues, onSubmit, isEditMode = false }) => {
  const { t } = useTranslation();

  const validationSchema = Yup.object({
    name: Yup.string().required(t("genreForm.validation.nameRequired")),
    is_active: Yup.boolean().required(t("genreForm.validation.statusRequired")),
   
  });

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={onSubmit}
    >
      {({ setFieldValue, isSubmitting }) => (
        <Form>
          <InputField label={t("genreForm.name")} name="name" type="text" required />
          <InputSelect
            label={t("genreForm.status")}
            name="is_active"
            options={[
              { value: '1', label: t("genreForm.active") },
              { value: '0', label: t("genreForm.inactive") },
            ]}
          />
          <ImageInputField
            name="thumbnail"
            label={t("genreForm.uploadThumbnail")}
            setFieldValue={setFieldValue}
          />
          <button
            type="submit"
            disabled={isSubmitting}
            className={`py-2 px-4 rounded flex items-center justify-center ${
              isSubmitting
                ? "bg-blue-400 cursor-not-allowed"
                : "bg-blue-500 hover:bg-blue-600"
            } text-white`}
          >
            {isSubmitting && (
              <svg
                className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            )}
            {isSubmitting
              ? t("Saving...")
              : isEditMode
                ? t("genreForm.updateGenre")
                : t("genreForm.addGenre")
            }
          </button>
        </Form>
      )}
    </Formik>
  );
};

export default GenreForm;
