import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Icon } from "@iconify/react";
import useDataFetching from '@/hooks/useDataFetching';
import { isValidYoutubeUrl, isValidVideoUrl, getYoutubeEmbedUrl } from '../../utils/videoUtils';
import { useQueryClient } from '@tanstack/react-query';
import Confirm from '@/components/ui/Confirm';
import api from '@/lib/axios';
import { useDispatch } from 'react-redux';
import { addElement } from '../../store/pagesSlice';

const MyVideos = ({ onClose }) => {
  const queryClient = useQueryClient();
  const dispatch = useDispatch();

  const [per_page, setPerPage] = useState(10);
  const [page, setPage] = useState(1);
  const [videoLink, setVideoLink] = useState('');
  const [linkError, setLinkError] = useState('');

  const onDrop = useCallback((acceptedFiles) => {
    const file = acceptedFiles[0];
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'video');

    api.post('/admin/attachments', formData)
      .then(response => {
        queryClient.invalidateQueries('myVideos');
      })
      .catch(error => {
        console.error('Error uploading file:', error);
      });
  }, [queryClient]);

  const { getRootProps, getInputProps } = useDropzone({ onDrop });

  const handleDelete = (id) => {
    Confirm(() => {
      api.delete(`/admin/attachments/${id}`)
        .then(response => {
          console.log('File deleted successfully:', response);
        })
        .catch(error => {
          console.error('Error deleting file:', error);
        })
        .finally(() => {
          queryClient.invalidateQueries('myVideos');
        });
    });
  };

  const handleItemClick = (url) => {
    dispatch(addElement({
      type: 'video',
      content: {
        videoUrl: url,
        isYoutube: false
      },
      position: { x: 50, y: 50 },
      size: { width: 400, height: 300 },
    }));
    onClose();
  };

  const handleAddVideoLink = () => {
    if (!videoLink) return;
    
    if (!isValidVideoUrl(videoLink)) {
      setLinkError('Please enter a valid video URL (YouTube or direct video link)');
      return;
    }

    let finalUrl = videoLink;
    if (isValidYoutubeUrl(videoLink)) {
      finalUrl = getYoutubeEmbedUrl(videoLink);
    }

    dispatch(addElement({
      type: 'video',
      content: {
        videoUrl: finalUrl,
        isYoutube: isValidYoutubeUrl(videoLink)
      },
      position: { x: 50, y: 50 },
      size: { width: 400, height: 300 },
    }));
    setVideoLink('');
    setLinkError('');
    onClose();
  };

  const { data, isLoading, isError } = useDataFetching({
    queryKey: 'myVideos',
    endPoint: `/admin/attachments?search=video&per_page=${per_page}&page=${page}`,
  });

  const totalPages = data?.data?.total_pages || 1;

  const handlePrevious = () => {
    if (page > 1) setPage(page - 1);
  };

  const handleNext = () => {
    if (page < totalPages) setPage(page + 1);
  };

  if (isLoading) {
    return <div className="text-center mt-4">Loading attachments...</div>;
  }

  if (isError) {
    return <div className="text-center mt-4 text-red-500">Error retrieving attachments.</div>;
  }

  return (
    <>
      <div className="space-y-4">
        <div {...getRootProps()} className="w-full">
          <input {...getInputProps()} />
          <button className="py-2 w-full bg-gray-100 border rounded hover:bg-gray-200">
            Upload Video
          </button>
        </div>

        <div className="space-y-2">
          <div className="flex gap-2">
            <input 
              type="text"
              value={videoLink}
              onChange={(e) => {
                setVideoLink(e.target.value);
                setLinkError('');
              }}
              placeholder="Enter video URL (YouTube or direct link)"
              className="flex-1 p-2 border rounded"
            />
            <button 
              onClick={handleAddVideoLink}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Add
            </button>
          </div>
          {linkError && (
            <p className="text-red-500 text-sm">{linkError}</p>
          )}
        </div>
      </div>

      {data?.data?.data.length === 0 ? (
        <div className="text-center mt-4">No data found</div>
      ) : (
        <>
          <div className="grid grid-cols-2 gap-2 mt-4">
            {data?.data?.data.map((item) => (
              <div
                key={item.id}
                className="border border-white relative flex flex-col items-center"
              >
                <div className="shadow p-2">
                  <video
                    src={`${import.meta.env.VITE_HOST_URL}/storage/${item.path}`}
                    className="w-full h-auto"
                  />
                  <button
                    className="absolute top-0 right-0 p-1 bg-red-500 text-white rounded-full"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete(item.id);
                    }}
                  >
                    <Icon icon="bi:trash" style={{ fontSize: '10px' }} />
                  </button>
                  <button
                    className="mt-3 p-2 bg-blue-500 text-white rounded text-sm"
                    onClick={() => handleItemClick(`${import.meta.env.VITE_HOST_URL}/storage/${item.path}`)}
                  >
                    Insert to Page
                  </button>
                </div>
              </div>
            ))}
          </div>

          {totalPages > 1 && (
            <div className="flex justify-center items-center mt-4 space-x-4">
              <button
                onClick={handlePrevious}
                disabled={page === 1}
                className={`px-3 py-1 rounded ${
                  page === 1 ? 'bg-gray-300 cursor-not-allowed' : 'bg-blue-500 text-white hover:bg-blue-600'
                }`}
              >
                Previous
              </button>
              <span>
                Page {page} of {totalPages}
              </span>
              <button
                onClick={handleNext}
                disabled={page === totalPages}
                className={`px-3 py-1 rounded ${
                  page === totalPages ? 'bg-gray-300 cursor-not-allowed' : 'bg-blue-500 text-white hover:bg-blue-600'
                }`}
              >
                Next
              </button>
            </div>
          )}
        </>
      )}
    </>
  );
};

export default MyVideos;
