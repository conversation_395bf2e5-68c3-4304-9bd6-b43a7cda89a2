import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Shield } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';

const HeroSection5 = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-blue-50 via-rose-100 to-blue-100 dark:bg-gradient-to-br dark:from-gray-800 dark:via-gray-900 dark:to-gray-700">
      <div className="absolute inset-0 bg-grid-pattern opacity-10 dark:bg-grid-pattern-dark" />
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32">
        <div className="flex flex-col md:flex-row items-center justify-between gap-10 mt-14">
          <div className="flex-1 text-center md:text-left">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 1 }}
              className="inline-flex items-center px-4 py-2 rounded-full bg-white/80 backdrop-blur-sm shadow-sm mb-6 dark:bg-gray-800/80 dark:text-gray-100"
            >
              <Star className="w-4 h-4 text-amber-500 mr-2" />
              <span className="text-sm font-medium text-gray-800 dark:text-gray-100">
                {t('trusted_by')}
              </span>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: -30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 1 }}
              className="text-5xl md:text-6xl font-bold text-gray-900 leading-tight mb-6 dark:text-white"
            >
              {t('future_of')}
              <span className="block bg-gradient-to-r from-rose-600 to-violet-600 text-transparent bg-clip-text pb-2">
                {t('digital_education')}
              </span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5, duration: 1 }}
              className="text-xl text-gray-700 mb-8 max-w-xl dark:text-gray-300"
            >
              {t('create_stunning_ebooks')}
            </motion.p>

            <div className="flex flex-wrap gap-4 justify-center md:justify-start">
              <motion.button
                onClick={() => navigate('/editor/1/1')}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 1 }}
                className="bg-blue-600 text-white px-8 py-3 rounded-full font-semibold hover:opacity-90 transition-opacity dark:bg-blue-500"
              >
                {t('try_ebook_builder')}
              </motion.button>
              <motion.button
                onClick={() => navigate('/ebooks')}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7, duration: 1 }}
                className="bg-white/80 backdrop-blur-sm text-gray-800 px-8 py-3 rounded-full font-semibold hover:bg-white transition-colors dark:bg-gray-800/80 dark:text-gray-100 hover:dark:bg-gray-700"
              >
                {t('read_books')}
              </motion.button>
            </div>

            <div className="mt-8 flex items-center gap-8 justify-center md:justify-start">
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8, duration: 1 }}
                className="flex items-center gap-2"
              >
                <BookOpen className="w-5 h-5 text-rose-600 dark:text-rose-400" />
                <span className="text-sm text-gray-700 dark:text-gray-300">{t('books_created')}</span>
              </motion.div>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.9, duration: 1 }}
                className="flex items-center gap-2"
              >
                <Shield className="w-5 h-5 text-violet-600 dark:text-violet-400" />
                <span className="text-sm text-gray-700 dark:text-gray-300">{t('coppa_compliant')}</span>
              </motion.div>
            </div>
          </div>

          <div className="flex-1 relative max-w-[500px]">
            <div className="absolute -inset-4 bg-gradient-to-r from-rose-50 to-blue-500 rounded-xl blur-lg opacity-30 animate-pulse dark:bg-gradient-to-r dark:from-violet-200 dark:to-blue-800 dark:opacity-20" />
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5, duration: 1 }}
              className="relative bg-white/80 backdrop-blur-sm p-8 rounded-xl shadow-xl dark:bg-gray-800/80 dark:text-gray-100"
            >
              <img
                src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?auto=format&fit=crop&q=80"
                alt={t('interactive_learning_platform')}
                className="rounded-lg shadow-lg mb-6"
              />
              <div className="grid grid-cols-2 gap-6">
                <div className="bg-white p-4 rounded-lg shadow-sm dark:bg-gray-700 dark:text-gray-100">
                  <h3 className="font-semibold text-gray-900 dark:text-white">{t('interactive_content')}</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">{t('interactive_content_desc')}</p>
                </div>
                <div className="bg-white p-4 rounded-lg shadow-sm dark:bg-gray-700 dark:text-gray-100">
                  <h3 className="font-semibold text-gray-900 dark:text-white">{t('smart_assessment')}</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">{t('smart_assessment_desc')}</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default HeroSection5;
