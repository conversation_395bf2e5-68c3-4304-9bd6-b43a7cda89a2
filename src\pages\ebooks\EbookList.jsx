// src/pages/EbookGallery.jsx
import React, { useState, useEffect } from "react";
import useDataFetching from "@/hooks/useDataFetching";
import api from "@/lib/axios";
import { useNavigate } from "react-router-dom";
import Modal from "@/components/ui/Modal";
import EbookInfoView from "./EbookInfoView";
import EbookForm from "./EbookForm";
import BookCard from "@/components/ui/BookCard";
import Confirm from "@/components/ui/Confirm";
import { toast } from 'sonner';
import { Edit, Trash2, Eye, BookOpen, Info, Edit3 } from 'lucide-react';
import EbookCardSkeleton from "@/components/ui/EbookCardSkeleton";
import EbookFilter from "./EbookFilter";
import { useTranslation } from 'react-i18next';

const EbookGallery = () => {
  const { t } = useTranslation();
  const [page, setPage] = useState(1);
  const [perPage] = useState(20);
  const [search, setSearch] = useState("");
  const [authorId, setAuthorId] = useState(null);
  const [publisherId, setPublisherId] = useState(null);
  const [bookType, setBookType] = useState(null);
  const [status, setStatus] = useState(null);
  const [approvalStatus, setApprovalStatus] = useState(null);
  const navigate = useNavigate();
  const [selectedBook, setSelectedBook] = useState(null);
  const [isInfoModalOpen, setIsInfoModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [totalPages, setTotalPages] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const { data, isLoading, refetch, error } = useDataFetching({
    queryKey: ["ebook", page, perPage, search, authorId, publisherId, bookType, status, approvalStatus],
    endPoint: "/admin/ebooks",
    params: {
      page,
      per_page: perPage,
      search,
      author_id: authorId,
      publisher_id: publisherId,
      book_type: bookType,
      status,
      approval_status: approvalStatus
    },
  });

  const [ebooks, setEbooks] = useState([]);

  useEffect(() => {
    if (error) {
      toast.error("Failed to fetch ebooks. Please try again.");
    }
  }, [error]);

  useEffect(() => {
    if (data?.data) {
      if (page === 1) {
        setEbooks(data.data.data);
      } else {
        setEbooks(prev => [...prev, ...data.data.data]);
      }
      setTotalPages(data.data.total_pages);
      setHasMore(page < data.data.total_pages);
    }
  }, [data, page]);

  const handleSearch = (e) => {
    e.preventDefault();
    setPage(1);
    refetch();
  };

  const handleAuthorChange = (selectedAuthorId) => {
    setAuthorId(selectedAuthorId);
    setPage(1);
    refetch();
  };

  const handlePublisherChange = (selectedPublisherId) => {
    setPublisherId(selectedPublisherId);
    setPage(1);
    refetch();
  };

  const handleBookTypeChange = (selectedBookType) => {
    setBookType(selectedBookType);
    setPage(1);
    refetch();
  };

  const handleStatusChange = (selectedStatus) => {
    setStatus(selectedStatus);
    setPage(1);
    refetch();
  };

  const handleApprovalStatusChange = (selectedApprovalStatus) => {
    setApprovalStatus(selectedApprovalStatus);
    setPage(1);
    refetch();
  };

  const handleLoadMore = () => {
    if (!isLoading && hasMore) {
      setPage(prev => prev + 1);
    }
  };

  const handleDelete = (e, bookId) => {
    Confirm(
      async () => {
        try {
          await api.delete(`/admin/ebooks/${bookId}`);
          setEbooks((prevEbooks) => prevEbooks.filter(book => book.id !== bookId));
        } catch (error) {
          console.error("Error deleting ebook:", error);
        }
      },
      "Are you sure you want to delete this ebook?",
      "This ebook",
      "will be deleted permanently.",
      "Cancel",
      "Delete"
    );
  };

  const handleView = (book) => {
    window.open(`/flipbooks/${book.id}`, '_blank');
  };

  const handleViewBasicInfo = (book) => {
    setSelectedBook(book);
    setIsInfoModalOpen(true);
  };

  const handleEditBasicInfo = (book) => {
    setSelectedBook(book);
    setIsEditModalOpen(true);
  };

  const handleDesignWithEditor = (book) => {
    navigate(`/ebooks/edit/${book.id}`);
  };

  const handleSaveAsTemplate = async (book) => {
    try {
      await api.post(`/admin/ebooks/${book.id}/save-templates`);
    } catch (error) {
      console.error("Error saving template:", error);
    }
  };

  const handleModalClose = () => {
    setIsInfoModalOpen(false);
    setIsEditModalOpen(false);
    setSelectedBook(null);
  };

  const handleCreateBook = () => {
    setSelectedBook(null);
    setIsEditModalOpen(true);
  };

  const generateMenuItems = (book) => {
    const menuItems = [
      {
        label: "View",
        action: () => handleView(book),
        icon: <Eye size={16} />,
      },
      {
        label: "View Basic Info",
        action: () => handleViewBasicInfo(book),
        icon: <Info size={16} />,
      },
      {
        label: "Edit Basic Info",
        action: () => handleEditBasicInfo(book),
        icon: <Edit3 size={16} />,
      },
      {
        label: "Delete",
        action: (e) => handleDelete(e, book.id),
        icon: <Trash2 size={16} />,
        danger: true,
      },
    ];

    if (book.book_type !== "pdf") {
      menuItems.splice(3, 0, {
        label: "Design Book with Editor",
        action: () => handleDesignWithEditor(book),
        icon: <Edit size={16} />,
      });
      menuItems.splice(4, 0, {
        label: "Save as Template",
        action: () => handleSaveAsTemplate(book),
        icon: <BookOpen size={16} />,
      });
    }

    return menuItems;
  };

  return (
    <div className="min-h-screen rounded  bg-white p-6 dark:bg-[#111827]">
      <div className="flex flex-row items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white">eBooks</h2>
        <button
          type="button"
          onClick={handleCreateBook}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 dark:bg-green-700 dark:hover:bg-green-800"
        >
          {t('ebook.button.create')}
        </button>
      </div>

      <EbookFilter
        authorId={authorId}
        publisherId={publisherId}
        onAuthorChange={handleAuthorChange}
        onPublisherChange={handlePublisherChange}
        search={search}
        onSearchChange={setSearch}
        onSearchSubmit={handleSearch}
        bookType={bookType}
        onBookTypeChange={handleBookTypeChange}
        status={status}
        onStatusChange={handleStatusChange}
        approvalStatus={approvalStatus}
        onApprovalStatusChange={handleApprovalStatusChange}
      />

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6 ">
        {ebooks?.map((book) => (
          <BookCard
            key={book.id}
            book={book}
            onView={() => handleView(book)}
            menuItems={generateMenuItems(book)}
            isPdf={book.book_type === "pdf"}
          />
        ))}

        {isLoading && Array.from({ length: 5 }, (_, index) => (
          <EbookCardSkeleton key={index} />
        ))}

        {!isLoading && ebooks.length === 0 && (
          <div className="col-span-full text-center text-gray-500 dark:text-gray-400">
            No books found.
          </div>
        )}
      </div>

      {/* Load More Button */}
      <div className="flex justify-center mt-6">
        {hasMore && (
          <button
            onClick={handleLoadMore}
            disabled={isLoading}
            className="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300"
          >
            {isLoading ? 'Loading...' : 'Load More'}
          </button>
        )}
      </div>

      <Modal
        activeModal={isInfoModalOpen}
        onClose={handleModalClose}
        title={selectedBook ? "Book Basic Information" : ""}
      >
        <EbookInfoView book={selectedBook} />
      </Modal>

      {isEditModalOpen && <Modal
        activeModal={isEditModalOpen}
        onClose={handleModalClose}
        title={selectedBook ? t("ebook.modal.editTitle") : t("ebook.modal.createTitle")}
        className="w-full max-w-3xl"
      >
        <EbookForm handleModalClose={handleModalClose} initialValues={selectedBook} />
      </Modal>}
    </div>
  );
};

export default EbookGallery;
