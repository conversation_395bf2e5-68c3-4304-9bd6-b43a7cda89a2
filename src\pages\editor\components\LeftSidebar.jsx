// src/LeftSidebar.jsx
import { Icon } from "@iconify/react";
import { useSelector, useDispatch } from "react-redux";
import { setSelectedTab, toggleLeftSidebarIsExpanded, setLeftSidebarIsExpanded } from "../store/toolbarSlice";
import Texts from "./left-sidebar/Texts";
import Templates from "./left-sidebar/Templates";
import Shapes from "./left-sidebar/Shapes";
import Elements from "./left-sidebar/Elements";
import { motion } from "framer-motion";
import ImagePicker from "./left-sidebar/ImagePicker";

export default function LeftSidebar() {
  const dispatch = useDispatch();
  const selectedTab = useSelector((state) => state.toolbar.selectedTab);
  const isExpanded = useSelector((state) => state.toolbar.leftSidebarIsExpanded);
  const selectedElements = useSelector((state) => state.pages.present.selectedElements);
  const tabs = [
    { name: "Text", icon: "mdi:format-text", component: <Texts /> },
    { name: "Images", icon: "mdi:image", component: <ImagePicker /> },
    { name: "Elements", icon: "mdi:archive-plus-outline", component: <Elements /> },
    { name: "Shapes", icon: "mdi:shape", component: <Shapes /> },
    { name: "Template", icon: "mdi:file-document", component: <Templates /> },
    // { name: "Uploads", icon: "mdi:upload", component: <Uploads /> },
  ];

  const handleTabClick = (tabName) => {
    dispatch(setSelectedTab(tabName));
    if (!isExpanded) {
      dispatch(setLeftSidebarIsExpanded(true));
    }
  };

  return (
    <div className="relative dark:bg-gray-800 bg-white">
      <div className="h-full w-100 dark:bg-gray-700 bg-white flex border-r dark:border-gray-600"> {/* Removed overflow-y-auto */}
        <div className="w-20 border-r dark:bg-gray-800 bg-white dark:border-gray-600">
          {tabs.map((tab) => (
            <div key={tab.name} className={`cursor-pointer flex flex-col items-center border-b border-gray-300 dark:border-gray-600 py-4 w-full ${selectedTab === tab.name ? 'bg-blue-500 text-white dark:bg-blue-600 dark:text-white' : 'dark:text-white text-gray-600'}`} onClick={() => handleTabClick(tab.name)}>
              <Icon icon={tab.icon} className="h-6 w-6 mb-1 dark:text-white" />
              <span className="font-semibold text-sm dark:text-white">{tab.name}</span>
            </div>
          ))}
        </div>
        <motion.div
          className="w-[300px]"
          initial={{ width: 0, opacity: 0 }}
          animate={{ width: isExpanded ? 300 : 0, opacity: isExpanded ? 1 : 0 }}
          transition={{ duration: 0.4 }}
        >
          <div className="p-4 dark:bg-gray-700 bg-white">
            {tabs.find(tab => tab.name === selectedTab)?.component || <p className="dark:text-white">Component not available</p>}
          </div>
        </motion.div>
      </div>
      <div className="absolute top-1/2 right-[-25px] transform -translate-y-1/2 h-[40px] border dark:border-gray-600 flex items-center justify-center rounded-tr-md rounded-br-md z-10 shadow-md dark:bg-gray-600 bg-white">
        <Icon icon={isExpanded ? "mdi:chevron-left" : "mdi:chevron-right"} className="h-6 w-6 cursor-pointer dark:text-white text-gray-600 hover:text-gray-900 dark:hover:text-gray-400 transition-colors duration-300" onClick={() => dispatch(toggleLeftSidebarIsExpanded())} />
      </div>
    </div>
  );
}
