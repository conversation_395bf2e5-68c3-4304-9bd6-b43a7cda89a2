import { <PERSON>, <PERSON>, Bell } from 'lucide-react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import api from '@/lib/axios';
import { useTranslation } from "react-i18next";

export default function Security() {
  const { t } = useTranslation();

  const validationSchema = Yup.object().shape({
    current_password: Yup.string().required(t("security.validation.required")),
    new_password: Yup.string()
      .min(8, t("security.validation.passwordMin"))
      .required(t("security.validation.required")),
    confirmPassword: Yup.string()
      .oneOf([Yup.ref('new_password'), null], t("security.validation.passwordMismatch"))
      .required(t("security.validation.required")),
  });

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <h2 className="text-2xl font-bold text-gray-800 dark:text-white">{t("security.title")}</h2>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm divide-y dark:divide-gray-700">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">{t("security.changePassword")}</h3>
          <Formik
            initialValues={{ current_password: '', new_password: '', confirmPassword: '' }}
            validationSchema={validationSchema}
            onSubmit={async (values, { setSubmitting }) => {
              setSubmitting(true);
              try {
                await api.put(`${import.meta.env.VITE_HOST_URL}/api/profile/update`, {
                  current_password: values.current_password,
                  new_password: values.new_password
                });
                console.log(t("security.updateSuccess"));
              } catch (error) {
                console.error(t("security.updateError"), error);
              } finally {
                setSubmitting(false);
              }
            }}
          >
            {({ isSubmitting }) => (
              <Form className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t("security.currentPassword")}
                  </label>
                  <Field
                    type="password"
                    name="current_password"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700"
                  />
                  <ErrorMessage name="current_password" component="div" className="text-red-500 text-sm mt-1" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t("security.newPassword")}
                  </label>
                  <Field
                    type="password"
                    name="new_password"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700"
                  />
                  <ErrorMessage name="new_password" component="div" className="text-red-500 text-sm mt-1" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t("security.confirmNewPassword")}
                  </label>
                  <Field
                    type="password"
                    name="confirmPassword"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700"
                  />
                  <ErrorMessage name="confirmPassword" component="div" className="text-red-500 text-sm mt-1" />
                </div>
                <div className="flex justify-end">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    {isSubmitting ? t("security.updating") : t("security.updatePassword")}
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </div>
  );
}
