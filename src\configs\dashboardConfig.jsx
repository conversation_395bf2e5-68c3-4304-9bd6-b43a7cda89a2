import {
  BookOpen,
  FileText,
  Home,
  Mail,
  Settings,
  Tag,
  Users,
  Ticket,
  ShoppingCart,
} from "lucide-react";

export const menuItems = [
  {
    title: "Dashboard",
    icon: <Home className="w-5 h-5" />,
    path: "/admin",
  },
  {
    title: "eBook",
    icon: <FileText className="w-5 h-5" />,
    path: "/admin/ebooks",
  },
  {
    title: "Manage eBooks",
    icon: <FileText className="w-5 h-5" />,
    path: "/admin/pending-ebooks",
  },
  {
    title: "Templates",
    icon: <BookOpen className="w-5 h-5" />,
    path: "/admin/templates",
  },
  {
    title: "Categories",
    icon: <Tag className="w-5 h-5" />,
    path: "/admin/categories",
  },
  {
    title: "Coupons",
    icon: <Ticket className="w-5 h-5" />,
    path: "/admin/coupons",
  },
  {
    title: "Publishers",
    icon: <Mail className="w-5 h-5" />,
    path: "/admin/publishers",
  },
  {
    title: "Authors",
    icon: <Users className="w-5 h-5" />,
    path: "/admin/authors",
  },
  {
    title: "Users",
    icon: <Users className="w-5 h-5" />,
    path: "/admin/users",
  },
  {
    title: "Inquiries",
    icon: <Mail className="w-5 h-5" />,
    path: "/admin/inquiries",
  },
  {
    title: "Orders",
    icon: <ShoppingCart className="w-5 h-5" />,
    path: "/admin/orders",
  },
  {
    title: "Settings",
    icon: <Settings className="w-5 h-5" />,
    submenu: [
      { title: "Profile", path: "/admin/settings/profile" },
      {
        title: "Static Pages",
        icon: <Users className="w-5 h-5" />,
        path: "/admin/static-pages",
      },
    ],
  },
];

export const sidebarVariants = {
  open: {
    width: "16rem",
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 30,
    },
  },
  closed: {
    width: "4rem",
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 30,
    },
  },
};

export const submenuVariants = {
  open: {
    height: "auto",
    opacity: 1,
    transition: {
      duration: 0.2,
      ease: "easeOut",
    },
  },
  closed: {
    height: 0,
    opacity: 0,
    transition: {
      duration: 0.2,
      ease: "easeIn",
    },
  },
};

export const profileMenuVariants = {
  open: { opacity: 1, scale: 1, transition: { duration: 0.2 } },
  closed: { opacity: 0, scale: 0.95, transition: { duration: 0.2 } },
};

export const formattedDate = (timestamp) => {
  if (!timestamp) return '';

  const date = new Date(timestamp);

  const months = [
    'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const day = date.getDate();
  const month = months[date.getMonth()];
  const year = date.getFullYear();

  return `${month} ${day}, ${year}`;
};