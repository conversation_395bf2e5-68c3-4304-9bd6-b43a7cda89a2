import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useParams, useSearchParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import axios from 'axios';
import { toast } from 'sonner';

/**
 * Custom hook for handling bookmark operations
 * Uses the token from URL parameters or Redux store
 */
export function useBookmarks() {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const urlToken = searchParams.get('token');
  const reduxToken = useSelector((state) => state.auth?.token);

  // Use URL token if available, otherwise fall back to redux token
  const token = urlToken || reduxToken;

  const queryClient = useQueryClient();

  // Base URL for API calls
  const baseURL = import.meta.env.VITE_HOST_URL || '';

  // Create axios instance with token
  const bookmarkAxios = axios.create({
    baseURL: `${baseURL}/api`,
  });

  // Add token to all requests
  bookmarkAxios.interceptors.request.use((config) => {
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  });

  // Query for fetching bookmarks
  const { data: bookmarks, isLoading, error, refetch } = useQuery({
    queryKey: ['bookmarkList', id, token],
    queryFn: async () => {
      try {
        const response = await bookmarkAxios.get(`/ebooks/${id}/bookmarks`);
        return response.data.data;
      } catch (error) {
        console.error('Error fetching bookmarks:', error);
        return [];
      }
    },
    enabled: !!id && !!token,
  });

  // Mutation for adding a bookmark
  const addBookmarkMutation = useMutation({
    mutationFn: async (bookmarkData) => {
      const modifiedData = {
        ...bookmarkData,
        page_id: String(bookmarkData.page_id),
      };

      const response = await bookmarkAxios.post('/bookmarks', modifiedData);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['bookmarkList', id]);
    },
    onError: (error) => {
      console.error('Failed to add bookmark:', error);
      toast.error('Failed to add bookmark. Please try again.');
    },
  });

  // Mutation for deleting a bookmark
  const deleteBookmarkMutation = useMutation({
    mutationFn: async (bookmarkId) => {
      const response = await bookmarkAxios.delete(`/bookmarks/${bookmarkId}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['bookmarkList', id]);
    },
    onError: (error) => {
      console.error('Failed to delete bookmark:', error);
      toast.error('Failed to delete bookmark. Please try again.');
    },
  });

  // Function to toggle bookmark (add or remove)
  const toggleBookmark = async (pageId) => {
    try {
      const isBookmarked = bookmarks?.some(
        (bookmark) => bookmark.page_id === parseInt(pageId)
      );

      if (isBookmarked) {
        const bookmarkToDelete = bookmarks.find(
          (bookmark) => bookmark.page_id === parseInt(pageId)
        );

        if (bookmarkToDelete?.id) {
          await deleteBookmarkMutation.mutateAsync(bookmarkToDelete.id);
        }
      } else {
        const bookmarkData = {
          ebook_id: id,
          page_id: pageId,
        };

        await addBookmarkMutation.mutateAsync(bookmarkData);
      }
    } catch (error) {
      console.error('Failed to toggle bookmark:', error);
    }
  };

  // Check if a page is bookmarked
  const isPageBookmarked = (pageId) => {
    return bookmarks?.some(
      (bookmark) => bookmark.page_id === parseInt(pageId)
    ) || false;
  };

  return {
    bookmarks,
    isLoading,
    error,
    refetch,
    addBookmark: addBookmarkMutation.mutate,
    deleteBookmark: deleteBookmarkMutation.mutate,
    toggleBookmark,
    isPageBookmarked,
  };
}
