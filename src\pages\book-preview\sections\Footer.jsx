import React, { useState } from "react";
import FullscreenButton from "../components/FullscreenButton";
import IconButton from "../components/IconButton";
import { useDispatch, useSelector } from "react-redux";
import { setTextToSpeech, setShowGallery, setNoteTakingLayer } from "../store/ebookSlice";
import { setScale, resetScale } from "../store/scaleSlice";
import TextToSpeechModal from "../components/TextToSpeechModal";
import AccessibilityButton from "../components/AccessibilityButton";
import Gallery from "./Gallery";
import Tooltip from "@/components/ui/Tooltip"; 

const Footer = ({ onGeneratePDF, onJumpToBookmark }) => {
  const data = useSelector((state) => state.ebook?.ebookData);
  const [isTextToSpeechModalOpen, setIsTextToSpeechModalOpen] = useState(false);
  const [isGalleryOpen, setIsGalleryOpen] = useState(false);
  const isAuthenticated = useSelector((state) => state.auth.isAuthenticated);

  const dispatch = useDispatch();
  const isTextToSpeechEnabled = useSelector((state) => state.ebook.isTextToSpeechEnabled);
  const scale = useSelector((state) => state.scale.scale); 
  const noteTakingLayer = useSelector((state) => state.ebook.noteTakingLayer);

  const [activeButton, setActiveButton] = useState(null); 

  const handleZoomIn = () => {
    dispatch(setScale(Math.min(scale + 0.1, 2)));
  };
  const handleZoomOut = () => {
    dispatch(setScale(Math.max(scale - 0.1, 0.5)));
  };
  const handleResetZoom = () => {
    dispatch(resetScale());
  };

  const isFlipbook = window.location.pathname.includes("/flipbooks/");

  const commonButtons = [
    { 
      text: "Settings", 
      icon: "mdi:settings", 
      onClick: () => setIsTextToSpeechModalOpen(true), 
      id: "settings",
      isActiveCheck: () => isTextToSpeechModalOpen
    },
    ...(isFlipbook
      ? []
      : [
        { text: "Zoom In", icon: "mdi:magnify-plus", onClick: handleZoomIn, id: "zoomIn" },
        { text: "Zoom Out", icon: "mdi:magnify-minus", onClick: handleZoomOut, id: "zoomOut" },
        { text: "Reset Zoom", icon: "mdi:restore", onClick: handleResetZoom, id: "resetZoom" },
      ]),
    ...(isAuthenticated
      ? [
        {
          text: "Notes",
          icon: "mdi:note",
          onClick: () => dispatch(setNoteTakingLayer(!noteTakingLayer)),
          id: "notes",
          isActiveCheck: () => noteTakingLayer === true
        },
      ]
      : []),
  ];

  const handleListenClick = () => {
    dispatch(setTextToSpeech());
  };

  const handleGalleryClick = () => {
    setIsGalleryOpen(!isGalleryOpen);
    dispatch(setShowGallery(!isGalleryOpen));
  };

  const ebookSpecificButtons = [
    { 
      text: "Gallery", 
      icon: "mdi:image", 
      onClick: handleGalleryClick, 
      id: "gallery",
      isActiveCheck: () => isGalleryOpen
    },
    {
      text: "Listen",
      icon: isTextToSpeechEnabled ? "mdi:pause-circle" : "mdi:play-circle", 
      onClick: handleListenClick,
      id: "listen",
      isActiveCheck: () => isTextToSpeechEnabled === true
    },
  ];

  const buttons = data?.book_type === "ebook" ? [...commonButtons, ...ebookSpecificButtons] : commonButtons;

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 bg-gray-100 dark:bg-[#111827] dark:border-gray-100 dark:border-t  shadow-md z-50"> {/* Reduced padding from p-2 to p-1 */}
        <div className="flex flex-wrap justify-center items-center gap-1"> {/* Reduced gap from gap-2 to gap-1 */}
          <Tooltip text="Fullscreen" position="top">
            <div className="scale-75"> {/* Added scaling wrapper */}
              <FullscreenButton />
            </div>
          </Tooltip>
          {buttons.map((button) => {
            let isCurrentlyActive;
            if (button.isActiveCheck) {
              isCurrentlyActive = button.isActiveCheck();
            } else {
              isCurrentlyActive = activeButton === button.id;
            }

            return (
              <Tooltip key={button.id} text={button.text} position="top">
                <div className="scale-75"> {/* Added scaling wrapper */}
                  <IconButton
                    icon={button.icon}
                    text={button.text}
                    onClick={button.onClick}
                    activeButton={isCurrentlyActive}
                  />
                </div>
              </Tooltip>
            );
          })}
          {data?.book_type === "ebook" && (
            <Tooltip text="Accessibility Settings" position="top">
              <div className="scale-75"> {/* Added scaling wrapper */}
                <AccessibilityButton />
              </div>
            </Tooltip>
          )}
        </div>
      </div>
      <TextToSpeechModal 
        isOpen={isTextToSpeechModalOpen} 
        handleClose={() => setIsTextToSpeechModalOpen(false)} 
      />
      <Gallery 
        isOpen={isGalleryOpen}
        onClose={() => {
          setIsGalleryOpen(false);
          dispatch(setShowGallery(false));
        }}
      />
    </>
  );
};

export default Footer;
