// Properties.jsx
import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { updateElement, alignElements } from '../store/pagesSlice';
import CommonProperties from './properties/CommonProperties';
import TextProperties from './properties/TextProperties';
import ImageBorderProperties from './properties/ImageBorderProperties';
import MCQProperties from './properties/MCQProperties';
import AlignmentProperties from './properties/AlignmentProperties';
import TrueFalseProperties from './properties/TrueFalseProperties';
import FlashCardProperties from './properties/FlashCardProperties';
import PageProperties from './properties/PageProperties';
import ShapeProperties from './properties/ShapeProperties';
import VideoProperties from './properties/VideoProperties';
import AudioProperties from './properties/AudioProperties';
import IndexProperties from './properties/IndexProperties';

const Properties = () => {
  const dispatch = useDispatch();
  const selectedElements = useSelector((state) => state.pages.present.selectedElements);
  const pages = useSelector((state) => state.pages.present.pages);
  const currentPage = useSelector((state) => state.pages.present.currentPage);
  const element = pages[currentPage]?.elements.find((el) => el.id === selectedElements[0]) || {};
  const { position = { x: 0, y: 0 }, size = { width: 100, height: 100 }, style = {} } = element;

  const handleUpdate = (updates) => {
    dispatch(updateElement({
      id: selectedElements[0],
      updates
    }));
  };

  const handlePositionChange = (axis, value) => {
    const parsedValue = parseInt(value, 10);
    if (isNaN(parsedValue)) return;
    handleUpdate({
      position: { ...position, [axis]: parsedValue }
    });
  };

  const handleSizeChange = (dimension, value) => {
    const parsedValue = parseInt(value, 10);
    if (isNaN(parsedValue)) return;
    handleUpdate({
      size: { ...size, [dimension]: parsedValue }
    });
  };

  const handleStyleChange = (property, value) => {
    handleUpdate({
      style: { ...style, [property]: value }
    });
  };

  const execCommand = (command, value = null) => {
    document.execCommand(command, false, value);
  };

  return (
    <div className="flex flex-col min-h-[100px]">


      {/* {element.type === 'text' && (
        <TextProperties
          element={element}
          execCommand={execCommand}
        />
      )} */}

      {element.type === 'image' && (
        <ImageBorderProperties
          element={element}
          handleUpdate={handleUpdate}
        />
      )}

      {element.type === 'mcq' && (
        <MCQProperties
          handleUpdate={handleUpdate}
          element={element}
        />
      )}
      {element.type === 'true-false' && (
        <TrueFalseProperties
          handleUpdate={handleUpdate}
          element={element}
        />
      )}
      {element.type === 'flashcard' && (
        <FlashCardProperties
          handleUpdate={handleUpdate}
          element={element}
        />
      )}
      {element.type === 'shape' && (
        <ShapeProperties
          handleUpdate={handleUpdate}
          element={element}
        />
      )}
      {element.type === 'video' && (
        <VideoProperties
          handleUpdate={handleUpdate}
          element={element}
        />
      )}
      {element.type === 'index-list' && (
  <IndexProperties
    element={element}
    handleUpdate={handleUpdate}
  />
)}

{element.type === 'audio' && (
  <AudioProperties
    handleUpdate={handleUpdate}
    element={element}
  />
)}
      {selectedElements.length > 1 && (
        <AlignmentProperties
          handleAlignment={(alignment) => dispatch(alignElements(alignment))}
        />
      )}
      {
        selectedElements.length == 0 && (
          <PageProperties />
        )
      }
      {
        selectedElements.length ? <CommonProperties
          position={position}
          size={size}
          handlePositionChange={handlePositionChange}
          handleSizeChange={handleSizeChange}
          handleStyleChange={handleStyleChange}
          style={style}
        /> : null
      }
    </div>
  );
};

export default Properties;