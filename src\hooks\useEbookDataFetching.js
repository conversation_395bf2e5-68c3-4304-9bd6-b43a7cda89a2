import { useQuery } from "@tanstack/react-query";
import api from "@/lib/axios";
import { useState, useEffect, useCallback } from "react";

/**
 * Custom hook for fetching ebook data with pagination support
 * Fetches all pages based on total_pages value from initial response
 */
const useEbookDataFetching = ({ queryKey, ebookId, enabled = true }) => {
  const [completeData, setCompleteData] = useState(null);
  const [isLoadingPages, setIsLoadingPages] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [initialDataLoaded, setInitialDataLoaded] = useState(false);

  // Fixed chunk size - no UI control needed
  const chunkSize = 100;

  // Initial data fetch using React Query
  const {
    data: initialData,
    isLoading: isLoadingInitial,
    error,
    refetch,
  } = useQuery({
    queryKey: [queryKey, ebookId],
    queryFn: async () => {
      try {
        console.log(`Fetching initial data for ebook ${ebookId}`);
        const response = await api.get(`/single-ebook/${ebookId}`, {
          params: {
            per_page: chunkSize,
            pagination: true,
            page: 1,
          },
        });
        return response.data;
      } catch (error) {
        console.error("Error in initial fetch:", error);
        throw new Error(
          error?.response?.data?.message || "Data fetching error"
        );
      }
    },
    enabled,
  });

  /**
   * Fetch a specific page of ebook data
   */
  const fetchPageData = useCallback(async (page) => {
    try {
      console.log(`Fetching page ${page} for ebook ${ebookId}`);
      const response = await api.get(`/single-ebook/${ebookId}`, {
        params: {
          per_page: chunkSize,
          pagination: true,
          page: page,
        },
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching page ${page}:`, error);
      throw error;
    }
  }, [ebookId, chunkSize]);

  // Effect to fetch all pages when initial data is loaded
  useEffect(() => {
    const fetchAllPages = async () => {
      if (!initialData) return;

      try {
        // Format the initial data to make it available immediately
        const formattedInitialData = {
          ...initialData,
          pages: initialData.pages || [],
        };

        // Make initial data available immediately
        setCompleteData(formattedInitialData);
        setInitialDataLoaded(true);

        // Start loading all pages
        setIsLoadingPages(true);

        // Get total pages from the response
        const totalPages = initialData.total_pages || 1;
        console.log(`Total pages to fetch: ${totalPages}`);

        // If only one page, we're done
        if (totalPages <= 1) {
          console.log(`Only one page needed, already loaded`);
          setIsLoadingPages(false);
          return;
        }

        // Initialize pages array
        let allPages = [...(initialData.pages || [])];
        console.log(`Starting with ${allPages.length} pages from initial data`);

        // Fetch all pages (including page 1 again to ensure we have all data)
        const fetchPromises = [];

        // Create promises for all page fetches
        for (let page = 1; page <= totalPages; page++) {
          fetchPromises.push(fetchPageData(page));
        }

        // Execute all fetch requests in parallel with a limit of 3 concurrent requests
        const concurrencyLimit = 3;
        const results = [];

        for (let i = 0; i < fetchPromises.length; i += concurrencyLimit) {
          const batch = fetchPromises.slice(i, i + concurrencyLimit);
          const batchResults = await Promise.allSettled(batch);
          results.push(...batchResults);

          // Update progress after each batch
          setLoadingProgress(Math.floor(((i + batch.length) / totalPages) * 100));
        }

        // Process all results
        results.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            const pageData = result.value;
            const pageNumber = index + 1;

            // Extract pages from the response
            const newPages = pageData.data?.pages || [];

            if (newPages.length === 0) {
              console.log(`No pages found in page ${pageNumber}, skipping`);
              return;
            }

            // Avoid duplicates by merging only unique pages
            const existingPageIds = new Set(allPages.map((page) => page.id));
            const uniqueNewPages = newPages.filter(
              (page) => !existingPageIds.has(page.id)
            );

            allPages = [...allPages, ...uniqueNewPages];
            console.log(
              `Added ${uniqueNewPages.length} unique pages from page ${pageNumber}, total so far: ${allPages.length}`
            );
          } else {
            console.error(`Error fetching page ${index + 1}:`, result.reason);
          }
        });

        // Update complete data with all pages
        console.log(`Total pages loaded: ${allPages.length}`);
        setCompleteData({
          ...initialData,
          pages: allPages,
        });
        setLoadingProgress(100);
      } catch (error) {
        console.error("Error fetching all pages:", error);
      } finally {
        setIsLoadingPages(false);
      }
    };

    fetchAllPages();
  }, [initialData, fetchPageData]);

  // Log when data is fully loaded
  useEffect(() => {
    if (completeData) {
      const pagesCount = completeData.pages?.length || 0;
      console.log(`Data loaded with ${pagesCount} pages`);
    }
  }, [completeData]);

  /**
   * Sync with latest data after operations
   */
  const syncWithLatestData = async () => {
    try {
      setIsLoadingPages(true);
      console.log("Synchronizing with latest data...");

      // Refetch all data
      await refetch();

      console.log("Synchronized with latest data after page operation");
    } catch (error) {
      console.error("Error synchronizing data:", error);
    } finally {
      setIsLoadingPages(false);
    }
  };

  return {
    data: completeData,
    isLoading: isLoadingInitial,
    isLoadingMore: isLoadingPages,
    initialDataLoaded,
    loadingProgress,
    error,
    refetch,
    syncWithLatestData,
  };
};

export default useEbookDataFetching;
