import React from "react";
import Select from "react-select";
import { useField } from "formik";

const InputSelect = ({
    label,
    options = [],
    valueKey = "value",
    labelKey = "label",
    placeholder, // Add placeholder prop
    onRoleSelect,
    className,
    onlyLabel,
    required,
    ...props
}) => {
    const [field, , helpers] = useField(props);

    const handleChange = (selectedOption, event) => {
        if (onlyLabel) {
            helpers.setValue(selectedOption.label);
        } else {
            helpers.setValue(selectedOption.value);
        }
        if (typeof onRoleSelect === "function") {
            onRoleSelect(onlyLabel ? selectedOption.label : selectedOption.value);
        }
    };

    const formattedOptions = options?.map((option) => ({
        value: option[valueKey],
        label: option[labelKey],
    }));
    // Set initial value if field value is undefined
    React.useEffect(() => {
        if (field.value === undefined && formattedOptions.length > 0) {
            const initialValue = formattedOptions[0];
            helpers.setValue(initialValue.value);
        }
    }, [field.value, formattedOptions, helpers]);
    return (
        <div className={className}>
            <label
                className="block text-[#000000] text-base font-normal mb-2"
                htmlFor={props.id || props.name}
            >
                {label} {required && <span className="text-red-500">*</span>}
            </label>
            <div>
                <Select
                    value={formattedOptions.find(
                        (opt) => opt.value === field.value
                    )}
                    options={formattedOptions}
                    onChange={handleChange}
                    onBlur={() => helpers.setTouched(true)}
                    placeholder={placeholder} // Pass placeholder prop to Select component
                />
            </div>
            {field.touched && field.error ? <div>{field.error}</div> : null}
        </div>
    );
};

export default InputSelect;
