import { useField } from 'formik';

export const RadioGroup = ({ label, options, ...props }) => {
  const [field, meta] = useField(props);
  
  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
        {label}
      </label>
      <div className="space-y-2">
        {options.map((option) => (
          <div key={option.value} className="flex items-center">
            <input
              type="radio"
              {...field}
              value={option.value}
              checked={field.value === option.value}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
            />
            <label className="ml-2 text-sm text-gray-700 dark:text-gray-200">
              {option.label}
            </label>
          </div>
        ))}
      </div>
      {meta.touched && meta.error ? (
        <div className="text-red-600 dark:text-red-400 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};