// src/pages/editor/components/properties/IndexProperties.jsx
import React from "react";
import { PlusCircle, MinusCircle } from "lucide-react";

const IndexProperties = ({ element, handleUpdate }) => {
  const { content } = element;
  const title = content.title || "Index";
  const items = content.items || [];
  const template = content.template || "classic";

  const handleTitleChange = (e) => {
    handleUpdate({
      content: { ...content, title: e.target.value },
    });
  };

  const handleItemChange = (index, field, value) => {
    const newItems = items.map((item, idx) =>
      idx === index ? { ...item, [field]: value } : item
    );
    handleUpdate({
      content: { ...content, items: newItems },
    });
  };

  const handleAddItem = () => {
    const newItem = {
      page: "",
      label: "",
      anchorId: Math.random().toString(36).substr(2, 9),
      // description: '' // we could add an empty description by default if you like
    };
    handleUpdate({
      content: { ...content, items: [...items, newItem] },
    });
  };

  const handleRemoveItem = (index) => {
    const newItems = items.filter((_, idx) => idx !== index);
    handleUpdate({
      content: { ...content, items: newItems },
    });
  };

  const handleTemplateChange = (e) => {
    handleUpdate({
      content: { ...content, template: e.target.value },
    });
  };

  return (
    <div className="space-y-4">
      {/* Edit Index Title */}
      {/* <div>
        <label className="block text-sm font-medium mb-1">Index Title</label>
        <input
          type="text"
          value={title}
          onChange={handleTitleChange}
          className="w-full p-2 border rounded-md"
        />
      </div> */}

      {/* Template Selector */}
      <div>
        <label className="block text-sm font-medium mb-1">Template</label>
        <select
          value={template}
          onChange={handleTemplateChange}
          className="w-full p-2 border rounded-md"
        >
          <option value="classic">Classic (Dotted Line)</option>
          <option value="greenStripes">Green Stripes</option>
          <option value="twoColumnOld">Old Book (Two-Column Layout)</option>
          <option value="modernGraphic">Modern Graphic Layout</option>
        </select>
      </div>

      {/* Edit Index Items */}
      <div>
        <div className="flex justify-between items-center mb-2">
          <label className="block text-sm font-medium">Content Items</label>
          <button
            onClick={handleAddItem}
            className="p-1 text-blue-500 hover:text-blue-600"
          >
            <PlusCircle size={20} />
          </button>
        </div>

        {items.map((item, index) => (
          <div
            key={item.anchorId || index}
            className="space-y-2 mb-4 border-b pb-2"
          >
            {/* Row for page & label */}
            <div className="flex items-center space-x-2">
              <input
                type="text"
                placeholder="Page"
                value={item.page}
                onChange={(e) =>
                  handleItemChange(index, "page", e.target.value)
                }
                className="w-20 p-2 border rounded-md"
              />
              <input
                type="text"
                placeholder="Label"
                value={item.label}
                onChange={(e) =>
                  handleItemChange(index, "label", e.target.value)
                }
                className="flex-1 p-2 border rounded-md"
              />
              <button
                onClick={() => handleRemoveItem(index)}
                className="p-1 text-red-500 hover:text-red-600"
                disabled={items.length <= 1}
              >
                <MinusCircle size={20} />
              </button>
            </div>

            {/* Conditionally show description input or "Add" button */}
            {template === "modernGraphic" && (
              <>
                {item.description === undefined || item.description === null ? (
                  <button
                    onClick={() => handleItemChange(index, "description", "")}
                    className="text-blue-500 hover:text-blue-600 text-sm"
                  >
                    + Add Description
                  </button>
                ) : (
                  <input
                    type="text"
                    placeholder="Description"
                    value={item.description}
                    onChange={(e) =>
                      handleItemChange(index, "description", e.target.value)
                    }
                    className="w-full p-2 border rounded-md"
                  />
                )}
              </>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default IndexProperties;
