// pagesSlice.js

import { createSlice } from "@reduxjs/toolkit";
import undoable, { excludeAction } from "redux-undo";
import {
  alignLeft,
  alignCenter,
  alignRight,
  justifyElements,
  alignTop,
  alignBottom,
  alignMiddle,
} from "../utils/alignmentUtils";
import { v4 as uuidv4 } from "uuid";

// Define the gap below the original element when pasting
const PASTE_GAP_BELOW = 10;
// Optional: Add a small horizontal nudge to avoid exact overlap if desired
const PASTE_HORIZONTAL_OFFSET = 0; // Set to 0 to keep same X, or e.g., 10

// Initial Present State with one default page
const initialPresentState = {
  pages: [],
  currentPage: 0,
  selectedElements: [], // Array of selected element IDs
  showCode: false,
  copiedElement: null, // For single copy/cut
  copiedElements: [], // For multiple copy/cut
  selectedTool: "select",
  scale: 1, // UI scale factor
  editingElementId: null,
  showGuide: false, // Show guide flag
};

const pagesSlice = createSlice({
  name: "pages",
  initialState: initialPresentState,
  reducers: {
    // ... (other reducers like addElement, updateElement, copyElements etc. remain the same as your last provided version) ...
    addElement: (state, action) => {
      const newElement = {
        id: uuidv4(),
        position: { x: 50, y: 50 },
        size: { width: 200, height: 50 },
        ...action.payload,
      };
      // Ensure current page exists
      if (!state.pages[state.currentPage]) {
        state.pages[state.currentPage] = {
          id: uuidv4(),
          elements: [],
          layout: { gutter: 50, columns: 2 },
          style: {},
          name: `Page ${state.pages.length + 1}`,
        };
      }
      state.pages[state.currentPage].elements.push(newElement);
      state.selectedElements = [newElement.id];
    },
    updateElement: (state, action) => {
      const { id, updates } = action.payload;
      // Ensure updates does not include the id itself if it's nested
      const cleanUpdates = { ...updates };
      delete cleanUpdates.id;

      const page = state.pages[state.currentPage];
      const element = page?.elements.find(
        (el) => el.id === id
      );

      if (element) {
        // Deep merge updates into the element for nested properties like style or position
        // Simple Object.assign is shallow
        // Consider using a deep merge utility if necessary, or handle specific nested updates
        // For position and size specifically:
        if (cleanUpdates.position) {
            element.position = { ...element.position, ...cleanUpdates.position };
            delete cleanUpdates.position;
        }
        if (cleanUpdates.size) {
            element.size = { ...element.size, ...cleanUpdates.size };
            delete cleanUpdates.size;
        }
         if (cleanUpdates.style) {
            element.style = { ...element.style, ...cleanUpdates.style };
            delete cleanUpdates.style;
        }
        // Assign remaining top-level updates
        Object.assign(element, cleanUpdates);

      } else {
        console.warn(`Element with id ${id} not found.`);
      }
    },
    reorderElements: (state, action) => {
      const { pageId, newOrder } = action.payload;
      const page = state.pages.find((page) => page.id === pageId);
      if (page) {
        page.elements = newOrder;
      } else {
        console.warn(`Page with id ${pageId} not found.`);
      }
    },
    setSelectedElement: (state, action) => {
      state.selectedElements = action.payload ? [action.payload] : [];
    },
    addPage: (state, action) => {
      const newPage = {
        id: uuidv4(),
        elements: [],
        layout: { gutter: 50, columns: 2 },
        style: {},
        name: `Page ${state.pages.length + 1}`,
        ...(action.payload || {}), // Spread payload safely
      };
      state.pages.push(newPage);
      state.currentPage = state.pages.length - 1;
    },
    setCurrentPage: (state, action) => {
       // Ensure the index is valid
      if (action.payload >= 0 && action.payload < state.pages.length) {
         state.currentPage = action.payload;
         state.selectedElements = []; // Deselect elements when changing page
         state.editingElementId = null;
      } else {
        console.warn(`Invalid page index: ${action.payload}`);
      }
    },
    toggleCode: (state) => {
      state.showCode = !state.showCode;
    },
    deleteElement: (state) => { // Assumes single element deletion based on name? Use deleteElements
        const page = state.pages[state.currentPage];
        if (page && state.selectedElements.length > 0) {
          page.elements = page.elements.filter(
            (el) => !state.selectedElements.includes(el.id)
          );
          state.selectedElements = [];
          state.editingElementId = null; // Clear editing state too
        }
    },
    deleteElements: (state) => { // Use this for deleting selected elements
      const page = state.pages[state.currentPage];
      if (page && state.selectedElements.length > 0) {
        page.elements = page.elements.filter(
          (el) => !state.selectedElements.includes(el.id)
        );
        state.selectedElements = [];
         state.editingElementId = null; // Clear editing state too
      }
    },
    copyElement: (state) => { // Usually better to handle only multi-copy via copyElements
      const page = state.pages[state.currentPage];
      const element = page?.elements.find(
        (el) => el.id === state.selectedElements[0]
      );
      if (element) {
        // Store a deep clone in the single element clipboard
        state.copiedElement = JSON.parse(JSON.stringify(element));
        // Clear the multi-element clipboard if activating single copy
        state.copiedElements = [];
      }
    },
    copyElements: (state) => {
      const page = state.pages[state.currentPage];
      const elementsToCopy = page?.elements.filter((el) =>
        state.selectedElements.includes(el.id)
      );
      if (elementsToCopy && elementsToCopy.length > 0) {
        // Store deep clones in the multi-element clipboard
        state.copiedElements = JSON.parse(JSON.stringify(elementsToCopy));
         // Clear the single element clipboard if activating multi copy
        state.copiedElement = null;
      } else {
         state.copiedElements = []; // Ensure it's always an array
      }
    },
    cutElement: (state) => { // Usually better to handle only multi-cut via cutElements
      const page = state.pages[state.currentPage];
      const element = page?.elements.find(
        (el) => el.id === state.selectedElements[0]
      );
      if (element) {
        // Copy first (deep clone)
        state.copiedElement = JSON.parse(JSON.stringify(element));
        state.copiedElements = [];
        // Then delete
        page.elements = page.elements.filter((el) => el.id !== element.id);
        state.selectedElements = [];
        state.editingElementId = null;
      }
    },
    cutElements: (state) => {
      const page = state.pages[state.currentPage];
      const elementsToCut = page?.elements.filter((el) =>
        state.selectedElements.includes(el.id)
      );
      if (elementsToCut && elementsToCut.length > 0) {
         // Copy first (deep clones)
        state.copiedElements = JSON.parse(JSON.stringify(elementsToCut));
        state.copiedElement = null;
         // Then delete
        page.elements = page.elements.filter(
          (el) => !state.selectedElements.includes(el.id)
        );
        state.selectedElements = [];
        state.editingElementId = null;
      } else {
          state.copiedElements = []; // Ensure it's clear if nothing was cut
      }
    },
    // pasteElement handles pasting from the single-element clipboard
    pasteElement: (state) => {
        if (state.copiedElement) {
            const page = state.pages[state.currentPage];
            if (!page) {
                console.warn("Current page is undefined.");
                return;
            }
            // Create a new element from the single clipboard item
            const elementDataToPaste = JSON.parse(JSON.stringify(state.copiedElement));

            // Calculate position below original
            const originalPos = elementDataToPaste.position;
            const originalSize = elementDataToPaste.size;
            const newX = (originalPos?.x ?? 0) + PASTE_HORIZONTAL_OFFSET;
            const newY = (originalPos?.y ?? 0) + (originalSize?.height ?? 0) + PASTE_GAP_BELOW;

            const newElement = {
                 ...elementDataToPaste,
                 id: uuidv4(),
                 position: { x: newX, y: newY },
            };
            page.elements.push(newElement);
            state.selectedElements = [newElement.id]; // Select the newly pasted element
        } else {
             console.warn("No single element to paste.");
        }
    },
    // Corrected pasteElements - handles pasting from the multi-element clipboard
    pasteElements: (state) => {
      if (state.copiedElements && state.copiedElements.length > 0) {
        const page = state.pages[state.currentPage];
        if (!page) {
          console.warn("Current page is undefined.");
          return;
        }

        const newElements = state.copiedElements.map((copiedEl) => {
          // Create a deep clone from the already cloned data in clipboard
          const elementDataToPaste = JSON.parse(JSON.stringify(copiedEl));

          // Calculate new position based on original position and size
          const originalPos = elementDataToPaste.position;
          const originalSize = elementDataToPaste.size;

          const newX = (originalPos?.x ?? 0) + PASTE_HORIZONTAL_OFFSET;
          const newY = (originalPos?.y ?? 0) + (originalSize?.height ?? 0) + PASTE_GAP_BELOW;

          return {
            ...elementDataToPaste, // Spread the deeply cloned data
            id: uuidv4(),      // Generate a unique ID for the new element
            position: {        // Set the calculated position
              x: newX,
              y: newY,
            },
          };
        });

        page.elements.push(...newElements);
        state.selectedElements = newElements.map((el) => el.id); // Select all newly pasted elements

        // Keeping clipboard allows multiple pastes from the same copy action.
        // state.copiedElements = []; // Uncomment this line if you want only one paste per copy.

      } else {
        console.warn("No elements to paste from copiedElements.");
      }
    },

    // ... (Rest of the reducers: selectTool, alignElements, deletePage, etc.) ...
     selectTool: (state, action) => {
      state.selectedTool = action.payload;
      state.selectedElements = []; // Often good to deselect when changing tools
      state.editingElementId = null;
    },
    addToSelection: (state, action) => {
      if (!state.selectedElements.includes(action.payload)) {
        state.selectedElements.push(action.payload);
      }
    },
    alignElements: (state, action) => {
      const page = state.pages[state.currentPage];
      const alignment = action.payload;

      // Need page dimensions for some alignments
       const pageWidth = page?.size?.width; // Assuming page has a size property
       const pageHeight = page?.size?.height;
      // if (!pageWidth || !pageHeight) {
      //    console.warn("Page dimensions needed for alignment are missing.");
      //    // Find page dimensions differently if needed
      // }


      const selectedElementsData = page?.elements.filter((el) =>
        state.selectedElements.includes(el.id)
      );

      if (!selectedElementsData || selectedElementsData.length < 2) return; // Need at least two elements to align

       // IMPORTANT: Alignment utilities likely mutate the element objects directly.
       // This is generally okay inside createSlice reducers due to Immer.
      switch (alignment) {
        case "left":
          alignLeft(selectedElementsData);
          break;
        case "center":
           if (pageWidth) alignCenter(selectedElementsData, pageWidth);
          break;
        case "right":
           if (pageWidth) alignRight(selectedElementsData, pageWidth);
          break;
        case "justify":
           if (pageWidth) justifyElements(selectedElementsData, pageWidth);
          break;
        case "top":
          alignTop(selectedElementsData);
          break;
        case "bottom":
           if (pageHeight) alignBottom(selectedElementsData, pageHeight);
          break;
        case "middle":
           if (pageHeight) alignMiddle(selectedElementsData, pageHeight);
          break;
        default:
          console.log("Unknown alignment:", alignment);
      }
       // No need to manually update state if alignment utils mutate directly due to Immer
    },
    deletePage: (state, action) => {
      const pageIdToDelete = action.payload;
      const pageIndex = state.pages.findIndex(
        (page) => page.id === pageIdToDelete
      );

      if (pageIndex !== -1 && state.pages.length > 1) { // Prevent deleting the last page
        state.pages.splice(pageIndex, 1);

         // Adjust currentPage index if necessary
        if (state.currentPage >= state.pages.length) {
             // If the last page was deleted, move to the new last page
            state.currentPage = state.pages.length - 1;
        } else if (state.currentPage === pageIndex) {
             // If the current page was deleted, stay at the same index (which now shows the next page)
             // Or optionally move to previous: state.currentPage = Math.max(0, pageIndex - 1);
             // No change needed if a page *before* the current one was deleted.
        }
        state.selectedElements = []; // Deselect elements after deleting page
        state.editingElementId = null;

      } else if (state.pages.length <= 1) {
          console.warn("Cannot delete the last page.");
      } else {
        console.warn(`Page with id ${pageIdToDelete} not found.`);
      }
    },
    duplicatePage: (state, action) => {
      const pageIdToDuplicate = action.payload;
      const pageIndex = state.pages.findIndex(
        (page) => page.id === pageIdToDuplicate
      );
      if (pageIndex !== -1) {
        const originalPage = state.pages[pageIndex];
         // Deep clone the original page and its elements
        const duplicatedPage = JSON.parse(JSON.stringify({
          ...originalPage,
          id: uuidv4(),
          name: `${originalPage.name || 'Page'} Copy`, // Handle potentially undefined name
        }));

        // Generate new IDs for all duplicated elements
        duplicatedPage.elements = duplicatedPage.elements.map(el => ({
            ...el,
            id: uuidv4(),
             // Apply offset to duplicated elements on the new page? Optional.
            // position: {
            //   x: (el.position?.x ?? 0) + PASTE_HORIZONTAL_OFFSET, // Using defined offset constant
            //   y: (el.position?.y ?? 0) + (el.size?.height ?? 0) + PASTE_GAP_BELOW, // Using defined offset constant
            // },
        }));

        state.pages.splice(pageIndex + 1, 0, duplicatedPage);
         // Optionally switch to the new duplicated page
        // state.currentPage = pageIndex + 1;
        // state.selectedElements = [];
      } else {
        console.warn(`Page with id ${pageIdToDuplicate} not found.`);
      }
    },
    setScale: (state, action) => {
      state.scale = action.payload;
    },
    updatePageStyle: (state, action) => {
      const { id, style: styleUpdates } = action.payload;
      const page = state.pages.find((page) => page.id === id);
      if (page) {
        page.style = { ...(page.style || {}), ...styleUpdates }; // Ensure page.style exists
      } else {
        console.warn(`Page with id ${id} not found.`);
      }
    },
    updatePageLayout: (state, action) => {
      const { id, layout: layoutUpdates } = action.payload;
      const page = state.pages.find((page) => page.id === id);
      if (page) {
        page.layout = { ...(page.layout || {}), ...layoutUpdates }; // Ensure page.layout exists
      } else {
        console.warn(`Page with id ${id} not found.`);
      }
    },
    // Simple layering assumes element order = stacking order
    sendToBack: (state, action) => {
      const { id } = action.payload;
      const page = state.pages[state.currentPage];
      const elementIndex = page?.elements.findIndex((el) => el.id === id);
       // Check if element exists and is not already at the back
      if (page && elementIndex !== undefined && elementIndex > 0) {
        const [element] = page.elements.splice(elementIndex, 1);
        page.elements.unshift(element); // Move to the beginning (back)
      }
    },
    sendToFront: (state, action) => {
      const { id } = action.payload;
      const page = state.pages[state.currentPage];
      const elementIndex = page?.elements.findIndex((el) => el.id === id);
       // Check if element exists and is not already at the front
      if (page && elementIndex !== undefined && elementIndex < page.elements.length - 1) {
        const [element] = page.elements.splice(elementIndex, 1);
        page.elements.push(element); // Move to the end (front)
      }
    },
    setPages: (state, action) => {
      state.pages = action.payload || []; // Ensure pages is an array
      // Reset state related to pages
      state.currentPage = 0;
      state.selectedElements = [];
      state.editingElementId = null;
      state.copiedElement = null;
      state.copiedElements = [];

      // Ensure currentPage is valid if loaded pages are fewer than expected
      if (state.currentPage >= state.pages.length) {
           state.currentPage = 0;
      }
    },
    setEditingElementId: (state, action) => {
      state.editingElementId = action.payload;
       // Usually deselect other elements when starting to edit one
      if (action.payload !== null) {
          state.selectedElements = [action.payload];
      }
    },
    toggleGuide: (state) => {
      state.showGuide = !state.showGuide;
    },
    reorderPages: (state, action) => {
       // Expects payload to be the new, reordered array of pages
       if (Array.isArray(action.payload)) {
           // Basic validation: check if page IDs match? Maybe too complex here.
           state.pages = action.payload;
           // Update current page index if its ID moved? More complex - often easier
           // to just stay on the page ID that *was* current, if it still exists.
           // Or switch to page at the old index:
           const oldCurrentPageId = state.pages[state.currentPage]?.id;
           const newIndex = state.pages.findIndex(p => p.id === oldCurrentPageId);
           if (newIndex !== -1) {
               state.currentPage = newIndex;
           } else if (state.pages.length > 0) {
                // Fallback if current page ID is gone
               state.currentPage = Math.min(state.currentPage, state.pages.length - 1);
           } else {
               state.currentPage = 0; // Should be handled by deletePage/setPages logic
           }

       } else {
           console.warn("reorderPages expects an array payload.");
       }
    },
    updatePageName: (state, action) => {
      const { id, name } = action.payload;
      const page = state.pages.find((page) => page.id === id);
      if (page) {
        page.name = name;
      } else {
        console.warn(`Page with id ${id} not found.`);
      }
    },
  },
});

// Wrap the reducer with `undoable`
const undoablePagesReducer = undoable(pagesSlice.reducer, {
  // Exclude actions that should not be undoable
  filter: excludeAction([
    "pages/setScale",
    "pages/setEditingElementId",
    "pages/setCurrentPage", // Exclude page navigation from undo
    "pages/toggleCode",
    "pages/selectTool", // Exclude tool selection
    "pages/addToSelection", // Usually selection changes aren't undoable
    "pages/setSelectedElement", // Usually selection changes aren't undoable
    "pages/setPages", // Loading data shouldn't be undoable
    "pages/toggleGuide",
    // Add other action types you want to exclude from undo/redo
  ]),
  limit: 100, // Limit history to 100 states
   // Group related actions like copy/paste? More advanced.
});

// Export actions
export const {
  addElement,
  updateElement,
  reorderElements,
  setSelectedElement,
  addPage,
  setCurrentPage,
  toggleCode,
  deleteElement, // Consider removing if deleteElements is primary
  deleteElements,
  copyElement,   // Consider removing if copyElements is primary
  copyElements,
  cutElement,    // Consider removing if cutElements is primary
  cutElements,
  pasteElement,  // Handles single paste
  pasteElements, // Handles multi paste
  selectTool,
  addToSelection,
  alignElements,
  deletePage,
  duplicatePage,
  setScale,
  updatePageStyle,
  updatePageLayout,
  sendToBack,
  sendToFront,
  setPages,
  setEditingElementId,
  toggleGuide,
  reorderPages,
  updatePageName,
} = pagesSlice.actions;

// Selectors (assuming state structure is { pages: undoableState })
export const selectPresentState = (state) => state.pages.present;

export const getSelectedTool = (state) => selectPresentState(state).selectedTool;
export const getSelectedElements = (state) => selectPresentState(state).selectedElements;
export const getCurrentPageData = (state) => {
    const present = selectPresentState(state);
    // Add safety check for pages array existence
    return present.pages?.[present.currentPage] || null;
};
export const getCurrentPageProperty = (state, property) => {
  const currentPageData = getCurrentPageData(state);
  // Use optional chaining for safer access
  return currentPageData?.[property];
};
// Renamed for clarity
export const selectCurrentPageObject = (state) => getCurrentPageData(state);

export const selectAllPages = (state) => selectPresentState(state).pages;
export const selectCopiedElement = (state) => selectPresentState(state).copiedElement;
export const selectCopiedElements = (state) => selectPresentState(state).copiedElements;
export const getShowGuide = (state) => selectPresentState(state).showGuide;
export const getScale = (state) => selectPresentState(state).scale;
export const getEditingElementId = (state) => selectPresentState(state).editingElementId;


// Export the undoable reducer
export default undoablePagesReducer;
