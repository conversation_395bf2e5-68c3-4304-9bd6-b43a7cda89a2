import { useState, useEffect } from "react";

export const useFlipBookScale = (flipbookWidth, flipbookHeight, minScale = 0.5, maxScale = 1.5, margin = 40) => {
    const [scale, setScale] = useState(1);

    useEffect(() => {
        if (!flipbookWidth || !flipbookHeight) return;

        const handleResize = () => {
            const availableWidth = window.innerWidth - margin;
            const availableHeight = window.innerHeight - margin;
            // Choose the smaller ratio so that the flipbook fits entirely
            const computedScale = Math.min(availableWidth / flipbookWidth, availableHeight / flipbookHeight);
            const clampedScale = Math.max(minScale, Math.min(maxScale, computedScale));
            setScale(clampedScale);
        };

        handleResize();
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, [flipbookWidth, flipbookHeight, minScale, maxScale, margin]);

    return scale;
};
