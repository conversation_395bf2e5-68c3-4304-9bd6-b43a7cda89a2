import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { updateElement } from '../../store/pagesSlice';
import { toast } from 'sonner';
import { CheckCircle, XCircle } from 'lucide-react'; // Importing icons
import { useSelector } from 'react-redux';

const TrueFalseElement = ({ element }) => {
  const dispatch = useDispatch();
  const { id, content, style } = element;
  const { question, correctAnswer } = content;
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const currentReadingElementId = useSelector(state => state.ebook.currentReadingElementId);

  const handleAnswerChange = (answer) => {
    if (selectedAnswer !== null) return; // Prevent re-selection

    const answerBool = answer === 'true'; // Convert string to boolean
    setSelectedAnswer(answerBool);

    if (answerBool === correctAnswer) {
      toast.success('Correct answer!');
    } else {
      toast.error('Incorrect answer. Try again!');
    }

    dispatch(
      updateElement({
        id,
        updates: {
          content: {
            ...content,
            selectedAnswer: answerBool,
          },
        },
      })
    );
  };
  return (
    <div
      className={`
                ${currentReadingElementId === element.id ? 'border border-blue-500 p-6 border' : ''}
    `}
      style={{
        height: element.size.height,
        width: element.size.width,
        ...style,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
      }}
    >
      <div style={{ marginBottom: '1.5rem', width: '100%' }}>
        <p className="text-xl font-medium text-left">{question}</p>
      </div>
      <div style={{ display: 'flex', gap: '1rem', width: '100%' }}>
        {['true', 'false'].map((option) => (
          <div
            key={option}
            onClick={() => handleAnswerChange(option)}
            style={{
              padding: '0.5rem 1.5rem',
              borderRadius: '0.5rem',
              cursor: selectedAnswer === null ? 'pointer' : 'not-allowed',
              backgroundColor:
                selectedAnswer === (option === 'true')
                  ? selectedAnswer === correctAnswer
                    ? '#C8E6C9' // Light green for correct
                    : '#FFCDD2' // Light red for incorrect
                  : '#E0E0E0', // Gray for unselected
              color: '#000000',
              transition: 'background-color 0.3s ease',
              textAlign: 'center',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
            }}
          >
            {selectedAnswer === (option === 'true') ? (
              selectedAnswer === correctAnswer ? (
                <CheckCircle size={20} color="#4CAF50" />
              ) : (
                <XCircle size={20} color="#F44336" />
              )
            ) : null}
            {option.charAt(0).toUpperCase() + option.slice(1)}
          </div>
        ))}
      </div>
    </div>
  );
};

export default TrueFalseElement;
