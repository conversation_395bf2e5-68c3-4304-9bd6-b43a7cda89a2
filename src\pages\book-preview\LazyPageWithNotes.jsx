import React, { useEffect, useRef, useState } from "react";
import PdfPageWithNotes from "./components/PdfPageWithNotes";

export default function LazyPageWithNotes({
  pageNumber,
  containerWidth,  // not mandatory now, but you can keep it if used elsewhere
  ebookId,
  scale = 1,
  onPageClick
}) {
  const ref = useRef(null);
  const [inView, setInView] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        setInView(true);
        observer.unobserve(ref.current);
      }
    });
    if (ref.current) observer.observe(ref.current);
    return () => {
      if (ref.current) observer.unobserve(ref.current);
    };
  }, []);

  return (
    <div ref={ref}>
      {inView && (
        <PdfPageWithNotes
          pageNumber={pageNumber}
          ebookId={ebookId}
          scale={scale}    // pass the dynamic scale down
          onPageClick={onPageClick}
        />
      )}
    </div>
  );
}
