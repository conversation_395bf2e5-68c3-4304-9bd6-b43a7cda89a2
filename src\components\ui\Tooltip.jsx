import React, { useState } from "react";

/**
 * Custom Tooltip Component
 * @param {string} text - Tooltip text to display
 * @param {string} position - Tooltip position: "top", "bottom", "left", "right"
 * @param {ReactNode} children - The component that should trigger the tooltip
 */
const Tooltip = ({ text, position = "top", children }) => {
  const [visible, setVisible] = useState(false);

  return (
    <div
      className="relative inline-block group"
      onMouseEnter={() => setVisible(true)}
      onMouseLeave={() => setVisible(false)}
    >
      {/* Target Element */}
      {children}

      {/* Tooltip */}
      {visible && (
        <div
          className={`absolute whitespace-nowrap bg-black text-white text-xs px-2 py-1 rounded shadow-md transition-opacity duration-300
            ${position === "top" ? "bottom-full left-1/2 transform -translate-x-1/2 mb-2" : ""}
            ${position === "bottom" ? "top-full left-1/2 transform -translate-x-1/2 mt-2" : ""}
            ${position === "left" ? "right-full top-1/2 transform -translate-y-1/2 mr-2" : ""}
            ${position === "right" ? "left-full top-1/2 transform -translate-y-1/2 ml-2" : ""}
          `}
        >
          {text}
        </div>
      )}
    </div>
  );
};

export default Tooltip;
