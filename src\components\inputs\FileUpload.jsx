import { useField } from 'formik';
import { useDropzone } from 'react-dropzone';
import { X } from 'lucide-react';

export const FileUpload = ({ label, multiple, accept, ...props }) => {
  const [field, meta, helpers] = useField(props);

  const onDrop = (acceptedFiles) => {
    if (multiple) {
      helpers.setValue([...field.value, ...acceptedFiles]);
    } else {
      helpers.setValue(acceptedFiles[0]);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple,
    accept
  });

  const removeFile = (fileToRemove) => {
    if (multiple) {
      helpers.setValue(field.value.filter(file => file !== fileToRemove));
    } else {
      helpers.setValue(null);
    }
  };

  const renderFiles = () => {
    if (multiple && Array.isArray(field.value)) {
      return field.value.map((file, index) => (
        <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
          <span className="text-sm truncate">{file.name}</span>
          <button
            type="button"
            onClick={() => removeFile(file)}
            className="text-red-500 hover:text-red-700"
          >
            <X size={24} />
          </button>
        </div>
      ));
    } else if (!multiple && field.value) {
      return (
        <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
          <span className="text-sm truncate">{field.value.name || field.value}</span>
          <button
            type="button"
            onClick={() => removeFile(field.value)}
            className="text-red-500 hover:text-red-700"
          >
            <X size={24} />
          </button>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-left text-gray-700 dark:text-gray-200 mb-1">
        {label}
      </label>
      <div
        {...getRootProps()}
        className={`min-h-[150px] flex flex-col items-center justify-center border-2 border-dashed rounded-lg p-4 text-center cursor-pointer
          ${isDragActive ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 dark:border-gray-600'}`}
      >
        <input {...getInputProps()} />
        {isDragActive ? (
          <p className="text-sm text-gray-600 dark:text-gray-300">Drop the files here...</p>
        ) : (
          <p className="text-sm text-gray-600 dark:text-gray-300">
            Drag & drop files here, or click to select files
          </p>
        )}
      </div>
      <div className="mt-2 space-y-2">
        {renderFiles()}
      </div>
      {meta.touched && meta.error ? (
        <div className="text-red-600 dark:text-red-400 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};