import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { REDIRECT_URL } from '@/config';
import {
    setBrushColor,
    setBrushSize,
    setTool,
} from '../store/drawingSlice';
import {
    Brush,
    Square,
    Circle,
    Triangle,
    Type,
    Trash2,
    BookOpen,
    Layers,
    Home,
    ScrollText,
} from 'lucide-react';

const PdfToolbar = ({ clearDrawing }) => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const fromUrl = searchParams.get('from');

    const bookData = useSelector((state) => state.ebook?.ebookData);
    const noteTakingLayer = useSelector((state) => state.ebook.noteTakingLayer);
    const brushColor = useSelector((state) => state.drawing.brushColor);
    const brushSize = useSelector((state) => state.drawing.brushSize);
    const currentTool = useSelector((state) => state.drawing.tool);

    const handleColorChange = (e) => dispatch(setBrushColor(e.target.value));
    const handleSizeChange = (e) => dispatch(setBrushSize(parseInt(e.target.value, 10)));
    const handleToolChange = (tool) => dispatch(setTool(tool));

    const handleGoHome = () => {
        if(fromUrl === 'ecom' && REDIRECT_URL) {
          return window.location.href = `${REDIRECT_URL}` || 'https://yourepub.com'
        }
        navigate('/');
    };
    const handleSwitchView = () => {
        const currentPath = location.pathname;
        let newPath = "";
        if (currentPath.includes("/ebooks")) {
          newPath = currentPath.replace("/ebooks", "/flipbooks");
        } else if (currentPath.includes("/ebooks/")) {
          newPath = currentPath.replace("/ebooks/", "/flipbooks/");
        } else if (currentPath.includes("/flipbooks")) {
          newPath = currentPath.replace("/flipbooks", "/ebooks");
        } else if (currentPath.includes("/flipbooks/")) {
          newPath = currentPath.replace("/flipbooks/", "/ebooks/");
        }

        // Only preserve the token when it's present in the URL
        const token = searchParams.get('token');
        if (token) {
          // Add the token to the new path only if it exists in the URL
          navigate(`${newPath}?token=${token}`);
        } else {
          // No token in URL, just navigate normally
          navigate(newPath);
        }
      };

    return (
        <div className="fixed top-0 left-0 right-0 p-2 bg-gray-900 text-white z-[100]">
            <div className="max-w-[1280px] mx-auto flex justify-between items-center">

                {/* Book Title */}
                <div className="bg-green-100 px-3 py-1.5 rounded-full inline-flex items-center border border-green-300 shadow-sm">
                    <ScrollText size={16} className="text-green-700" />
                    <span className="pl-2 text-green-700 text-sm font-medium">{bookData?.title}</span>
                </div>

                {/* Toolbar Actions */}
                <div className="flex items-center gap-2">

                    {/* Note-Taking Tools */}
                    {noteTakingLayer ? (
                        <>
                            <label className="flex items-center">
                                <span className="mr-2 text-white">Color:</span>
                                <input
                                    type="color"
                                    value={brushColor}
                                    onChange={handleColorChange}
                                    className="ml-2 cursor-pointer"
                                    aria-label="Select brush color"
                                />
                            </label>
                            <label className="flex items-center">
                                <span className="mx-2 text-white">Size:</span>
                                <select
                                    value={brushSize}
                                    onChange={handleSizeChange}
                                    className="mx-2 px-2 py-1 bg-white border border-gray-200 rounded cursor-pointer"
                                    aria-label="Select brush size"
                                >
                                    {[1, 3, 5, 10, 20, 30, 40, 50].map((size) => (
                                        <option key={size} value={size}>{size}</option>
                                    ))}
                                </select>
                            </label>
                            <div className="tools flex items-center gap-2">
                                {[
                                    { tool: 'brush', Icon: Brush, label: 'Brush' },
                                    { tool: 'rectangle', Icon: Square, label: 'Rectangle' },
                                    { tool: 'circle', Icon: Circle, label: 'Circle' },
                                    { tool: 'triangle', Icon: Triangle, label: 'Triangle' },
                                    { tool: 'text', Icon: Type, label: 'Text' },
                                ].map(({ tool, Icon, label }) => (
                                    <button
                                        key={tool}
                                        onClick={() => handleToolChange(tool)}
                                        className={`p-2 rounded ${currentTool === tool ? 'bg-gray-100' : 'text-white'}`}
                                        aria-label={`${label} tool`}
                                    >
                                        <Icon size={20} />
                                    </button>
                                ))}
                            </div>
                            <button
                                onClick={clearDrawing}
                                className="p-2 ml-2 bg-red-500 text-white rounded"
                                aria-label="Clear drawing"
                            >
                                <Trash2 size={20} />
                            </button>
                        </>
                    ) : (
                        <>
                            {/* Navigation & View Switching */}
                            <button
                                onClick={handleGoHome}
                                className="p-1 bg-blue-500 rounded flex items-center"
                                aria-label="Go Home"
                            >
                                <Home size={20} />
                            </button>
                            <button
                                onClick={handleSwitchView}
                                className="p-1 bg-blue-500 text-white rounded flex items-center gap-2"
                                aria-label="Switch View"
                            >
                                {window.location.pathname.includes('/ebooks/') ? (
                                    <>
                                        <Layers size={20} />
                                        <span className='hidden md:inline'>Flipbook View</span>
                                    </>
                                ) : (
                                    <>
                                        <BookOpen size={20} />
                                        <span className='hidden md:inline'>Scroll View</span>
                                    </>
                                )}
                            </button></>
                    )}
                </div>
            </div>
        </div>
    );
};

export default PdfToolbar;
