{"trusted_by": "1000人以上の教育者から信頼されています", "future_of": "未来の", "digital_education": "デジタル教育", "create_stunning_ebooks": "学生の学習、エンゲージメント、情報保持の方法を変革する、素晴らしいインタラクティブeBookを作成します。", "try_ebook_builder": "Ebookビルダーを試す", "read_books": "本を読む", "books_created": "10,000冊以上の本を作成", "coppa_compliant": "COPPA準拠", "interactive_learning_platform": "インタラクティブ学習プラットフォーム", "interactive_content": "インタラクティブコンテンツ", "interactive_content_desc": "マルチメディア要素と連携", "smart_assessment": "スマートアセスメント", "smart_assessment_desc": "学習の進捗状況を追跡", "features": "機能", "how_it_works": "仕組み", "books": "書籍", "contact": "お問い合わせ", "bookSection.exploreLibrary": "デジタルライブラリを探索", "bookSection.browseDescription": "インタラクティブeBookのコレクションを閲覧し、新しい知識を発見してください", "bookSection.error": "問題が発生しました。後でもう一度お試しください。", "bookSection.showMore": "もっと見る", "home.transformContent": "コンテンツを変換", "home.engagingPublications": "魅力的なデジタル出版物を作成するために必要なすべて", "home.howItWorksTitle": "仕組み", "home.howItWorksDescription": "4つの簡単なステップでインタラクティブeBookを作成して公開する", "home.getInTouch": "お問い合わせ", "home.readyToStart": "作成を開始する準備はできましたか？", "home.joinPublishers": "インタラクティブコンテンツを作成している何千もの出版社に参加する", "home.startPublishing": "公開を開始", "home.features.interactiveEbooks.title": "インタラクティブeBook", "home.features.interactiveEbooks.description": "インタラクティブ要素とマルチメディアコンテンツを備えた魅力的なデジタルブックを作成する", "home.features.mcqAssessments.title": "MCQとアセスメント", "home.features.mcqAssessments.description": "学習成果を向上させるための包括的なクイズとアセスメントを構築する", "home.features.flashcards.title": "フラッシュカード", "home.features.flashcards.description": "効果的な暗記と復習のための動的フラッシュカードを設計する", "home.features.accessibility.title": "アクセシビリティ", "home.features.accessibility.description": "組み込みのアクセシビリティ機能を使用して、すべての人がコンテンツにアクセスできるようにする", "home.features.richMedia.title": "リッチメディア", "home.features.richMedia.description": "没入型の学習体験を作成するために、ビデオやインタラクティブメディアを埋め込む", "home.features.publisherTools.title": "パブリッシャーツール", "home.features.publisherTools.description": "コンテンツ配信と管理のためのプロフェッショナルな出版ツール", "home.howItWorks.step1.title": "コンテンツを作成する", "home.howItWorks.step1.description": "既存のコンテンツから始めるか、直感的なエディターを使用して新しい資料を作成する", "home.howItWorks.step2.title": "インタラクティブ性を追加する", "home.howItWorks.step2.description": "クイズ、フラッシュカード、マルチメディア要素でコンテンツを強化する", "home.howItWorks.step3.title": "デザインをカスタマイズする", "home.howItWorks.step3.description": "美しいテンプレートから選択するか、独自のカスタムデザインを作成する", "home.howItWorks.step4.title": "公開して共有する", "home.howItWorks.step4.description": "インタラクティブeBookを公開して、視聴者と共有する", "home.sendMessage": "メッセージを送信", "footer.product": "製品", "footer.features": "機能", "footer.howItWorks": "仕組み", "footer.pricing": "価格", "footer.faq": "FAQ", "footer.company": "会社", "footer.about": "私たちについて", "footer.blog": "ブログ", "footer.careers": "採用情報", "footer.contact": "お問い合わせ", "footer.resources": "リソース", "footer.documentation": "ドキュメント", "footer.templates": "テンプレート", "footer.examples": "例", "footer.support": "サポート", "footer.legal": "法律", "footer.privacy": "プライバシー", "footer.terms": "利用規約", "footer.security": "セキュリティ", "footer.allRightsReserved": "All rights reserved.", "authorForm.name": "名前", "authorForm.nameRequired": "名前は必須です", "authorForm.status": "ステータス", "authorForm.statusRequired": "ステータスは必須です", "authorForm.active": "アクティブ", "authorForm.inactive": "非アクティブ", "authorForm.uploadPhoto": "写真をアップロード", "authorForm.unsupportedFileFormatOrSize": "サポートされていないファイル形式またはサイズ", "authorForm.photoRequired": "写真が必要です", "authorForm.unsupportedFileFormat": "サポートされていないファイル形式", "authorForm.fileSizeTooLarge": "ファイルサイズが大きすぎます", "authorForm.bio": "バイオ", "authorForm.bioRequired": "バイオが必要です", "authorForm.updateAuthor": "著者を更新", "authorForm.addAuthor": "著者を追加", "authors.authorsList": "著者リスト", "authors.name": "名前", "authors.photo": "写真", "authors.status": "ステータス", "authors.active": "アクティブ", "authors.inactive": "非アクティブ", "authors.action": "アクション", "authors.addAuthor": "著者を追加", "authors.editAuthor": "著者を編集", "inquiries.title": "お問い合わせ", "inquiries.name": "名前", "inquiries.email": "メール", "inquiries.subject": "件名", "inquiries.status": "ステータス", "inquiries.createdAt": "作成日", "inquiries.action": "アクション", "inquiries.modalTitle": "お問い合わせ詳細", "genreForm.name": "名前", "genreForm.status": "ステータス", "genreForm.active": "アクティブ", "genreForm.inactive": "非アクティブ", "genreForm.uploadThumbnail": "サムネイルをアップロード", "genreForm.addGenre": "カテゴリーを追加", "genreForm.updateGenre": "ジャンルを更新", "genreForm.validation.nameRequired": "名前は必須です", "genreForm.validation.statusRequired": "ステータスは必須です", "genreForm.validation.thumbnailRequired": "サムネイルは必須です", "genreForm.validation.unsupportedFormat": "対応していないファイル形式", "genreForm.validation.fileTooLarge": "ファイルサイズが大きすぎます", "genreForm.validation.fileInvalid": "無効なファイル形式またはサイズ", "genresPage.title": "ジャンルリスト", "genresPage.name": "名前", "genresPage.image": "画像", "genresPage.status": "ステータス", "genresPage.active": "アクティブ", "genresPage.inactive": "非アクティブ", "genresPage.action": "アクション", "genresPage.addGenre": "ジャンルを追加", "genresPage.editGenre": "ジャンルを編集", "genresPage.submitError": "フォーム送信エラー", "genresPage.deleteError": "ジャンルの削除エラー", "userForm.name": "名前", "userForm.email": "メール", "userForm.password": "パスワード", "userForm.role": "役割", "userForm.isActive": "アクティブ?", "userForm.profilePicture": "プロフィール画像", "userForm.active": "アクティブ", "userForm.inactive": "非アクティブ", "userForm.admin": "管理者", "userForm.author": "著者", "userForm.user": "ユーザー", "userForm.createUser": "ユーザーを作成", "userForm.updateUser": "ユーザーを更新", "userForm.validation.nameRequired": "名前は必須です", "userForm.validation.emailRequired": "メールは必須です", "userForm.validation.invalidEmail": "無効なメールアドレス", "userForm.validation.passwordRequired": "パスワードは必須です", "userForm.validation.passwordMin": "パスワードは6文字以上である必要があります", "userForm.validation.roleRequired": "役割は必須です", "userForm.submitError": "フォーム送信エラー", "usersList.title": "ユーザーリスト", "usersList.name": "名前", "usersList.email": "メール", "usersList.role": "役割", "usersList.status": "ステータス", "usersList.active": "アクティブ", "usersList.inactive": "非アクティブ", "usersList.action": "アクション", "usersList.addUser": "ユーザーを追加", "usersList.editUser": "ユーザーを編集", "usersList.deleteError": "ユーザー削除エラー", "userView.loading": "読み込み中...", "userView.user": "ユーザー", "userView.backToList": "ユーザーリストに戻る", "userView.name": "名前", "userView.email": "メール", "userView.role": "役割", "userView.profilePicture": "プロフィール画像", "userView.status": "ステータス", "userView.active": "アクティブ", "userView.inactive": "非アクティブ", "accessDenied.title": "アクセス拒否", "accessDenied.message": "このページにアクセスする権限がありません。", "accessDenied.goHome": "ホームページへ", "profile.title": "プロフィール設定", "profile.profilePicture": "プロフィール画像", "profile.uploadProfilePicture": "プロフィール画像をアップロード", "profile.fullName": "フルネーム", "profile.currentPassword": "現在のパスワード", "profile.newPassword": "新しいパスワード", "profile.confirmNewPassword": "新しいパスワードの確認", "profile.role": "役割", "profile.saving": "保存中...", "profile.saveChanges": "変更を保存", "profile.updateSuccess": "プロフィールが正常に更新されました！", "profile.updateFail": "プロフィールの更新に失敗しました。もう一度お試しください。", "profile.updateError": "プロフィールの更新中にエラーが発生しました", "profile.validation.nameRequired": "フルネームが必要です", "profile.validation.passwordMin": "パスワードは6文字以上である必要があります", "profile.validation.newPasswordRequired": "新しいパスワードが必要です", "profile.validation.confirmPasswordRequired": "パスワードの確認が必要です", "profile.validation.passwordMismatch": "パスワードが一致しません", "profile.mobile": "携帯電話", "profile.bio": "略歴", "profile.address": "住所", "security.title": "セキュリティ設定", "security.changePassword": "パスワードを変更", "security.currentPassword": "現在のパスワード", "security.newPassword": "新しいパスワード", "security.confirmNewPassword": "新しいパスワードを確認", "security.updating": "更新中...", "security.updatePassword": "パスワードを更新", "security.updateSuccess": "パスワードが正常に更新されました！", "security.updateError": "パスワードの更新エラー", "security.validation.required": "必須", "security.validation.passwordMin": "パスワードは8文字以上である必要があります", "security.validation.passwordMismatch": "パスワードが一致しません", "dashboard.title": "ダッシュボード概要", "dashboard.totalUnpublishedBooks": "未公開の本の合計", "dashboard.totalPublishedBooks": "公開された本の合計", "dashboard.totalEbooks": "電子書籍の合計", "dashboard.totalGenres": "ジャンルの合計", "dashboard.recentEbooks": "最近の電子書籍", "dashboard.pendingEbooks": "保留中の電子書籍", "dashboard.titleColumn": "タイトル", "dashboard.authorColumn": "著者", "dashboard.genreColumn": "ジャンル", "dashboard.loading": "読み込み中...", "dashboard.noData": "データがありません", "Dashboard": "ダッシュボード", "Genres": "ジャンル", "eBook": "電子書籍", "Templates": "テンプレート", "Users": "ユーザー", "Inquiries": "お問い合わせ", "Authors": "著者", "Settings": "設定", "Profile": "プロフィール", "Security": "セキュリティ", "Logout": "ログアウト", "Coupons": "クーポン", "home.pricing.title": "シンプルで透明な料金プラン", "home.pricing.subtitle": "出版ニーズに合わせた最適なプランをお選びください", "home.pricing.popular": "人気", "home.pricing.basic.name": "ベーシック", "home.pricing.basic.price": "¥3,900", "home.pricing.basic.description": "インタラクティブな電子書籍を始めるのに最適", "home.pricing.basic.features.ebooks": "電子書籍5冊まで", "home.pricing.basic.features.storage": "5GBストレージ", "home.pricing.basic.features.support": "メールサポート", "home.pricing.basic.features.analytics": "基本的な分析機能", "home.pricing.basic.cta": "始める", "home.pricing.pro.name": "プロフェッショナル", "home.pricing.pro.price": "¥11,900", "home.pricing.pro.description": "プロフェッショナルな出版に必要なすべての機能", "home.pricing.pro.features.unlimited": "無制限の電子書籍", "home.pricing.pro.features.advanced": "高度なインタラクティブ機能", "home.pricing.pro.features.priority": "優先サポート", "home.pricing.pro.features.customization": "カスタムブランディング", "home.pricing.pro.features.collaboration": "チームコラボレーション", "home.pricing.pro.cta": "無料トライアルを開始", "home.pricing.enterprise.name": "エンタープライズ", "home.pricing.enterprise.price": "カスタム", "home.pricing.enterprise.description": "大規模組織向けのカスタムソリューション", "home.pricing.enterprise.features.custom": "カスタムソリューション", "home.pricing.enterprise.features.dedicated": "専任サポート", "home.pricing.enterprise.features.sla": "SLA保証", "home.pricing.enterprise.features.api": "API利用", "home.pricing.enterprise.features.training": "チームトレーニング", "home.pricing.enterprise.cta": "営業に問い合わせ", "pricing": "料金", "couponsPage.title": "クーポンリスト", "couponsPage.code": "コード", "couponsPage.discountType": "割引タイプ", "couponsPage.appliesTo": "適用対象", "couponsPage.validPeriod": "有効期間", "couponsPage.status": "ステータス", "couponsPage.action": "アクション", "couponsPage.active": "有効", "couponsPage.inactive": "無効", "couponsPage.addCoupon": "クーポンを追加", "couponsPage.editCoupon": "クーポンを編集", "couponsPage.item": "アイテム", "couponsPage.submitError": "クーポン送信エラー", "couponsPage.deleteError": "クーポン削除エラー", "couponForm.code": "クーポンコード", "couponForm.discountType": "割引タイプ", "couponForm.percentage": "パーセンテージ", "couponForm.fixed": "固定金額", "couponForm.discountValue": "割引額", "couponForm.appliesTo": "適用対象", "couponForm.entireOrder": "全注文", "couponForm.specificItem": "特定アイテム", "couponForm.itemId": "アイテムID", "couponForm.minOrderAmount": "最小注文金額", "couponForm.maxDiscount": "最大割引額", "couponForm.usageLimit": "使用制限", "couponForm.startDate": "開始日", "couponForm.endDate": "終了日", "couponForm.status": "ステータス", "couponForm.active": "有効", "couponForm.inactive": "無効", "couponForm.addCoupon": "クーポンを追加", "couponForm.updateCoupon": "クーポンを更新", "couponForm.validation.codeRequired": "クーポンコードが必要です", "couponForm.validation.invalidDiscountType": "無効な割引タイプです", "couponForm.validation.discountTypeRequired": "割引タイプが必要です", "couponForm.validation.discountValueRequired": "割引額が必要です", "couponForm.validation.minDiscount": "割引は0以上である必要があります", "couponForm.validation.appliesToRequired": "適用対象が必要です", "couponForm.validation.invalidAppliesTo": "無効な適用対象値です", "couponForm.validation.itemIdRequired": "アイテム固有のクーポンにはアイテムIDが必要です", "couponForm.validation.minOrderAmount": "最小注文金額は0以上である必要があります", "couponForm.validation.minOrderAmountRequired": "最小注文金額が必要です", "couponForm.validation.minMaxDiscount": "最大割引額は0以上である必要があります", "couponForm.validation.maxDiscountRequired": "最大割引額が必要です", "couponForm.validation.minUsageLimit": "使用制限は0以上である必要があります", "couponForm.validation.usageLimitRequired": "使用制限が必要です", "couponForm.validation.startDateRequired": "開始日が必要です", "couponForm.validation.endDateRequired": "終了日が必要です", "couponForm.validation.endDateAfterStart": "終了日は開始日より後である必要があります", "couponForm.validation.invalidStatus": "無効なステータスです", "couponForm.validation.statusRequired": "ステータスが必要です", "publishers.name": "名前", "publishers.mobile": "携帯電話", "publishers.email": "メール", "publishers.tradeLicense": "取引ライセンス番号", "publishers.status": "ステータス", "publishers.active": "アクティブ", "publishers.inactive": "非アクティブ", "publishers.action": "アクション", "publishers.editPublisher": "出版社を編集", "publishers.addPublisher": "出版社を追加", "publishers.deleteError": "出版社の削除に失敗しました。", "publishers.submitError": "フォームの送信に失敗しました。", "publishers.title": "出版社リスト", "publishers.form.name": "名前", "publishers.form.mobile": "携帯", "publishers.form.nid": "個人識別番号", "publishers.form.nidImage": "個人識別番号の画像", "publishers.form.proprietorPhoto": "所有者の写真", "publishers.form.proprietorSignature": "所有者の署名", "publishers.form.presentAddress": "現在の住所", "publishers.form.permanentAddress": "永久住所", "publishers.form.officePresentAddress": "オフィスの現在の住所", "publishers.form.tradeLicenseNumber": "営業許可番号", "publishers.form.tradeLicenseImage": "営業許可証の画像", "publishers.form.logo": "ロゴ", "publishers.form.etin": "電子税識別番号", "publishers.form.vatNumber": "消費税番号", "publishers.form.officeMobile": "オフィスの携帯電話", "publishers.form.officePhone": "オフィスの電話", "publishers.form.officeEmail": "オフィスのEメール", "publishers.form.officePermanentAddress": "オフィスの永久住所", "publishers.form.cancel": "キャンセル", "publishers.form.add": "追加", "publishers.form.edit": "編集", "publishers.form.email": "メール", "publishers.form.password": "パスワード", "publishers.form.confirmPassword": "パスワード確認", "publishers.form.publisherLogin": "出版社ログイン", "publishers.form.passwordMin": "パスワードは8文字以上である必要があります", "publishers.form.passwordRequired": "パスワードは必須です", "publishers.form.confirmPasswordRequired": "パスワード確認は必須です", "publishers.form.passwordMismatch": "パスワードが一致しません", "category.list": "カテゴリリスト", "static.page": "静的ページリスト", "static.addStaticPage": "静的ページの追加", "title": "タイトル", "staticForm.content": "コンテンツ", "staticForm.slug": "Slug", "static.editStatic": "静的ページを更新する", "update": "アップデート", "pending": "保留中", "approved": "承認済み", "rejected": "却下", "approval.status": "承認ステータス", "publishers.form.emailRequired": "メールアドレスは必須です", "publishers.form.nameRequired": "名前が必要です", "publishers.form.invalidEmail": "無効なメールアドレス形式", "Manage eBooks": "eBookを管理する", "Publishers": "出版社", "Categories": "カテゴリ", "Orders": "注文", "Search eBooks": "eBookを検索する", "Author": "著者", "Publisher": "出版社", "Book Type": "本の種類", "Status": "ステータス", "Approval Status": "承認ステータス", "ebook.title": "電子書籍", "ebook.modal.title.view": "本の基本情報", "ebook.modal.title.edit": "本の基本情報を編集", "ebook.modal.title.create": "新しい本を作成", "ebook.button.tooltip.view": "本を表示", "ebook.button.tooltip.viewInfo": "基本情報を表示", "ebook.button.tooltip.editInfo": "基本情報を編集", "ebook.button.tooltip.design": "エディターでデザイン", "ebook.button.tooltip.template": "テンプレートとして保存", "ebook.button.tooltip.delete": "本を削除", "ebook.delete.confirm.title": "この電子書籍を削除してもよろしいですか？", "ebook.delete.confirm.text": "この電子書籍は", "ebook.delete.confirm.description": "完全に削除されます。", "ebook.delete.confirm.cancel": "キャンセル", "ebook.delete.confirm.confirm": "削除", "ebook.modal.editTitle": "本の基本情報を編集", "ebook.modal.createTitle": "新しい本を作成", "ebook.form.fields.title": "タイトル", "ebook.form.fields.authors": "著者を選択", "ebook.form.fields.description": "説明", "ebook.form.fields.categories": "カテゴリーを選択", "ebook.form.fields.status": "ステータス", "ebook.form.fields.status.options.draft": "下書き", "ebook.form.fields.status.options.published": "公開済み", "ebook.form.fields.bookType": "ブックタイプ", "ebook.form.fields.bookType.options.ebook": "電子書籍", "ebook.form.fields.bookType.options.pdf": "PDF", "ebook.form.fields.pageDimensions": "ページサイズ", "ebook.form.fields.orientation": "向き", "ebook.form.fields.orientation.options.portrait": "縦", "ebook.form.fields.orientation.options.landscape": "横", "ebook.form.fields.pdfFile": "PDFファイル", "ebook.form.fields.coverImage": "表紙画像", "ebook.form.fields.buttons.update": "電子書籍を更新", "ebook.form.fields.buttons.submit": "電子書籍を登録", "ebook.form.fields.price": "価格", "ebook.form.fields.discount": "割引", "ebook.form.fields.finalPrice": "最終価格", "ebook.form.fields.pageDimension": "ページサイズ", "fileInput.dragActive": "ここにファイルをドロップ...", "fileInput.dragInactive": "ドラッグ＆ドロップまたはクリックして選択", "ebook.button.create": "本を作成"}