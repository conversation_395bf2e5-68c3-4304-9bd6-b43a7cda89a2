import React, { useState, useEffect } from "react";
import Select from "react-select";
import { useField, useFormikContext } from "formik";
import api from "@/lib/axios";

const SearchableSelect = ({
  name,
  label,
  endpoint,
  dataKey = "data.data", // Path to data array inside response
  labelKey = "title", // Key for label
  valueKey = "id", // Key for value
  isMulti = false,
}) => {
  const { setFieldValue } = useFormikContext();
  const [field, meta] = useField(name);
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);

  const fetchOptions = async (searchQuery = "") => {
    setLoading(true);
    try {
      const response = await api.get(`${endpoint}?search=${searchQuery}`);
      const data = dataKey.split(".").reduce((o, i) => (o ? o[i] : []), response.data);
      const formattedOptions = data.map((item) => ({
        value: item[valueKey],
        label: item[labelKey],
      }));
      setOptions(formattedOptions);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOptions();
  }, [endpoint]);

  return (
    <div>
      {label && <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>}
      <Select
        isMulti={isMulti}
        options={options}
        isLoading={loading}
        placeholder="Search and select..."
        value={options.find(option => option.value === field.value) || null}
        onChange={(selectedOption) => setFieldValue(name, selectedOption ? selectedOption.value : "")}
        isSearchable
        onInputChange={(inputValue) => {
          if (inputValue.length > 2) {
            fetchOptions(inputValue);
          }
        }}
      />
      {meta.touched && meta.error ? <div className="text-red-500 text-sm">{meta.error}</div> : null}
    </div>
  );
};

export default SearchableSelect;