import React, { useState, useRef, useEffect } from "react";
import { Icon } from "@iconify/react";

const CustomVideoPlayer = ({ src, thumbnail, miniView }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [showControls, setShowControls] = useState(false);
  const videoRef = useRef(null);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.volume = volume;
    }
  }, [volume]);

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
  };

  const handlePlayPause = () => {
    if (videoRef.current.paused) {
      videoRef.current.play();
      setIsPlaying(true);
    } else {
      videoRef.current.pause();
      setIsPlaying(false);
    }
  };

  const handleTimeUpdate = () => {
    setCurrentTime(videoRef.current.currentTime);
  };

  const handleLoadedMetadata = () => {
    setDuration(videoRef.current.duration);
  };

  const handleSeek = (e) => {
    const seekTime = (e.nativeEvent.offsetX / e.target.offsetWidth) * duration;
    videoRef.current.currentTime = seekTime;
    setCurrentTime(seekTime);
  };

  const handleVolumeChange = (e) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    videoRef.current.volume = newVolume;
  };

  return (
    <div
      className="relative w-full h-full"
      onMouseEnter={() => setShowControls(true)}
      onMouseLeave={() => setShowControls(false)}
    >
      {/* Video with thumbnail */}
      <video
        muted={miniView}
        ref={videoRef}
        className="w-full h-full"
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onClick={handlePlayPause}
      >
        <source src={src} type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {/* Thumbnail and play button overlay (shown when video is not playing) */}
      {!isPlaying && (
        <div
          className="absolute top-0 left-0 w-full h-full flex items-center justify-center bg-black bg-opacity-40 cursor-pointer"
          onClick={handlePlayPause}
        >
          {thumbnail && (
            <img
              src={thumbnail}
              alt="Video thumbnail"
              className="absolute top-0 left-0 w-full h-full object-cover"
            />
          )}
          <div className="absolute inset-0 flex items-center justify-center">
            <button className="w-16 h-16 bg-white bg-opacity-80 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-opacity">
              <Icon icon="mdi:play" className="text-4xl text-blue-500" />
            </button>
          </div>
        </div>
      )}

      {/* Custom controls (shown on hover or when video is playing) */}
      {(showControls || isPlaying) && (
        <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 p-2 transition-opacity">
          {/* Progress bar */}
          <div
            className="w-full h-1 bg-gray-600 cursor-pointer mb-2"
            onClick={handleSeek}
          >
            <div
              className="h-full bg-blue-500"
              style={{ width: `${(currentTime / duration) * 100}%` }}
            />
          </div>

          <div className="flex items-center gap-2">
            {/* Play/Pause button */}
            <button
              onClick={handlePlayPause}
              className="text-white hover:text-blue-500"
            >
              <Icon
                icon={isPlaying ? "mdi:pause" : "mdi:play"}
                className="text-xl"
              />
            </button>

            {/* Time display */}
            <span className="text-white text-sm">
              {formatTime(currentTime)} / {formatTime(duration)}
            </span>

            {/* Volume control */}
            <div className="flex items-center gap-1 ml-auto">
              <Icon
                icon={volume === 0 ? "mdi:volume-off" : "mdi:volume-high"}
                className="text-white text-xl"
              />
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={handleVolumeChange}
                className="w-20"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomVideoPlayer;
