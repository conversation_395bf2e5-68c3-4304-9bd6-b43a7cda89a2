// src/store/drawingSlice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  brushColor: '#FFFF00', // Yellow color
  brushSize: 5,
  tool: 'brush',
  drawings: {},
  selectedId: null,
};


const drawingSlice = createSlice({
  name: 'drawing',
  initialState,
  reducers: {
    setBrushColor: (state, action) => {
      state.brushColor = action.payload;
    },
    setBrushSize: (state, action) => {
      state.brushSize = action.payload;
    },
    setTool: (state, action) => {
      state.tool = action.payload;
    },
    // Single-shape approach
    setDrawingData: (state, action) => {
      const { ebookId, pageId, shape } = action.payload;
      if (!state.drawings[ebookId]) {
        state.drawings[ebookId] = {};
      }
      if (!state.drawings[ebookId][pageId]) {
        state.drawings[ebookId][pageId] = [];
      }
      state.drawings[ebookId][pageId].push(shape);
    },
    // New bulk approach: replaces all pages' shapes at once
    setAllDrawings: (state, action) => {
      const { ebookId, structuredData } = action.payload;
      /*
        structuredData example:
        {
          101: [ { ...shape1 }, { ...shape2 } ],
          102: [ { ...shape1 } ],
          ...
        }
      */
      state.drawings[ebookId] = structuredData; // Overwrite all pages for this ebookId
    },
    updateShape: (state, action) => {
      const { ebookId, pageId, id, attrs } = action.payload;
      if (state.drawings[ebookId] && state.drawings[ebookId][pageId]) {
        const shape = state.drawings[ebookId][pageId].find((s) => s.id === id);
        if (shape) {
          Object.assign(shape, attrs);
        }
      }
    },
    clearDrawingData: (state, action) => {
      const { ebookId, pageId } = action.payload;
      if (state.drawings[ebookId] && state.drawings[ebookId][pageId]) {
        state.drawings[ebookId][pageId] = [];
      }
    },
    clearAllDrawings: (state, action) => {
      const { ebookId } = action.payload;
      if (state.drawings[ebookId]) {
        state.drawings[ebookId] = {};
      }
    },
    deleteShape: (state, action) => {
      const { ebookId, pageId, shapeId } = action.payload;
      if (state.drawings[ebookId] && state.drawings[ebookId][pageId]) {
        state.drawings[ebookId][pageId] = state.drawings[ebookId][pageId].filter(
          (shape) => shape.id !== shapeId
        );
      }
    },
    setSelectedId: (state, action) => {
      state.selectedId = action.payload;
    },
    clearSelectedId: (state) => {
      state.selectedId = null;
    },
    undoLastShape: (state, action) => {
      const { ebookId, pageId } = action.payload;
      if (state.drawings[ebookId] &&
        state.drawings[ebookId][pageId] &&
        state.drawings[ebookId][pageId].length > 0
      ) {
        state.drawings[ebookId][pageId].pop();
      }
    }
  },
});

export const {
  setBrushColor,
  setBrushSize,
  setTool,
  setDrawingData,
  setAllDrawings,
  updateShape,
  clearDrawingData,
  clearAllDrawings,
  setSelectedId,
  clearSelectedId,
  deleteShape,
  undoLastShape
} = drawingSlice.actions;

export default drawingSlice.reducer;
