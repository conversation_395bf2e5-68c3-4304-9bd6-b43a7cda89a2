import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { replace, useNavigate, useParams, useSearchParams, useLocation } from 'react-router-dom';
import {
  clearAllDrawings,
  clearDrawingData,
  deleteShape,
  setBrushColor,
  setBrushSize,
  setSelectedId,
  setTool,
  undoLastShape,
} from '../store/drawingSlice';
import { Brush, Square, Circle, Triangle, Type, Trash2, BookOpen, Layers, Home, ScrollText, Eraser, RotateCcw } from 'lucide-react';
import { REDIRECT_URL } from '@/config';

const Toolbar = ({ clickedPageId, ebook }) => {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const fromUrl = searchParams.get('from');
  const dispatch = useDispatch();
  const selectedId = useSelector((state) => state.drawing.selectedId);
  const brushColor = useSelector((state) => state.drawing.brushColor);
  const brushSize = useSelector((state) => state.drawing.brushSize);
  const currentTool = useSelector((state) => state.drawing.tool);
  const data = useSelector((state) => state.ebook?.ebookData);
  const noteTakingLayer = useSelector((state) => state.ebook.noteTakingLayer);
  const navigate = useNavigate();
  const isTemplatePreview = location.pathname.includes("templates");
  const handleDeleteShape = () => {
    if (!selectedId) return;
    dispatch(deleteShape({ ebookId: id, pageId: clickedPageId, shapeId: selectedId }));
    dispatch(setSelectedId(null));
  };


  const handleClearDrawing = () => {
    dispatch(clearAllDrawings({ ebookId: id }));
  }

  const handleClearSelectedPage = () => {
    dispatch(clearDrawingData({ ebookId: id, pageId: clickedPageId }));
  }


  const handleColorChange = (e) => {
    dispatch(setBrushColor(e.target.value));
  };

  const handleSizeChange = (e) => {
    dispatch(setBrushSize(parseInt(e.target.value, 10)));
  };

  const handleToolChange = (tool) => {
    dispatch(setTool(tool));
  };

  const handleGoHome = () => {
    if(fromUrl === 'ecom' && REDIRECT_URL) {
      return window.location.href = `${REDIRECT_URL}` || 'https://yourepub.com'
    }
    navigate('/');
  };

  const handleSwitchView = () => {
    const currentPath = location.pathname;
    let newPath = "";
    if (currentPath.includes("/ebooks")) {
      newPath = currentPath.replace("/ebooks", "/flipbooks");
    } else if (currentPath.includes("/ebooks/")) {
      newPath = currentPath.replace("/ebooks/", "/flipbooks/");
    } else if (currentPath.includes("/flipbooks")) {
      newPath = currentPath.replace("/flipbooks", "/ebooks");
    } else if (currentPath.includes("/flipbooks/")) {
      newPath = currentPath.replace("/flipbooks/", "/ebooks/");
    }

    // Only preserve the token when it's present in the URL
    const token = searchParams.get('token');
    if (token) {
      // Add the token to the new path only if it exists in the URL
      navigate(`${newPath}?token=${token}`);
    } else {
      // No token in URL, just navigate normally
      navigate(newPath);
    }
  };


  const handleUndo = () => {
    dispatch(undoLastShape({ ebookId: id, pageId: clickedPageId }));
  };


  return (
    <div className="toolbar bg-gray-900 fixed top-0 left-0 right-0 z-10">
      {/* Book Title */}
      <div className='flex justify-between items-center max-w-[1280px] mx-auto gap-4 p-2 text-white'>
        <div className="bg-green-100 px-3 py-1.5 rounded-full inline-flex items-center border border-green-300 shadow-sm">
          <ScrollText size={16} className="text-green-700" />
          {isTemplatePreview ? (
            <span className="pl-2 text-green-700 text-sm font-medium">{ebook?.title}</span>
          ) : (
            <span className="pl-2 text-green-700 text-sm font-medium">{data.title}</span>
          )}
        </div>
        <div className="flex items-center">
          {noteTakingLayer ? (
            <>
              {/* Note-Taking Tools */}
              <label className="flex items-center">
                <span className="mr-2 text-white">Color:</span>
                <input
                  type="color"
                  value={brushColor}
                  onChange={handleColorChange}
                  className="ml-2 cursor-pointer"
                  aria-label="Select brush color"
                  title="Select brush color"
                />
              </label>
              <label className="flex items-center">
                <span className="mx-2 text-white">Size:</span>
                <select
                  value={brushSize}
                  onChange={handleSizeChange}
                  className="mx-2 px-2 py-1 bg-blue-500 rounded cursor-pointer focus:outline-none focus:right-0"
                  aria-label="Select brush size"
                  title="Select brush size"
                >
                  {[1, 3, 5, 10, 20, 30, 40, 50].map((size) => (
                    <option key={size} value={size} className='bg-blue-400 text-white'>
                      {size}
                    </option>
                  ))}
                </select>
              </label>
              <div className="tools flex items-center gap-2">
                {[
                  { tool: 'brush', Icon: Brush, label: 'Brush' },
                  { tool: 'rectangle', Icon: Square, label: 'Rectangle' },
                  { tool: 'circle', Icon: Circle, label: 'Circle' },
                  { tool: 'triangle', Icon: Triangle, label: 'Triangle' },
                  { tool: 'text', Icon: Type, label: 'Text' },
                  { tool: 'eraser', Icon: Eraser, label: 'Eraser' },
                ].map(({ tool, Icon, label }) => (
                  <button
                    key={tool}
                    onClick={() => handleToolChange(tool)}
                    className={`p-2 rounded ${currentTool === tool ? 'bg-blue-500' : 'bg-gray-200 text-black'
                      }`}
                    aria-label={`${label} tool`}
                    title={label}
                  >
                    <Icon size={20} />
                  </button>
                ))}
              </div>
              {/* <button
                onClick={handleClearSelectedPage}
                className="p-2 ml-2 bg-red-500 text-white rounded"
                aria-label="Clear drawing"
                title="Clear"
              >
                <Eraser size={20} />
              </button> */}
              {/* <button
                onClick={handleUndo}
                className="p-2 bg-yellow-500 text-white rounded ml-2"
                aria-label="Undo last shape"
                title="Undo last shape"
              >
                <RotateCcw size={20} />
              </button> */}
              <button
                onClick={handleDeleteShape}
                className="p-2 ml-2 bg-red-500 text-white rounded"
                aria-label="Clear drawing"
                title="Clear"
              >
                <Trash2 size={20} />
              </button>
            </>
          ) : (
            <div className="flex items-center gap-2">
              <button
                onClick={handleGoHome}
                className="p-1 bg-blue-500 rounded flex items-center"
                aria-label="Go Back"
                title="Go Back"
              >
                <Home size={20} />
              </button>
              <button
                onClick={handleSwitchView}
                className="p-1 bg-blue-500 text-white rounded flex items-center gap-2"
                aria-label="Switch view"
                title={
                  location.pathname.includes('/ebooks/')
                    ? 'Flipbook view'
                    : 'Scroll view'
                }
              >
                {location.pathname.includes('/ebooks/') ? (
                  <>
                    <Layers size={20} />
                    <span className='hidden md:inline'>Flipbook View</span>
                  </>
                ) : (
                  <>
                    <BookOpen size={20} />
                    <span className='hidden md:inline'>Scroll View</span>
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
export default Toolbar;

