import Swal from "sweetalert2";


const Confirm = (handleOk,
  customMessage = "Do you want to delete?",
  customTopicName = "This data",
  customDescription = 'will be deleted permanently.',
  customBack = 'Cancel',
  customAction = 'Delete',
) => {

  Swal.fire({
    html: `
      <div class="bg-white rounded-lg">
        <div class="text-start items-center flex bg-valencia-500 justify-between">
          <h6 class="font-small mt-3 px-3 text-start text-gray-600  mb-4" style="font-size: 16px;">${customMessage}</h6>
          <button id="cancel-button" class="text-base px-3 text-white">X</button>
        </div>
        <p class="text-gray-600 mb-4 text-start px-3 mt-3" style="font-size: 16px;">${customTopicName} ${customDescription}</p>
        <div class="flex justify-end mr-5">
          <button id="cancel-buttonn" class="bg-[#9C9B9B] mr-2 text-base text-white px-4 py-2 rounded">${customBack}</button>
          <button id="confirm-button" class="bg-[#DC4C64] text-base text-white px-4 py-2 mr-2 rounded">${customAction}</button>
        </div>
      </div>
    `,
    showConfirmButton: false, // Hide default confirm button
  });

  document.getElementById("confirm-button").addEventListener("click", () => {
    handleOk();
    Swal.close();
  });

  document.getElementById("cancel-buttonn").addEventListener("click", () => {
    Swal.close(); // Close the modal
  });
  document.getElementById("cancel-button").addEventListener("click", () => {
    Swal.close(); // Close the modal
  });
};

export default Confirm;
