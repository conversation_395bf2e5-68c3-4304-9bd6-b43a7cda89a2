import { useSelector } from "react-redux";
import { Navigate, useLocation } from "react-router-dom";

const ProtectedRoute = ({ element: Element, allowedRoles, ...rest }) => {
  const { isAuthenticated, user } = useSelector((state) => state.auth);

  const location = useLocation();

  if (!isAuthenticated) {
    // Add redirect parameter with the current path
    const redirectPath = encodeURIComponent(location.pathname + location.search);
    return <Navigate to={`/login?redirect=${redirectPath}`} replace state={{ from: location }} />;
  }
  if (allowedRoles && user && !allowedRoles.includes(user.role)) {
    return <Navigate to="/unauthorized" replace />;
  }
  return <Element {...rest} />;
};

export default ProtectedRoute;
