import React, { useEffect, useRef, useState } from "react";
import { Outlet, useNavigate, Link, useLocation } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { Menu, Sun, Moon, ChevronDown, X } from "lucide-react";
import ebookLogo from "@/assets/ebookLogo.png";
import {
  menuItems,
  profileMenuVariants,
  submenuVariants,
  sidebarVariants,
} from "@/configs/dashboardConfig";
import LanguageSwitcher from "../LanguageSwitcher";
import { useTranslation } from "react-i18next";
import { useAuth } from "../../hooks/useAuth";
import useDataFetching from "@/hooks/useDataFetching";

export default function DashboardLayout() {
  const { t } = useTranslation();
  const { logout } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(() => {
    const storedSidebarOpen = localStorage.getItem("sidebarOpen");
    return storedSidebarOpen != null ? storedSidebarOpen === "true" : true;
  });
  const [darkMode, setDarkMode] = useState(() => {
    // Initialize darkMode from localStorage in this component
    if (typeof window !== 'undefined') {
        return localStorage.getItem('newtheme') === 'dark';
    }
    return false;
});
  const [isHovered, setIsHovered] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  // const [darkMode, setDarkMode] = useState(false);
  const [menuStates, setMenuStates] = useState({});
  const navigate = useNavigate();
  const location = useLocation();
  const profileMenuRef = useRef(null);
  const [profileMenuOpen, setProfileMenuOpen] = useState(false);

  // Get user data using useDataFetching hook for real-time updates
  const { data: userInfo } = useDataFetching({
    queryKey: 'user',
    endPoint: 'profile',
  });

  // Extract user data from the response
  const user = userInfo?.data;
  console.log(user);
  const allowedRoutesForNonAdmin = ["Dashboard", "eBook", "Templates"];
  // Filter the menu items based on the user role
  const filteredMenuItems =
    user?.role === "admin"
      ? menuItems
      : menuItems.filter((item) =>
        allowedRoutesForNonAdmin.includes(item.title)
      );

      const toggleDarkMode = () => {
        const newDarkMode = !darkMode;
        setDarkMode(newDarkMode);
        // Save to localStorage here in this component
        if (typeof window !== 'undefined') {
            localStorage.setItem('newtheme', newDarkMode ? 'dark' : 'light');
            console.log('Theme saved to localStorage from DashboardLayout:', localStorage.getItem('newtheme'));
        }
        document.documentElement.classList.toggle("dark", newDarkMode);
    };

  const handleLogout = () => {
    logout()
  };

  const toggleSubmenu = (title) => {
    setMenuStates((prev) => ({ ...prev, [title]: !prev[title] }));
  };

  const isActive = (path) => location.pathname === path;

  useEffect(() => {
    // Apply dark mode class on component mount and when darkMode changes
    if (darkMode) {
        document.documentElement.classList.add("dark");
    } else {
        document.documentElement.classList.remove("dark");
    }

    const handleClickOutside = (event) => {
        if (profileMenuRef.current && !profileMenuRef.current.contains(event.target)) {
            setProfileMenuOpen(false);
        }
    };

    const handleResize = () => {
        if (window.innerWidth >= 1024) {
            setMobileMenuOpen(false);
        }
    };

    document.addEventListener("mousedown", handleClickOutside);
    window.addEventListener("resize", handleResize);

    return () => {
        document.removeEventListener("mousedown", handleClickOutside);
        window.removeEventListener("resize", handleResize);
    };
}, [darkMode]);

  const toggleSidebar = () => {
    setSidebarOpen((prev) => {
      const newState = !prev;
      localStorage.setItem("sidebarOpen", newState);
      return newState;
    });
  };

  const renderNavigation = (isMobile = false) => (
    <nav className="p-4 flex-1">
      {filteredMenuItems.map((item) => (
        <div key={item.title} className="mb-2">
          {item.submenu ? (
            <div>
              <button
                onClick={() => toggleSubmenu(item.title)}
                className={`flex items-center justify-between w-full px-4 py-2 rounded transition-colors ${isActive(item.path)
                  ? "bg-blue-500 text-white"
                  : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                  }`}
              >
                <span
                  className={`flex items-center ${!isMobile && !(sidebarOpen || isHovered)
                    ? "justify-center w-full"
                    : "space-x-2"
                    }`}
                >
                  <div className="w-5 h-5 flex items-center justify-center">
                    {item.icon}
                  </div>
                  {(isMobile || sidebarOpen || isHovered) && (
                    <span>{t(`${item.title}`)}</span>
                  )}
                </span>
                {(isMobile || sidebarOpen || isHovered) && (
                  <motion.div
                    animate={{ rotate: menuStates[item.title] ? 180 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ChevronDown className="w-4 h-4" />
                  </motion.div>
                )}
              </button>
              {(isMobile || sidebarOpen || isHovered) && (
                <motion.div
                  initial="closed"
                  animate={menuStates[item.title] ? "open" : "closed"}
                  variants={submenuVariants}
                  className="overflow-hidden"
                >
                  {item.submenu.map((subItem) => (
                    <Link
                      key={subItem.path}
                      to={subItem.path}
                      className={`block pl-12 py-2 text-sm transition-colors ${isActive(subItem.path)
                        ? "bg-blue-500 text-white"
                        : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                        }`}
                    >
                      {t(`${subItem.title}`)}
                    </Link>
                  ))}
                </motion.div>
              )}
            </div>
          ) : (
            <Link
              to={item.path}
              className={`flex items-center px-4 py-2 rounded transition-colors ${!isMobile && !(sidebarOpen || isHovered)
                ? "justify-center"
                : "space-x-2"
                } ${isActive(item.path)
                  ? "bg-blue-500 text-white"
                  : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                }`}
            >
              <div className="w-5 h-5 flex items-center justify-center">
                {item.icon}
              </div>
              {(isMobile || sidebarOpen || isHovered) && (
                <span>{t(`${item.title}`)}</span>
              )}
            </Link>
          )}
        </div>
      ))}
    </nav>
  );

  return (
    <div className={`min-h-screen ${darkMode ? "dark" : ""}`}>
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
        {/* Desktop Sidebar */}
        <motion.aside
          initial="open"
          animate={sidebarOpen || isHovered ? "open" : "closed"}
          variants={sidebarVariants}
          onMouseEnter={() => !sidebarOpen && setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          className="hidden lg:block relative overflow-hidden border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
        >
          <div className="h-full flex flex-col">
            <div className="flex items-center justify-center border-b border-gray-200 dark:border-gray-700 h-20 py-6">
              {sidebarOpen || isHovered ? (
                <img
                  src={ebookLogo}
                  alt="eBook Logo"
                  className="h-9 cursor-pointer"
                  onClick={() => navigate("/admin")}
                />
              ) : (
                <div
                  className="w-9 h-9 rounded-full bg-blue-500 flex items-center justify-center cursor-pointer"
                  onClick={() => navigate("/")}
                >
                  <span className="text-white text-lg font-bold">E</span>
                </div>
              )}
            </div>
            {renderNavigation()}
          </div>
        </motion.aside>

        {/* Mobile Drawer */}
        <AnimatePresence>
          {mobileMenuOpen && (
            <>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 0.5 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black lg:hidden z-40"
                onClick={() => setMobileMenuOpen(false)}
              />
              <motion.div
                initial={{ x: "-100%" }}
                animate={{ x: 0 }}
                exit={{ x: "-100%" }}
                transition={{ type: "tween", duration: 0.3 }}
                className="fixed inset-y-0 left-0 w-64 bg-white dark:bg-gray-800 lg:hidden overflow-y-auto z-50"
              >
                <div className="h-full flex flex-col">
                  <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 h-20 px-4">
                    <img
                      src={ebookLogo}
                      alt="eBook Logo"
                      className="h-9 cursor-pointer"
                      onClick={() => navigate("/")}
                    />
                    <button
                      onClick={() => setMobileMenuOpen(false)}
                      className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <X className="w-6 h-6 text-gray-500 dark:text-gray-400" />
                    </button>
                  </div>
                  {renderNavigation(true)}
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>

        <div className="flex-1 flex flex-col overflow-hidden">
          <header className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
            <button
              onClick={() => {
                if (window.innerWidth >= 1024) {
                  toggleSidebar();
                } else {
                  setMobileMenuOpen(true);
                }
              }}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <Menu className="w-6 h-6 text-gray-500 dark:text-gray-400" />
            </button>
            <div className="flex items-center space-x-4">
              <LanguageSwitcher />
              <button
                onClick={toggleDarkMode}
                className="p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                {darkMode ? (
                  <Sun className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                ) : (
                  <Moon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                )}
              </button>
              <div className="relative z-40" ref={profileMenuRef}>
                <button
                  onClick={() => setProfileMenuOpen((prev) => !prev)}
                  className="flex items-center space-x-2 px-4 py-2 rounded text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  {user && user.profile_picture_url ? (
                    <img
                      src={user.profile_picture_url}
                      alt="User"
                      className="w-8 h-8 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
                      <span className="text-gray-600 text-sm font-medium">
                        {user?.name?.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  )}
                  <span>{user?.name}</span>
                  <ChevronDown
                    className={`w-4 h-4 transition-transform ${profileMenuOpen ? "rotate-180" : ""
                      }`}
                  />
                </button>
                <AnimatePresence>
                  {profileMenuOpen && (
                    <motion.div
                      initial="closed"
                      animate="open"
                      exit="closed"
                      variants={profileMenuVariants}
                      className="absolute right-0 top-16 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg rounded-lg overflow-hidden"
                    >
                      <Link
                        to="/admin/settings/profile"
                        className="block px-4 py-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      >
                        {t("Profile")}
                      </Link>
                      <button
                        onClick={handleLogout}
                        className="block w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
                      >
                        {t("Logout")}
                      </button>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </header>

          <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 dark:bg-gray-900">
            <div className="container mx-auto px-6 py-8">
              <Outlet />
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}
