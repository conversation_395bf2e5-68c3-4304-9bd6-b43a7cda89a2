import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  currentPage: 0,
  ebookData: {
    pages: [],
  },
  isMobile: false,
  showGallery: false,
  isTextToSpeechEnabled: false, // State for text-to-speech
  currentReadingElementId: null, // State for tracking the current reading element
  textToSpeechSettings: {
    pitch: 1,
    rate: 1,
    volume: 1,
    voice: '', // Property for selected voice
  },
  selectedTool: null, // State for selected tool
  noteTakingLayerData: null,
  noteTakingLayer: false,
  width: null, // Adding width state to store
};

const ebookSlice = createSlice({
  name: 'ebook',
  initialState,
  reducers: {
    setEbookData: (state, action) => {
      state.ebookData = action.payload;
    },
    nextPage: (state) => {
      if (state.currentPage < state.ebookData.pages.length - 2) {
        state.currentPage += 2;
      }
    },
    prevPage: (state) => {
      if (state.currentPage > 0) {
        state.currentPage -= 2;
      }
    },
    setIsMobile: (state, action) => {
      state.isMobile = action.payload;
    },
    resetPage: (state) => {
      state.currentPage = 0;
    },
    setShowGallery: (state, action) => {
      state.showGallery = action.payload;
    },
    setTextToSpeech: (state) => {
      state.isTextToSpeechEnabled = !state.isTextToSpeechEnabled;
    },
    setTextToSpeechSettings: (state, action) => {
      state.textToSpeechSettings = {
        ...state.textToSpeechSettings,
        ...action.payload,
      };
    },
    // Update width directly in the top level state
    setWidth: (state, action) => {
      state.width = action.payload; // Set width directly at the top level
    },

    setCurrentReadingElementId: (state, action) => {
      state.currentReadingElementId = action.payload;
    },
    setSelectedTool(state, action) { // Add this reducer
      state.selectedTool = action.payload;
    },
    setNoteTakingLayer(state, action) { // Add this reducer
      state.noteTakingLayer = action.payload;
    },
    setNoteTakingLayerData(state, action) { // Add this reducer
      state.noteTakingLayerData = action.payload;
    },
    resetToInitialState: (state) => {
      return initialState;
    },
  },
});

export const {
  setEbookData,
  nextPage,
  prevPage,
  setIsMobile,
  setWidth, // Export the new action
  resetPage,
  setShowGallery,
  setTextToSpeech,
  setTextToSpeechSettings,
  setCurrentReadingElementId,
  setNoteTakingLayer,
  setNoteTakingLayerData,
  setSelectedTool,
  resetToInitialState,
} = ebookSlice.actions;

export default ebookSlice.reducer;
