import Modal from "@/components/ui/Modal";
import { useSelector, useDispatch } from "react-redux";
import { setShowGallery } from "@/pages/book-preview/store/ebookSlice";
import Tabs from "@/components/ui/Tabs";
import { useState } from "react";
import { toast } from "sonner";

const Gallery = ({ isOpen, onClose }) => {
   const showGallery = useSelector((state) => state.ebook.showGallery);
   const dispatch = useDispatch();
   const data = useSelector((state) => state.ebook?.ebookData?.pages);

   function getAllImages(data) {
      return data?.flatMap(page =>
         page.elements.filter(element => element.type === "image")
      );
   }
   function getAllVideos(data) {
      return data?.flatMap(page =>
         page.elements.filter(element => element.type === "video")
      );
   }
   function getAllMCQs(data) {
      return data?.flatMap(page =>
         page.elements.filter(element => element.type === "mcq")
      );
   }
   function getAllTrueFalse(data) {
      return data?.flatMap(page =>
         page.elements.filter(element => element.type === "true-false")
      );
   }

   const images = getAllImages(data);
   const videos = getAllVideos(data);
   const mcqs = getAllMCQs(data);
   const trueFalse = getAllTrueFalse(data);

   const [activeTab, setActiveTab] = useState(1);
   const [selectedOptions, setSelectedOptions] = useState({});

   const handleTabChange = (tab) => {
      setActiveTab(tab);
   };

   const handleMCQSelect = (questionId, selectedOption, correctAnswer) => {
      setSelectedOptions(prev => ({ ...prev, [questionId]: selectedOption }));
      if (selectedOption === correctAnswer) {
         toast.success("Correct Answer!", { position: "top-center" });
      } else {
         toast.error("Wrong Answer!", { position: "top-center" });
      }
   };

   const handleTrueFalseSelect = (questionId, selectedAnswer, correctAnswer) => {
      setSelectedOptions(prev => ({ ...prev, [questionId]: selectedAnswer }));
      if (selectedAnswer === correctAnswer) {
         toast.success("Correct Answer!", { position: "top-center" });
      } else {
         toast.error("Wrong Answer!", { position: "top-center" });
      }
   };

   const handleClose = () => {
      onClose();
   };

   const handleClickOutside = () => {
      onClose();
   };

   return (
      <Modal
         activeModal={showGallery}
         onClose={handleClose}
         title="Gallery"
         className="max-w-5xl"
         handleClickOutside={handleClickOutside}
      >
         <div className="h-[80vh] overflow-y-auto">
            <Tabs
               activeTab={activeTab}
               onTabChange={handleTabChange}
               tabs={[
                  {
                     label: "Images",
                     content: images?.length ? (
                        <div className="grid grid-cols-3 gap-4">
                           {images.map((element, index) => (
                              <img
                                 key={index}
                                 className="w-full h-full object-cover"
                                 src={element.content}
                                 alt="Image"
                              />
                           ))}
                        </div>
                     ) : <p className="text-center text-gray-400">No images found</p>
                  },
                  {
                     label: "Videos",
                     content: videos?.length ? (
                        <div className="grid grid-cols-3 gap-4">
                           {videos.map((element, index) => (
                              <video
                                 key={index}
                                 className="w-full h-full"
                                 controls
                                 src={element.content}
                              />
                           ))}
                        </div>
                     ) : <p className="text-center text-gray-400">No videos found</p>
                  },
                  {
                     label: "MCQs",
                     content: mcqs?.length ? (
                        <div className="grid grid-cols-1 gap-4">
                           {mcqs.map((element, index) => (
                              <div key={index} className="p-4 border rounded-lg">
                                 <p className="text-lg font-semibold mb-4">{element.content.question}</p>
                                 <ul className="pl-4" style={{ listStyle: "none" }}>
                                    {element.content.options.map((option, i) => (
                                       <li key={i} className="mb-2">
                                          <label className="flex items-center space-x-2">
                                             <input
                                                type="radio"
                                                name={`mcq-${index}`}
                                                className="form-radio"
                                                checked={selectedOptions[element.id] === i}
                                                onChange={() => handleMCQSelect(element.id, i, element.content.correctAnswer)}
                                             />
                                             <span>{option}</span>
                                          </label>
                                       </li>
                                    ))}
                                 </ul>
                              </div>
                           ))}
                        </div>
                     ) : <p className="text-center text-gray-400">No MCQs found</p>
                  },
                  {
                     label: "True/False",
                     content: trueFalse?.length ? (
                        <div className="grid grid-cols-1 gap-4">
                           {trueFalse.map((element, index) => (
                              <div key={index} className="p-4 border rounded-lg">
                                 <p className="text-lg font-semibold mb-4">{element.content.question}</p>
                                 <div className="flex space-x-4">
                                    <label className="flex items-center space-x-2">
                                       <input
                                          type="radio"
                                          name={`truefalse-${index}`}
                                          className="form-radio"
                                          checked={selectedOptions[element.id] === true}
                                          onChange={() => handleTrueFalseSelect(element.id, true, element.content.correctAnswer)}
                                       />
                                       <span>True</span>
                                    </label>
                                    <label className="flex items-center space-x-2">
                                       <input
                                          type="radio"
                                          name={`truefalse-${index}`}
                                          className="form-radio"
                                          checked={selectedOptions[element.id] === false}
                                          onChange={() => handleTrueFalseSelect(element.id, false, element.content.correctAnswer)}
                                       />
                                       <span>False</span>
                                    </label>
                                 </div>
                              </div>
                           ))}
                        </div>
                     ) : <p className="text-center text-gray-400">No true/false questions found</p>
                  },
               ]}
            />
         </div>
      </Modal>
   );
};

export default Gallery;

