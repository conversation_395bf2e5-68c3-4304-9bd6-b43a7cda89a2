import { FONT_SIZES } from "./constant"
import { getSelectedTextFontSize, applyParagraphSpacing, handleVerticalText } from "./utils"

export const setupToolbar = (editor, splitButtonApi) => {
  editor.ui.registry.addIcon(
    "customVertical",
    '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M12 2v20M5 12h14" stroke="#000" stroke-width="2" fill="none"/></svg>'
  )
  editor.ui.registry.addIcon(
    "customHorizontal",
    '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M2 12h20M12 5v14" stroke="#000" stroke-width="2" fill="none"/></svg>'
  )
  editor.ui.registry.addButton("verticaltext", {
    icon: "customVertical",
    tooltip: "Vertical Text (Tategaki)",
    onAction: () => {
      const selectedText = editor.selection.getContent({ format: "html" })
      if (selectedText) {
        editor.insertContent(`
          <div style="float: right; writing-mode: vertical-rl; text-orientation: upright; white-space: pre-wrap; margin: 0; padding: 0; line-height: 1.2;">
            ${selectedText}
          </div>
        `)
      }
      handleVerticalText.wrap(editor)
    }
  })
  editor.ui.registry.addButton("horizontaltext", {
    icon: "customHorizontal",
    tooltip: "Horizontal Text (English)",
    onAction: () => {
      const selectedText = editor.selection.getContent({ format: "html" })
      if (selectedText) {
        editor.insertContent(`
          <div style="writing-mode: horizontal-tb; text-orientation: initial; white-space: pre-wrap; margin: 0; padding: 0; line-height: 1.2;">
            ${selectedText}
          </div>
        `)
      }
      handleVerticalText.unwrap(editor)
    }
  })
  editor.ui.registry.addSplitButton("customfontsize", {
    tooltip: "Font Size",
    text: "16px",
    onSetup: api => {
      splitButtonApi = api
      const nodeChangeHandler = () => {
        const size = getSelectedTextFontSize(editor)
        api.setText(size + "px")
      }
      editor.on("NodeChange", nodeChangeHandler)
      return () => {
        splitButtonApi = null
        editor.off("NodeChange", nodeChangeHandler)
      }
    },
    onAction: () => {
      const currentSize = getSelectedTextFontSize(editor)
      editor.execCommand("FontSize", false, currentSize + "px")
    },
    onItemAction: (api, value) => {
      if (value === "custom") {
        const currentSize = getSelectedTextFontSize(editor)
        editor.windowManager.open({
          title: "Custom Font Size",
          body: {
            type: "panel",
            items: [
              {
                type: "input",
                name: "fontsize",
                label: "Enter font size in pixels",
                inputMode: "numeric",
                value: currentSize.toString()
              }
            ]
          },
          buttons: [{ type: "submit", text: "Apply" }],
          onSubmit: api => {
            const size = api.getData().fontsize
            if (size) {
              editor.execCommand("FontSize", false, size + "px")
            }
            api.close()
          }
        })
      } else {
        editor.execCommand("FontSize", false, value + "px")
      }
    },
    fetch: callback => {
      const items = [
        ...FONT_SIZES.map(size => ({
          type: "choiceitem",
          text: size + "px",
          value: size.toString()
        })),
        {
          type: "choiceitem",
          text: "Custom Size...",
          value: "custom"
        }
      ]
      callback(items)
    }
  })
  editor.ui.registry.addMenuButton("paragraphspacing", {
    icon: "paragraph",
    tooltip: "Paragraph Spacing",
    fetch: callback => {
      const items = [
        {
          type: "menuitem",
          text: "Add Space Before Paragraph",
          onAction: () => {
            applyParagraphSpacing(editor, "addSpaceBefore")
            return false
          }
        },
        {
          type: "menuitem",
          text: "Custom Spacing...",
          onAction: () => {
            editor.windowManager.open({
              title: "Custom Paragraph Spacing",
              body: {
                type: "panel",
                items: [
                  {
                    type: "input",
                    name: "spacing",
                    label: "Enter spacing in pixels",
                    inputMode: "numeric"
                  }
                ]
              },
              buttons: [{ type: "submit", text: "Apply" }],
              onSubmit: api => {
                const spacing = parseInt(api.getData().spacing)
                if (!isNaN(spacing)) {
                  applyParagraphSpacing(editor, "customSpacing", spacing)
                }
                api.close()
                editor.focus()
                return false
              }
            })
          }
        },
        {
          type: "menuitem",
          text: "Remove All Spacing",
          onAction: () => {
            applyParagraphSpacing(editor, "removeSpacing")
            return false
          }
        }
      ]
      callback(items)
    }
  })
}
