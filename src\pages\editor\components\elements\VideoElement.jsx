import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import CustomVideoPlayer from "./CustomVideoPlayer";

const VideoElement = ({ element, miniView }) => {
  const [isControlsVisible, setIsControlsVisible] = useState(false);
  const { id, content } = element;
  const dispatch = useDispatch();
  const { videoUrl, thumbnail, isYoutube } = content;
  const currentReadingElementId = useSelector(
    (state) => state.ebook.currentReadingElementId
  );

  // const handleVideoUrlChange = (newUrl) => {
  //   dispatch(updateElement({
  //     id,
  //     updates: {
  //       content: {
  //         ...content,
  //         videoUrl: newUrl
  //       }
  //     }
  //   }));
  // };

  return (
    <div
      style={{ width: "100%", height: "100%" }}
      className={`relative ${
        currentReadingElementId === element.id
          ? "border border-blue-500"
          : "border"
      }`}
      onMouseEnter={() => setIsControlsVisible(true)}
      onMouseLeave={() => setIsControlsVisible(false)}
    >
      <div
        style={{ position: "relative", width: "100%", paddingTop: "56.25%" }}
      >
        {isYoutube ? (
          // YouTube Video
          <iframe
            src={videoUrl}
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              border: "none",
            }}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          />
        ) : (
          // Custom Video Player for non-YouTube videos
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
            }}
          >
            <CustomVideoPlayer
              miniView={miniView}
              src={videoUrl}
              thumbnail={thumbnail}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoElement;
