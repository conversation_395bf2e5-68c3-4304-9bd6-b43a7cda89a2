import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  mcqElements: {},
};

const mcqSlice = createSlice({
  name: 'mcq',
  initialState,
  reducers: {
    addMCQ: (state, action) => {
      const { id, content } = action.payload;
      state.mcqElements[id] = content;
    },
    updateMCQ: (state, action) => {
      const { id, updates } = action.payload;
      state.mcqElements[id] = { ...state.mcqElements[id], ...updates };
    },
    deleteMCQ: (state, action) => {
      delete state.mcqElements[action.payload];
    },
  },
});

export const { addMCQ, updateMCQ, deleteMCQ } = mcqSlice.actions;
export default mcqSlice.reducer;