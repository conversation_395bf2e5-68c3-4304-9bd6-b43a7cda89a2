import React, { useEffect, useRef, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { jsPDF } from "jspdf";
import html2canvas from "html2canvas";
import { Document, Page, pdfjs } from "react-pdf";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import { useEbook } from "./hooks/useEbook";
import Loading from "@/components/Loading";
import Toolbar from "./components/Toolbar";
import BookToolbar from "./sections/BookToolbar";
import { setCurrentReadingElementId } from "./store/ebookSlice";
import JumpToPage from "./components/JumpToPage";
import { clearAllDrawings } from "./store/drawingSlice";
import LazyPageWithNotes from "./LazyPageWithNotes";
import LazyHtmlContent from "./LazyHtmlContent";
import { useScrollBookScale } from "../../hooks/useScrollBookScale";
pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js`;

export default function BookScrollView() {
  const [clickedPageId, setClickedPageId] = useState(null);
  const [bookmarks, setBookmarks] = useState([]);
  const { slug, id } = useParams();
  const isAdmin = slug ? "user" : "admin";
  // const { scale, zoomIn, zoomOut, resetZoom, currentZoom } = useZoomableScale(
  //   0.3,
  //   1,
  //   900
  // );
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);
  const navigate = useNavigate();

  const { ebook, isLoading, error } = useEbook({
    ebookId: slug ? slug : id,
    isAdmin,
  });

  const { scale, zoomIn, zoomOut, resetZoom, currentZoom } = useScrollBookScale(
    ebook?.width,
    ebook?.height,
    0.3,
    2,
    40
  );


  useEffect(() => {
    if (ebook?.bookmarks) {
      setBookmarks(ebook.bookmarks);
    }
  }, [ebook]);

  // ---------------------------
  // PDF viewer states & logic
  // ---------------------------
  const [numPages, setNumPages] = useState(null);
  const containerRef = useRef(null);
  const [containerWidth, setContainerWidth] = useState(0);

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, []);

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
  };

  // ---------------------------
  // Text-to-speech logic
  // ---------------------------
  const pageRefs = useRef([]);
  const speechSynthesisRef = useRef(window.speechSynthesis);
  const [activePage, setActivePage] = useState(null);
  const dispatch = useDispatch();

  const { pitch, rate, volume, voice, isTextToSpeechEnabled } = useSelector(
    (state) => ({
      ...state.ebook.textToSpeechSettings,
      isTextToSpeechEnabled: state.ebook.isTextToSpeechEnabled,
    })
  );

  const createUtterance = (text, callback) => {
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = rate;
    utterance.pitch = pitch;
    utterance.volume = volume;

    const availableVoices = speechSynthesisRef.current.getVoices();
    const selectedVoice = availableVoices.find((v) => v.name === voice);
    if (selectedVoice) {
      utterance.voice = selectedVoice;
    }

    utterance.onend = () => {
      if (callback) callback();
    };

    return utterance;
  };

  const readElement = (element, callback) => {
    let textToSpeak = "";

    switch (element.type) {
      case "text": {
        const tempDiv = document.createElement("div");
        tempDiv.innerHTML = element.content;
        textToSpeak = tempDiv.innerText;
        break;
      }
      case "mcq":
        textToSpeak = `Question: ${element.content.question
          }. Options: ${element.content.options.join(", ")}`;
        break;
      case "true-false":
        textToSpeak = `Question: ${element.content.question}`;
        break;
      case "video":
        textToSpeak = "This is a video element.";
        break;
      case "flashcard":
        textToSpeak = "This is a flashcard element.";
        break;
      case "shape":
        textToSpeak = "This is a shape element.";
        break;
      default:
        textToSpeak = "";
    }

    if (textToSpeak) {
      dispatch(setCurrentReadingElementId(element.id));
      const utterance = createUtterance(textToSpeak, callback);
      speechSynthesisRef.current.speak(utterance);
    } else if (callback) {
      callback();
    }
  };

  const readPageElements = (pageIndex) => {
    if (!ebook?.pages?.length || pageIndex >= ebook.pages.length) return;

    const pageElements = ebook.pages[pageIndex]?.elements || [];
    let elementIndex = 0;

    const readNextElement = () => {
      if (elementIndex >= pageElements.length) {
        dispatch(setCurrentReadingElementId(null));
        setTimeout(() => goToNextPage(pageIndex), 1000);
        return;
      }
      const element = pageElements[elementIndex];
      elementIndex += 1;
      readElement(element, readNextElement);
    };

    readNextElement();
  };

  const goToNextPage = (currentPage) => {
    const nextPage = currentPage + 1;
    if (nextPage < ebook.pages.length) {
      setActivePage(nextPage);
      pageRefs.current[nextPage]?.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
      setTimeout(() => readPageElements(nextPage), 1500); // Start reading the next page
    } else {
      speechSynthesisRef.current.cancel();
    }
  };

  const readVisiblePages = () => {
    if (!ebook?.pages || ebook.pages.length === 0) return;
    const currentPageIndex = activePage;
    speechSynthesisRef.current.cancel();

    if (currentPageIndex !== null) {
      readPageElements(currentPageIndex);
    }
  };

  useEffect(() => {
    if (isTextToSpeechEnabled) {
      readPageElements(activePage || 0); // Start from the active page
    } else {
      speechSynthesisRef.current.cancel();
      dispatch(setCurrentReadingElementId(null));
    }
    return () => speechSynthesisRef.current.cancel();
  }, [isTextToSpeechEnabled, pitch, rate, volume, voice]);

  // ---------------------------
  // Page observer logic
  // ---------------------------
  useEffect(() => {
    if (!ebook?.pages) return;

    const observerOptions = {
      root: null,
      rootMargin: "0px",
      threshold: 0.6,
    };

    const observerCallback = (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const idx = pageRefs.current.indexOf(entry.target);
          if (idx !== -1) setActivePage(idx);
        }
      });
    };

    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );
    pageRefs.current.forEach((pageEl) => {
      if (pageEl) observer.observe(pageEl);
    });

    return () => observer.disconnect();
  }, [ebook]);

  // ---------------------------
  // PDF Generation logic
  // ---------------------------
  const handleGeneratePDF = async () => {
    if (!ebook?.pages?.length) return;

    const pdf = new jsPDF("p", "pt", "a4");
    const pdfWidth = pdf.internal.pageSize.getWidth();

    for (let i = 0; i < ebook.pages.length; i++) {
      const pageEl = pageRefs.current[i];
      if (!pageEl) continue;

      const canvas = await html2canvas(pageEl, { scale: 2 });
      const imgData = canvas.toDataURL("image/png");

      const imgWidth = pdfWidth;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      pdf.addImage(imgData, "PNG", 0, 0, imgWidth, imgHeight);
      if (i < ebook.pages.length - 1) {
        pdf.addPage();
      }
    }

    pdf.save("ebook.pdf");
  };

  // ---------------------------
  // Error handling & loading
  // ---------------------------
  useEffect(() => {
    if (error) {
      navigate("/404");
    }
  }, [error, navigate]);

  if (isLoading) {
    return <Loading />;
  }

  // ---------------------------
  // Main render
  // ---------------------------

  if (!ebook || !ebook.pages || !Array.isArray(ebook.pages)) {
    console.error("Invalid data provided to Book Scroll View");
    return <div>Error: Invalid ebook data</div>;
  }

  const handleJumpToPage = (pageIndex) => {
    if (ebook.book_type === "pdf") {
      const pageElements = document.querySelectorAll('.react-pdf__Page');
      if (pageElements[pageIndex - 1]) { // pageIndex is 1-based for PDFs
        pageElements[pageIndex - 1].scrollIntoView({
          behavior: "smooth",
          block: "start"
        });
        setActivePage(pageIndex - 1);
      }
    } else {
      if (pageRefs.current[pageIndex]) {
        pageRefs.current[pageIndex].scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
        setActivePage(pageIndex);
      }
    }
  };

  const handleJumpToBookmark = (pageId) => {
    const pageIndex = ebook.pages.findIndex((page) => page.id === pageId);
    if (pageIndex !== -1 && pageRefs.current[pageIndex]) {
      pageRefs.current[pageIndex].scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
      setActivePage(pageIndex);
    }
  };


  return (
    // <div className="relative h-screen w-screen">
    <div className="relative h-screen">
      <Toolbar clickedPageId={clickedPageId} />
      <div className="fixed top-3 left-1/2 transform -translate-x-1/2 z-50 hidden md:block">
        <JumpToPage
          totalPages={ebook.book_type === "pdf" ? numPages : ebook?.pages?.length}
          onJump={handleJumpToPage}
          isPdf={ebook.book_type === "pdf"}
        />
      </div>

      <div
        className={`fixed bg-gray-50 border ${isMobile
          ? "bottom-0 z-50 w-full flex flex-row justify-center py-2 border-t"
          : "left-0 top-10 h-full flex flex-col justify-center border-r"
          }`}
      >
        <BookToolbar handleJumpToPage={handleJumpToBookmark} onGeneratePDF={handleGeneratePDF} zoomIn={zoomIn} zoomOut={zoomOut} resetZoom={resetZoom} />
      </div>

      {ebook.book_type === "pdf" ? (
        <div
          ref={containerRef}
          className="flex flex-col items-center min-h-screen mt-20"
          key={scale}
        >
          <Document file={ebook.pdf_file_url} onLoadSuccess={onDocumentLoadSuccess}>
            {Array.from(new Array(numPages || 0), (_, index) => (
              <LazyPageWithNotes
                key={index}
                pageNumber={index + 1}
                containerWidth={containerWidth}
                ebookId={slug || id}
                scale={scale}
                onPageClick={() => setClickedPageId(index + 1)}
              />
            ))}
          </Document>
        </div>
      ) : (
        // Normal Book (HTML pages)
        <div className="flex flex-col w-full" key={scale}>
          <div className="flex flex-col items-center mt-20 gap-8 w-full px-4 md:px-8">
            {ebook.pages?.map((page, index) => (
              <div
                key={page.id}
                ref={(el) => (pageRefs.current[index] = el)}
                className="relative mb-5"
                style={{
                  width: ebook.width * scale,
                  height: ebook.height * scale,
                  overflow: "hidden",
                }}
                onClick={() => setClickedPageId(page.id)}
              >
                <div
                  style={{
                    transform: `scale(${scale})`,
                    transformOrigin: "top left",
                    width: ebook.width,
                    height: ebook.height,
                  }}
                >
                  <LazyHtmlContent
                    ebookId={slug || id}
                    page={page}
                    index={index}
                    width={ebook.width}
                    height={ebook.height}
                    bookmarks={ebook?.bookmarks}
                    isMobile={isMobile}
                  />
                </div>
              </div>
            ))}
          </div>
          <div className="fixed top-0 right-0 h-screen w-48 flex flex-col items-center bg-gray-50 border-l hidden lg:flex">
            <div className="flex flex-col items-center mt-8 gap-2 w-full px-4 overflow-y-auto">
              {ebook.pages?.map((page, index) => (
                <div
                  key={page.id}
                  className={`relative w-full cursor-pointer ${activePage === index ? "border-2 border-blue-500" : ""
                    }`}
                  style={{
                    backgroundColor: page?.bg_color || "white",
                    height: "200px",
                    margin: "0.25rem",
                    transform: "scale(0.2)",
                    transformOrigin: "top left",
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    setClickedPageId(page.id);
                    pageRefs.current[index]?.scrollIntoView({
                      behavior: "smooth",
                      block: "start",
                    });
                  }}
                >
                  <LazyHtmlContent
                    miniView={true}
                    ebookId={slug}
                    page={page}
                    index={index}
                    width={ebook.width}
                    height={ebook.height}
                    isMobile={isMobile}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
