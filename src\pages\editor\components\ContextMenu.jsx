import React from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  copyElement,
  copyElements,
  cutElement,
  cutElements,
  deleteElement,
  sendToBack,
  sendToFront,
} from "../store/pagesSlice";

const ContextMenu = ({ id, position, size, onClose }) => {
  const dispatch = useDispatch();
  const selectedElements = useSelector((state) => state.pages.present.selectedElements);

  const handleCopy = () => {
    if (selectedElements.length > 1) {
      dispatch(copyElements());
    } else {
      dispatch(copyElement());
    }
    onClose();
  };

  const handleCut = () => {
    if (selectedElements.length > 1) {
      dispatch(cutElements());
    } else {
      dispatch(cutElement());
    }
    onClose();
  };

  const handleSendToBack = () => {
    dispatch(sendToBack({ id }));
    onClose();
  };

  const handleSendToFront = () => {
    dispatch(sendToFront({ id }));
    onClose();
  };

  const handleDelete = () => {
    dispatch(deleteElement());
    onClose();
  };

  return (
    <div
      id={`context-menu-${id}`}
      style={{
        position: "absolute",
        top: position.y + size.height + 20,
        left: position.x + 20,
        background: "white",
        border: "1px solid #ccc",
        borderRadius: "4px",
        boxShadow: "0 2px 10px rgba(0,0,0,0.2)",
        zIndex: 1000,
        width: "150px",
        padding: "5px 0",
      }}
    >
      <button
        onClick={handleCopy}
        style={{
          display: "block",
          padding: "8px 12px",
          width: "100%",
          textAlign: "left",
          background: "transparent",
          border: "none",
          cursor: "pointer",
        }}
      >
        Copy
      </button>
      <button
        onClick={handleCut}
        style={{
          display: "block",
          padding: "8px 12px",
          width: "100%",
          textAlign: "left",
          background: "transparent",
          border: "none",
          cursor: "pointer",
        }}
      >
        Cut
      </button>
      <button
        onClick={handleSendToBack}
        style={{
          display: "block",
          padding: "8px 12px",
          width: "100%",
          textAlign: "left",
          background: "transparent",
          border: "none",
          cursor: "pointer",
        }}
      >
        Send to Back
      </button>
      <button
        onClick={handleSendToFront}
        style={{
          display: "block",
          padding: "8px 12px",
          width: "100%",
          textAlign: "left",
          background: "transparent",
          border: "none",
          cursor: "pointer",
        }}
      >
        Send to Front
      </button>
      <hr style={{ margin: "5px 0", borderColor: "#eee" }} />
      <button
        onClick={handleDelete}
        style={{
          display: "block",
          padding: "8px 12px",
          width: "100%",
          textAlign: "left",
          background: "transparent",
          border: "none",
          cursor: "pointer",
          color: "red",
        }}
      >
        Delete
      </button>
    </div>
  );
};

export default ContextMenu;
