import React, { useEffect, useState } from "react";
import useDataFetching from "@/hooks/useDataFetching";
import { motion } from "framer-motion";
import BookCard from "@/components/ui/BookCard";
import EbookCardSkeleton from "@/components/ui/EbookCardSkeleton";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next"; // Import useTranslation
import { useSelector } from "react-redux";

const Books = () => {
  const [page, setPage] = useState(1);
  const [perPage] = useState(4);
  const [search, setSearch] = useState("");

  const isAdmin = useSelector((state) => state.auth?.user?.role == "admin");
  const { isAuthenticated } = useSelector((state) => state.auth);

  const { data, isLoading, isError, refetch } = useDataFetching({
    queryKey: ["books", page, perPage, search],
    endPoint: "/home",
  });
  const books = data?.data;
  const navigate = useNavigate();
  const { t } = useTranslation(); // Use useTranslation hook

  const handleSearch = (e) => {
    e.preventDefault();
    refetch();
  };

  const handleViewEbook = (book) => {
    if (!isAuthenticated) {
      // Redirect to login with the ebook URL as redirect parameter
      const ebookUrl = `/ebooks/${book?.id}`;
      const redirectPath = encodeURIComponent(ebookUrl);
      navigate(`/login?redirect=${redirectPath}`);
    } else {
      // User is authenticated, open the ebook
      window.open(`/ebooks/${book?.id}`, '_blank');
    }
  };

  const loadMore = () => {
    navigate("/ebooks");
  };

  if (isError) {
    return (
      <div className="text-center py-24">
        <p className="text-lg text-red-600">{t("bookSection.error")}</p>
      </div>
    );
  }

  return (
    <div id="books" className="py-24 ">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
            {t("bookSection.exploreLibrary")}
          </h2>
          <p className="mt-4 text-gray-600 dark:text-gray-300">
            {t("bookSection.browseDescription")}
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {isLoading
            ? Array.from({ length: 4 }, (_, index) => (
                <EbookCardSkeleton key={index} /> // Changed to use EbookCardSkeleton
              ))
            : books?.data?.slice(0, 4).map((book) => (
                <motion.div
                  key={book?.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  <BookCard
                    book={book}
                    onView={() => handleViewEbook(book)}
                  />
                </motion.div>
              ))}
        </div>

        <div className="text-center mt-8">
          <button
            type="button"
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            onClick={loadMore}
          >
            {t("bookSection.showMore")}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Books;
