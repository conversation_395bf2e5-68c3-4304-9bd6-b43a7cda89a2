import { useQuery } from "@tanstack/react-query";
import api from "@/lib/axios";

const useDataFetching = ({ queryKey, endPoint, params = {}, enabled = true }) => {
    return useQuery({
        queryKey: [queryKey, params],
        queryFn: async () => {
            const { id, ...otherParams } = params;

            try {
                const response = id
                    ? await api.get(`${endPoint}/${id}`)
                    : await api.get(endPoint, { params: otherParams });

                return response.data;
            } catch (error) {
                throw new Error(error?.response?.data?.message || "Data fetching error");
            }
        },
        enabled, // Allows disabling the query when needed
    });
};

export default useDataFetching;
