import React from 'react';

const EbookInfoView = ({ book }) => {
    const fallbackImage = 'https://via.placeholder.com/80x52'; // Placeholder image URL
    const fallbackText = 'N/A'; // Fallback text for missing data

    if (!book) {
        return <p className="text-center text-gray-500">No book selected.</p>;
    }

    return (
        <div>
            <div className="flex justify-center">
                <div>
                    <div className="px-5">
                        <div className="grid grid-cols-12 gap-4 sm:gap-2">
                            <div className="col-span-2">
                                <h6 className="text-base font-normal text-[#000000]">Name</h6>
                            </div>
                            <div className="col-span-1">
                                <p className='text-base font-semibold text-[#000000] ml-1'>:</p>
                            </div>
                            <div className="col-span-9">
                                <p className="text-base font-normal text-[#7A818A]">{book.title || fallbackText}</p>
                            </div>
                        </div>
                        <div className="grid grid-cols-12 gap-4 sm:gap-2 mt-3">
                            <div className="col-span-2">
                                <h6 className="text-base font-normal text-[#000000]">Genres</h6>
                            </div>
                            <div className="col-span-1">
                                <p className='text-base font-semibold text-[#000000] ml-1'>:</p>
                            </div>
                            <div className="col-span-9">
                                <p className="text-base font-normal text-[#7A818A]">{book.genre?.name || fallbackText}</p>
                            </div>
                        </div>
                        <div className="grid grid-cols-12 gap-4 sm:gap-2 mt-3">
                            <div className="col-span-2">
                                <h6 className="text-base font-normal text-[#000000]">Author</h6>
                            </div>
                            <div className="col-span-1">
                                <p className='text-base font-semibold text-[#000000] ml-1'>:</p>
                            </div>
                            <div className="col-span-9">
                                <p className="text-base font-normal text-[#7A818A]">{book.author || fallbackText}</p>
                            </div>
                        </div>
                        <div className="grid grid-cols-12 gap-4 sm:gap-2 mt-3">
                            <div className="col-span-2">
                                <h6 className="text-base font-normal text-[#000000]">Description</h6>
                            </div>
                            <div className="col-span-1">
                                <p className='text-base font-semibold text-[#000000] ml-1'>:</p>
                            </div>
                            <div className="col-span-9">
                                <p className="text-base font-normal text-[#7A818A]">{book.description || fallbackText}</p>
                            </div>
                        </div>
                        <div className="grid grid-cols-12 gap-4 sm:gap-2 mt-3">
                            <div className="col-span-2">
                                <h6 className="text-base font-normal text-[#000000]">Book Cover</h6>
                            </div>
                            <div className="col-span-1">
                                <p className='text-base font-semibold text-[#000000] ml-1'>:</p>
                            </div>
                            <div className="col-span-9">
                                <img
                                    src={book.cover_image_url || fallbackImage}
                                    alt={book.title || fallbackText}
                                    className="h-[52px] w-[80px] object-cover rounded-md"
                                />
                                <p className="text-sm text-[#7A818A] mt-2">
                                    {"book cover" + (book.cover_image?.substring(book.cover_image.lastIndexOf('.')) || fallbackText)}
                                </p>
                            </div>
                        </div>
                        <div className="grid grid-cols-12 gap-4 sm:gap-2 mt-3">
                            <div className="col-span-2">
                                <h6 className="text-base font-normal text-[#000000]">Status</h6>
                            </div>
                            <div className="col-span-1">
                                <p className='text-base font-semibold text-[#000000] ml-1'>:</p>
                            </div>
                            <div className="col-span-9">
                                <p className="text-base font-normal text-[#7A818A]">
                                    {book.status === 'published'
                                        ? 'Published'
                                        : book.status === 'pending'
                                            ? 'Pending'
                                            : 'Unpublished'}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default EbookInfoView;
