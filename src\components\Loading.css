/* Common styles */
.loader-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.loader-text {
  margin-top: 1rem;
  color: #666;
  font-size: 0.9rem;
}

/* Spinning Book */
.book-loader {
  perspective: 1000px;
}

.book {
  width: 60px;
  height: 80px;
  position: relative;
  transform-style: preserve-3d;
  animation: rotate 2s infinite linear;
}

.book-page {
  position: absolute;
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  border: 2px solid #ddd;
  transform-origin: left;
  animation: flip 1.5s infinite ease-in-out;
}

/* Pulsing Pages */
.pulsing-pages {
  display: flex;
  gap: 4px;
}

.pulsing-pages .page {
  width: 20px;
  height: 30px;
  background: #4a90e2;
  animation: pulse 1.5s infinite ease-in-out;
}

/* Circular Progress */
.circular-loader {
  width: 50px;
  height: 50px;
}

.circular-loader circle {
  stroke: #4a90e2;
  stroke-linecap: round;
  animation: circle-progress 2s infinite linear;
}

/* Flipping Pages */
.flipping-pages {
  position: relative;
  width: 40px;
  height: 40px;
}

.flip-page {
  position: absolute;
  width: 100%;
  height: 100%;
  background: #4a90e2;
  animation: flip-page 1.2s infinite ease-in-out;
}

/* Dots Wave */
.dots-wave {
  display: flex;
  gap: 6px;
}

.dot {
  width: 8px;
  height: 8px;
  background: #4a90e2;
  border-radius: 50%;
  animation: wave 1s infinite ease-in-out;
}

/* Bookshelf */
.bookshelf {
  display: flex;
  gap: 8px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.book-item {
  width: 20px;
  height: 60px;
  background: #4a90e2;
  animation: bounce 0.6s infinite alternate;
}

/* Text Scramble */
.text-scramble {
  display: flex;
  gap: 2px;
}

.letter {
  font-size: 1.2rem;
  font-weight: bold;
  color: #4a90e2;
  animation: scramble 2s infinite;
}

/* Progress Bar */
.progress-bar {
  width: 200px;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  width: 100%;
  height: 100%;
  background: #4a90e2;
  animation: progress 2s infinite;
}

/* Ink Drop */
.ink-drop {
  position: relative;
  width: 60px;
  height: 60px;
}

.drop {
  width: 20px;
  height: 20px;
  background: #4a90e2;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: drop 2s infinite;
}

/* Minimalist Spinner */
.minimalist-spinner {
  width: 40px;
  height: 40px;
  position: relative;
}

.spinner-ring {
  width: 100%;
  height: 100%;
  border: 3px solid #f0f0f0;
  border-top-color: #4a90e2;
  border-radius: 50%;
  animation: spin 1s infinite linear;
}

/* Animations */
@keyframes rotate {
  from { transform: rotateY(0deg); }
  to { transform: rotateY(360deg); }
}

@keyframes flip {
  0%, 100% { transform: rotateY(0deg); }
  50% { transform: rotateY(-180deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(0.8); opacity: 0.5; }
}

@keyframes circle-progress {
  0% { stroke-dasharray: 0 100; }
  100% { stroke-dasharray: 100 100; }
}

@keyframes flip-page {
  0%, 100% { transform: rotateX(0deg); }
  50% { transform: rotateX(180deg); }
}

@keyframes wave {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes bounce {
  to { transform: translateY(-10px); }
}

@keyframes scramble {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

@keyframes progress {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes drop {
  0% { transform: scale(0); opacity: 1; }
  100% { transform: scale(3); opacity: 0; }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
