import { useState, useEffect } from "react";

export const useScrollBookScale = (bookWidth, bookHeight, minScale = 0.1, maxScale = 2, margin = 40) => {
    const [scale, setScale] = useState(1);
    const [manualScale, setManualScale] = useState(1);

    useEffect(() => {
        const handleResize = () => {
            if (!bookWidth || !bookHeight) return;

            const availableWidth = window.innerWidth - margin;
            const availableHeight = window.innerHeight - margin;

            const widthScale = availableWidth / bookWidth;
            const heightScale = availableHeight / bookHeight;

            const autoScale = Math.min(widthScale, heightScale);
            const clampedScale = Math.max(minScale, Math.min(maxScale, autoScale * manualScale));
            setScale(clampedScale);
        };

        handleResize();
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, [bookWidth, bookHeight, manualScale, minScale, maxScale, margin]);

    const zoomIn = () => setManualScale(prev => Math.min(2, prev + 0.1));
    const zoomOut = () => setManualScale(prev => Math.max(0.1, prev - 0.1));
    const resetZoom = () => setManualScale(1);

    return {
        scale,
        zoomIn,
        zoomOut,
        resetZoom,
        currentZoom: manualScale
    };
};
