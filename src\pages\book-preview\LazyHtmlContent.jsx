import React, { useEffect, useRef, useState } from "react";
import HtmlContent from "./components/HtmlContent";

export default function LazyHtmlContent({
  ebookId,
  page,
  index,
  width,
  height,
  bookmarks,
  miniView,
  isMobile
}) {
  const ref = useRef(null);
  const [inView, setInView] = useState(false);
  const [hasBeenVisible, setHasBeenVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      setInView(entry.isIntersecting);
      if (entry.isIntersecting) {
        setHasBeenVisible(true);
      }
    }, { threshold: 0.1 }); // Use a lower threshold to load content a bit earlier

    if (ref.current) observer.observe(ref.current);

    return () => {
      if (ref.current) observer.unobserve(ref.current);
    };
  }, []);

  // Always render miniView content regardless of visibility
  if (miniView) {
    return (
      <HtmlContent
        ebookId={ebookId}
        page={page}
        index={index}
        width={width}
        height={height}
        bookmarks={bookmarks}
        miniView={miniView}
        isMobile={isMobile}
      />
    );
  }

  // For regular view, use lazy loading
  // Parse the style if it's a string
  const parsedStyle = (() => {
    try {
      return typeof page.style === 'string' ? JSON.parse(page.style) : page.style || {};
    } catch (e) {
      return {};
    }
  })();

  return (
    <div
      ref={ref}
      style={{
        width,
        height,
        backgroundColor: parsedStyle?.backgroundColor || '#ffffff',
        backgroundImage: parsedStyle?.backgroundImage || 'none',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        backgroundPosition: parsedStyle?.backgroundPosition || 'center',
        position: 'relative'
      }}
      className="relative overflow-hidden shadow-lg bg-white border"
    >
      {(inView || hasBeenVisible) && (
        <HtmlContent
          ebookId={ebookId}
          page={page}
          index={index}
          width={width}
          height={height}
          bookmarks={bookmarks}
          isMobile={isMobile}
        />
      )}
      {!hasBeenVisible && index !== 0 && (
        <div className="rounded-full border w-6 h-6 flex items-center justify-center px-2 py-1 absolute bottom-4 right-4 text-gray-500 text-sm">
          {index}
        </div>
      )}
    </div>
  );
}
