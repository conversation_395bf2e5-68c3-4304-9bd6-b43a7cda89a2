import React, { useEffect } from "react";
import MCQElement from "../../editor/components/elements/MCQElement";
import TrueFalseElement from "../../editor/components/elements/TrueFalseElement";
import TextElement from "../../editor/components/elements/TextElement";
import VideoElement from "../../editor/components/elements/VideoElement";
import FlashcardElement from "../../editor/components/elements/FlashcardElement";
import ShapesElement from "../../editor/components/elements/ShapeElement";
import { useSelector } from "react-redux";
import DrawingLayer from "./DrawingLayer";
import { useLocation, useParams } from "react-router-dom";
import ElementIndex from "../../editor/components/elements/ElementIndex";
import { Icon } from "@iconify/react";
import AudioElement from "../../editor/components/elements/AudioElement";
import { useBookmarks } from "../hooks/useBookmarks";

const HtmlContent = ({ page, index, height, width, miniView, isMobile }) => {
  const noteTakingLayer = useSelector((state) => state.ebook.noteTakingLayer);
  const { elements, style, id: pageId } = page;
  const { id, slug } = useParams();
  const location = useLocation();
  const isFlipBook = location.pathname.includes("flipbooks");
  const isTemplatePreview = location.pathname.includes("templates");
  const ebookParam = location.pathname.includes("admin") ? id : slug;

  // Use our custom hook for bookmark operations
  const { toggleBookmark, isPageBookmarked } = useBookmarks();
  useEffect(() => {
    const handleLinks = () => {
      const pageElement = document.getElementById(`page-${index}`);
      if (!pageElement) return;

      pageElement.querySelectorAll('a').forEach(link => {
        // Ensure attributes exist
        link.setAttribute('target', '_blank');
        link.setAttribute('rel', 'noopener noreferrer');

        // Add click handler as backup
        link.onclick = (e) => {
          // Only intervene if target="_blank" isn't working
          if (!e.ctrlKey && !e.metaKey && !e.shiftKey) {
            e.preventDefault();
            window.open(link.href, '_blank');
          }
        };
      });
    };

    handleLinks();

    // Set up mutation observer for dynamic content
    const observer = new MutationObserver(handleLinks);
    observer.observe(document.getElementById(`page-${index}`), {
      childList: true,
      subtree: true
    });

    return () => observer.disconnect();
  }, [index, page.elements]);

  const renderElement = (element) => {
    switch (element.type) {
      case "text":
        return (
          <TextElement
            key={element.id}
            element={element}
            setEditingElementId={() => null}
            editingElementId={() => null}
            readonly={true}
          />
        );
      case "image":
        return (
          <img
            key={element.id}
            src={element.content}
            alt=""
            className="w-full h-full object-cover"
            style={element.style}
            draggable={false}
          />
        );
      case "mcq":
        return <MCQElement key={element.id} element={element} isMobile={isMobile} />;
      case "true-false":
        return <TrueFalseElement key={element.id} element={element} isMobile={isMobile} />;
      case "video":
        return (
          <VideoElement
            key={element.id}
            element={element}
            miniView={miniView}
            isMobile={isMobile}
          />
        );
      case "flashcard":
        return <FlashcardElement key={element.id} element={element}  isMobile={isMobile}/>;
      case "shape":
        return <ShapesElement key={element.id} element={element} />;
      case "index-list":
        return <ElementIndex key={element.id} element={element} />;
      case "audio":
        return (
          <AudioElement
            key={element.id}
            element={element}
            miniView={miniView}
          />
        );
      default:
        return null;
    }
  };

  // Handle bookmark click - uses the toggleBookmark function from our hook
  const handleBookmarkClick = () => {
    toggleBookmark(pageId);
  };

  // Check if the current page is bookmarked
  const isBookmarked = isPageBookmarked(pageId);

  return (
    <div
      id={`page-${index}`}
      className="relative overflow-hidden shadow-lg bg-white border"
      style={{
        width: width,
        height: height,
        backgroundColor: JSON.parse(style)?.backgroundColor || '#ffffff',
        backgroundImage: JSON.parse(style)?.backgroundImage || 'none',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        backgroundPosition: JSON.parse(style)?.backgroundPosition || 'center'
      }}
    >
      {elements.map((element) => (
        <div
          key={element.id}
          style={{
            position: "absolute",
            width: element?.size?.width,
            height: element?.size?.height,
            // transform: `translate(${element?.position?.x}px, ${element?.position?.y}px)`,
            transform: `translate(${element?.position?.x || 0}px, ${element?.position?.y || 0}px) rotate(${element?.rotation || 0}deg)`,

          }}
        >
          {renderElement(element)}
        </div>
      ))}

      {/* Page number indicator */}
      {index !== 0 && (
        <div className="rounded-full border w-6 h-6 flex items-center justify-center px-2 py-1 absolute bottom-4 right-4 text-gray-500 text-sm">
          {index}
        </div>
      )}
      <div className="absolute top-2 right-2">
        {!isFlipBook && !isTemplatePreview  && (
          <div className="absolute top-2 right-2">
            <Icon
              icon={isBookmarked ? "mdi:bookmark" : "mdi:bookmark-outline"}
              className={`text-2xl cursor-pointer ${isBookmarked ? "text-green-500" : "text-gray-500"
                }`}
              onClick={handleBookmarkClick}
            />
          </div>
        )}
      </div>

      {/* Drawing Layer */}
      {noteTakingLayer && (
        <DrawingLayer
          ebookId={ebookParam}
          pageId={pageId}
          width={width}
          height={height}
        />
      )}
    </div>
  );
};

export default HtmlContent;
