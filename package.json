{"name": "your-epub-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "postinstall": "node ./postinstall.js"}, "dependencies": {"@google/generative-ai": "^0.21.0", "@headlessui/react": "^2.2.0", "@iconify/react": "^5.0.2", "@react-oauth/google": "^0.12.1", "@react-pdf-viewer/core": "^3.12.0", "@reduxjs/toolkit": "^2.3.0", "@tanstack/react-query": "^5.60.6", "@tanstack/react-table": "^8.20.5", "@tinymce/tinymce-react": "^5.1.1", "axios": "^1.7.7", "date-fns": "^4.1.0", "formik": "^2.4.6", "framer-motion": "^11.11.10", "fs-extra": "^11.2.0", "howler": "^2.2.4", "html2canvas": "^1.4.1", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.2", "i18next-localstorage-backend": "^4.2.0", "js-cookie": "^3.0.5", "jspdf": "^2.5.2", "lucide-react": "^0.344.0", "moment": "^2.30.1", "react": "^18.3.1", "react-datepicker": "^8.1.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-i18next": "^15.4.0", "react-konva": "^18.2.10", "react-moveable": "^0.56.0", "react-pageflip": "^2.0.3", "react-pdf": "^7.7.1", "react-redux": "^9.1.2", "react-router-dom": "^6.27.0", "react-select": "^5.8.3", "react-table": "^7.8.0", "react-toastify": "^10.0.6", "redux": "^5.0.1", "redux-persist": "^6.0.0", "redux-undo": "^1.1.0", "sonner": "^1.7.1", "sweetalert2": "^11.14.5", "swiper": "^11.2.2", "uuid": "^11.0.2", "yup": "^1.4.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}