import React from 'react';

const PageWithGuides = ({ pageWidth = 816, pageHeight = 1056, margin = 50, gutter = 50, numColumns = 2 }) => {
  const columnWidth = (pageWidth - 2 * margin - (numColumns - 1) * gutter) / numColumns;
  const columns = Array.from({ length: numColumns }, (_, i) => ({
    left: margin + i * (columnWidth + gutter),
    width: columnWidth,
  }));

  return (
    <div
      style={{
        pointerEvents: 'none', // Prevents the guides from capturing click events
        background: 'transparent',
        width: pageWidth,
        height: pageHeight,
        position: 'relative',
        padding: `${margin}px`,
        boxSizing: 'border-box', // Ensures padding is included in the width and height
      }}
    >
      {columns.map((col, index) => (
        <div
          key={index}
          style={{
            position: 'absolute',
            top: margin, // Start from the top margin
            left: col.left,
            width: col.width,
            height: `calc(100% - ${2 * margin}px)`, // Adjust height for top and bottom margins
            border: '1px solid rgba(0, 0, 255, 0.1)',

          }}
        />
      ))}
    </div>
  );
};

export default PageWithGuides;