// ResizableWrapper.jsx
import React, { useRef, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  deleteElement,
  updateElement,
  copyElements,
  pasteElements,
} from "../store/pagesSlice";
import Moveable from "react-moveable";
import { TrashIcon, Copy, GripVertical } from "lucide-react";
import ContextMenu from "./ContextMenu";

const ResizableWrapper = ({
  id,
  children,
  className,
  onClick,
  onDoubleClick,
  position,
  size,
  style,
  rotation = 0,
  pageWidth,
  type,
}) => {
  const dispatch = useDispatch();
  const selectedElements = useSelector(
    (state) => state.pages.present.selectedElements
  );
  const isSelected = selectedElements.includes(id);
  const isMovable = true;
  const isPreviewActive = useSelector((state) => state.toolbar.isPreviewActive);
  const elementRef = useRef(null);
  const moveableRef = useRef(null);
  const dragHandleRef = useRef(null);
  const contentRef = useRef(null);
  const positionRef = useRef({ x: position?.x || 0, y: position?.y || 0 });
  const sizeRef = useRef({ width: size?.width || 0, height: size?.height || 0 });
  const rotationRef = useRef(rotation || 0);
  const otherElements = useRef([]);
  const [menuVisible, setMenuVisible] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isHovering, setIsHovering] = useState(false);

  const applyClip = (x, width) => {
    if (!contentRef.current) return;
    const overflowLeft = Math.max(0, -x);
    const overflowRight = Math.max(0, x + width - pageWidth);
    if (overflowLeft || overflowRight) {
      contentRef.current.style.clipPath = `inset(0px ${overflowRight}px 0px ${overflowLeft}px)`;
    } else {
      contentRef.current.style.clipPath = "none";
    }
  };

  useEffect(() => {
    rotationRef.current = rotation || 0;
  }, [rotation]);

  useEffect(() => {
    if (elementRef.current) {
      const elements = Array.from(
        document.getElementsByClassName("moveable-element")
      );
      otherElements.current = elements.filter((el) => el !== elementRef.current);
    }
  }, []);

  useEffect(() => {
    if (moveableRef.current) moveableRef.current.updateRect();
    positionRef.current = { x: position?.x || 0, y: position?.y || 0 };
    sizeRef.current = { width: size?.width || 0, height: size?.height || 0 };
    applyClip(positionRef.current.x, sizeRef.current.width);
  }, [position, size]);

  const handleDrag = ({ target, beforeTranslate }) => {
    const [x, y] = beforeTranslate;
    const adjustedX =
      type === "image"
        ? x
        : Math.max(0, Math.min(x, pageWidth - sizeRef.current.width));
    target.style.transform = `translate(${adjustedX}px, ${y}px) rotate(${rotationRef.current}deg)`;
    positionRef.current = { x: adjustedX, y };
    applyClip(adjustedX, sizeRef.current.width);
  };

  const handleDragEnd = () => {
    const { x, y } = positionRef.current;
    dispatch(
      updateElement({
        id,
        updates: { position: { x, y } },
      })
    );
    setIsDragging(false);
  };

  const handleResize = ({ target, width, height, drag, direction }) => {
    const { beforeTranslate } = drag;
    const [x, y] = beforeTranslate;
    const adjustedX =
      type === "image" ? x : Math.max(0, Math.min(x, pageWidth - width));

    if (type === "image") {
      const horizontal = direction.includes("w") || direction.includes("e");
      const vertical = direction.includes("n") || direction.includes("s");
      if (horizontal && !vertical) {
        height = sizeRef.current.height;
      } else if (vertical && !horizontal) {
        width = sizeRef.current.width;
      }
      const img = target.querySelector("img");
      if (img) {
        img.style.width = "100%";
        img.style.height = "100%";
        img.style.objectFit = "fill";
      }
    }

    target.style.width = `${width}px`;
    target.style.height = `${height}px`;
    target.style.transform = `translate(${adjustedX}px, ${y}px) rotate(${rotationRef.current}deg)`;
    positionRef.current = { x: adjustedX, y };
    sizeRef.current = { width, height };
    applyClip(adjustedX, width);
  };

  const handleResizeEnd = () => {
    const { x, y } = positionRef.current;
    const { width, height } = sizeRef.current;
    dispatch(
      updateElement({
        id,
        updates: {
          size: { width, height },
          position: { x, y },
        },
      })
    );
  };

  const handleRotate = ({ target, beforeRotate }) => {
    const currentRotation = beforeRotate;
    rotationRef.current = currentRotation;
    target.style.transform = `translate(${positionRef.current.x}px, ${positionRef.current.y}px) rotate(${currentRotation}deg)`;
  };

  const handleRotateEnd = () => {
    dispatch(
      updateElement({
        id,
        updates: { rotation: rotationRef.current },
      })
    );
  };

  const handleContextMenu = (e) => {
    e.preventDefault();
    setMenuVisible(true);
  };

  const closeContextMenu = () => setMenuVisible(false);

  const handleDragStart = (e) => {
    e.stopPropagation();
    setIsDragging(true);
    if (moveableRef.current) moveableRef.current.dragStart(e);
  };

  const handleMouseEnter = () => setIsHovering(true);
  const handleMouseLeave = () => setIsHovering(false);

  const allowDirectDrag = type === "image"|| type === "shape";
  const showControls = isSelected && !isPreviewActive;

  return (
    <>
      <div
        ref={elementRef}
        className={`moveable-element ${className}`}
        onClick={(e) => {
          if (!isPreviewActive) {
            closeContextMenu();
            onClick && onClick(e);
          }
        }}
        onDoubleClick={(e) => {
          if (!isPreviewActive) onDoubleClick && onDoubleClick(e);
        }}
        onContextMenu={handleContextMenu}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        style={{
          position: "absolute",
          width: size?.width || 0,
          height: size?.height || 0,
          transform: `translate(${position?.x || 0}px, ${
            position?.y || 0
          }px) rotate(${rotation || 0}deg)`,
          pointerEvents: isPreviewActive ? "none" : "auto",
          // padding: "3px",
          cursor: isSelected && isHovering && allowDirectDrag ? "move" : "default",
          // ...style,
        }}
      >
        <div
          ref={contentRef}
          style={{
            width: "100%",
            height: "100%",
          }}
        >
          {children}
        </div>

        {showControls && (
          <div className="absolute top-[-30px] right-0 flex gap-2 items-center bg-white bg-opacity-80 p-1 rounded">
            <button
              className="border p-1 text-red-500 cursor-pointer bg-transparent hover:bg-red-50 rounded"
              onClick={(e) => {
                e.stopPropagation();
                dispatch(deleteElement());
              }}
              title="Delete"
            >
              <TrashIcon size={16} />
            </button>
            <button
              className="border p-1 text-blue-500 cursor-pointer bg-transparent hover:bg-blue-50 rounded"
              onClick={(e) => {
                e.stopPropagation();
                dispatch(copyElements());
                dispatch(pasteElements());
              }}
              title="Duplicate"
            >
              <Copy size={16} />
            </button>
            {!allowDirectDrag && (
              <div
                ref={dragHandleRef}
                className={`border p-1 cursor-move bg-transparent hover:bg-gray-50 rounded ${
                  isDragging ? "text-gray-700 ring-1 ring-blue-400" : "text-gray-400"
                }`}
                onMouseDown={handleDragStart}
                title="Drag"
              >
                <GripVertical size={16} />
              </div>
            )}
          </div>
        )}
      </div>

      {isSelected && menuVisible && !isPreviewActive && (
        <ContextMenu
          id={id}
          position={position}
          size={size}
          onClose={closeContextMenu}
        />
      )}

      {isSelected && elementRef.current && !isPreviewActive && (
        <Moveable
          ref={moveableRef}
          target={elementRef.current}
          container={null}
          origin={false}
          draggable={isMovable}
          resizable={isMovable}
          rotatable={isMovable}
          dragTarget={allowDirectDrag ? elementRef.current : dragHandleRef.current}
          onDrag={handleDrag}
          onDragEnd={handleDragEnd}
          onResize={handleResize}
          onResizeEnd={handleResizeEnd}
          onRotate={handleRotate}
          onRotateEnd={handleRotateEnd}
          snappable
          snapThreshold={5}
          elementGuidelines={otherElements.current}
          keepRatio={false}
          renderDirections={[
            "nw",
            "ne",
            "sw",
            "se",
            "n",
            "w",
            "s",
            "e",
          ]}
          bounds={type === "image" ? undefined : { left: 0, right: pageWidth, top: 0 }}
          rotation={rotation || 0}
        />
      )}
    </>
  );
};

export default ResizableWrapper;
