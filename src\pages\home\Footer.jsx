import { Link } from 'react-router-dom';
import { Facebook, Twitter, Instagram } from 'lucide-react';
import { useTranslation } from 'react-i18next';

export default function Footer() {
  const { t } = useTranslation();
  const footerSections = [
    {
      title: t('footer.product'),
      links: [
        { name: t('footer.features'), href: '#features' },
        { name: t('footer.howItWorks'), href: '#how-it-works' },
        { name: t('footer.pricing'), href: '#pricing' },
        { name: t('footer.faq'), href: '#faq' },
      ],
    },
    {
      title: t('footer.company'),
      links: [
        { name: t('footer.about'), href: '#about' },
        { name: t('footer.blog'), href: '#blog' },
        { name: t('footer.careers'), href: '#careers' },
        { name: t('footer.contact'), href: '#contact' },
      ],
    },
    {
      title: t('footer.resources'),
      links: [
        { name: t('footer.documentation'), href: '#docs' },
        { name: t('footer.templates'), href: '#templates' },
        { name: t('footer.examples'), href: '#examples' },
        { name: t('footer.support'), href: '#support' },
      ],
    },
    {
      title: t('footer.legal'),
      links: [
        { name: t('footer.privacy'), href: '#privacy' },
        { name: t('footer.terms'), href: '#terms' },
        { name: t('footer.security'), href: '#security' },
      ],
    },
  ];

  const socialLinks = [
    { icon: Facebook, href: '#' },
    { icon: Twitter, href: '#' },
    { icon: Instagram, href: '#' },
   
  ];

  return (
    <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {footerSections.map((section) => (
            <div key={section.title}>
              <h3 className="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider">
                {section.title}
              </h3>
              <ul className="mt-4 space-y-4 list-none ml-[-20px]">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="text-base text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="mt-12 border-t border-gray-200 dark:border-gray-700 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-6">
              {socialLinks.map((link, index) => (
                <a
                  key={index}
                  href={link.href}
                  className="text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                >
                  <link.icon className="w-6 h-6" />
                </a>
              ))}
            </div>
            <p className="mt-4 md:mt-0 text-base text-gray-500 dark:text-gray-400">
              &copy; {new Date().getFullYear()} YouREPUB. {t('footer.allRightsReserved')}
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}