// PdfFlipBookContainer.jsx
import React, { useState, useEffect, useRef } from "react";
import HTM<PERSON>lipBook from "react-pageflip";
import { Document, Page, pdfjs } from "react-pdf";
import { Loader2 } from "lucide-react";
import Pagination from "./Pagination";

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

const PdfFlipBookContainer = ({ pdfUrl }) => {
  const [pageIndex, setPageIndex] = useState(0);
  const [numPages, setNumPages] = useState(0);
  const [loading, setLoading] = useState(true);
  const [documentError, setDocumentError] = useState(null);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [scale, setScale] = useState(1);
  const [pdfDimensions, setPdfDimensions] = useState({ width: 0, height: 0 });
  // Only render initially the first two pages (page numbers 1 and 2)// Original scale hook for basic responsive scaling
  const [renderedPages, setRenderedPages] = useState(new Set([1, 2]));
  const bookRef = useRef(null);

  useEffect(() => {
    const loadPdfDimensions = async () => {
      try {
        const loadingTask = pdfjs.getDocument(pdfUrl);
        const pdf = await loadingTask.promise;
        const page = await pdf.getPage(1);
        const viewport = page.getViewport({ scale: 1 });
        setPdfDimensions({ width: viewport.width, height: viewport.height });
        setNumPages(pdf.numPages);
      } catch (error) {
        console.error("Error loading PDF dimensions:", error);
        setDocumentError("Failed to load PDF dimensions.");
      }
    };
    loadPdfDimensions();
  }, [pdfUrl]);

  useEffect(() => {
    const updateDimensions = () => {
      if (!pdfDimensions.width || !pdfDimensions.height) return;
      const isMobileView = window.innerWidth <= 768;
      setIsMobile(isMobileView);
      const availableHeight = window.innerHeight * 0.9;
      const availableWidth = window.innerWidth * 0.9;
      let newScale = isMobileView
        ? Math.min(availableWidth / pdfDimensions.width, availableHeight / pdfDimensions.height)
        : Math.min(availableWidth / (pdfDimensions.width * 2), availableHeight / pdfDimensions.height);
      setScale(Math.min(newScale, 1.5));
    };
    updateDimensions();
    window.addEventListener("resize", updateDimensions);
    return () => window.removeEventListener("resize", updateDimensions);
  }, [pdfDimensions]);

  useEffect(() => {
    if (bookRef.current?.pageFlip) {
      bookRef.current.pageFlip().flip(pageIndex);
    }
  }, [pageIndex]);

  // Update the set of rendered pages based on which pages become visible.
  const handleFlip = (e) => {
    const newPageIndex = e.data;
    setPageIndex(newPageIndex);
    const visiblePages = [];
    // Pages are 1-indexed in our mapping.
    const firstVisible = newPageIndex + 1;
    visiblePages.push(firstVisible);
    if (!isMobile && firstVisible + 1 <= numPages) {
      visiblePages.push(firstVisible + 1);
    }
    setRenderedPages((prev) => {
      const newSet = new Set(prev);
      visiblePages.forEach((page) => newSet.add(page));
      return newSet;
    });
  };

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
    setLoading(false);
    setDocumentError(null);
  };

  const onDocumentLoadError = (error) => {
    console.error("Error loading PDF:", error);
    setDocumentError("Failed to load PDF. Please try again later.");
    setLoading(false);
  };

  const handleNext = () => {
    if (bookRef.current?.pageFlip) {
      bookRef.current.pageFlip().flipNext();
    }
  };

  const handlePrevious = () => {
    if (bookRef.current?.pageFlip) {
      bookRef.current.pageFlip().flipPrev();
    }
  };

  if (documentError) {
    return (
      <div className="flex items-center justify-center h-[90vh] text-red-500">
        {documentError}
      </div>
    );
  }

  if (!pdfDimensions.width || !pdfDimensions.height) {
    return (
      <div className="flex items-center justify-center h-[90vh]">
        <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
      </div>
    );
  }

  const scaledWidth = pdfDimensions.width * scale;
  const scaledHeight = pdfDimensions.height * scale;

  return (
    <div className="flex justify-center items-center  h-[90vh] lg:h-[80vh] overflow-hidden p-1">
      <Document
        file={pdfUrl}
        onLoadSuccess={onDocumentLoadSuccess}
        onLoadError={onDocumentLoadError}
        loading={
          <div className="flex items-center justify-center h-full">
            <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
          </div>
        }
      >
        {!loading && (
          <HTMLFlipBook
            ref={bookRef}
            width={scaledWidth}
            height={scaledHeight}
            size="stretch"
            minWidth={scaledWidth}
            maxWidth={scaledWidth}
            minHeight={scaledHeight}
            maxHeight={scaledHeight}
            showCover={false}
            flippingTime={800}
            className={`shadow-xl ${!isMobile ? 'double-page-view' : ''}`}
            startPage={pageIndex}
            drawShadow
            usePortrait={isMobile}
            startZIndex={0}
            autoSize={false}
            maxShadowOpacity={0.5}
            mobileScrollSupport
            clickEventForward
            useMouseEvents
            showPageCorners
            disableFlipByClick={isMobile ? false : true}
            singlePage={isMobile}
            onFlip={handleFlip}
            key={window.innerWidth}
          >
            {Array.from({ length: numPages }, (_, i) => i + 1).map((pageNumber) => (
              <div key={pageNumber} className="relative bg-white page-container">
                {renderedPages.has(pageNumber) ? (
                  <Page
                    pageNumber={pageNumber}
                    width={scaledWidth}
                    height={scaledHeight}
                    renderTextLayer={false}
                    renderAnnotationLayer={false}
                    loading={
                      <div className="flex items-center justify-center h-full">
                        <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
                      </div>
                    }
                  />
                ) : (
                  <div
                    style={{ width: scaledWidth, height: scaledHeight }}
                    className="flex items-center justify-center"
                  >
                    <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
                  </div>
                )}
              </div>
            ))}
          </HTMLFlipBook>
        )}
      </Document>
      <div className="z-50 fixed top-0 left-0 w-full h-full pointer-events-none md:block hidden">
      <Pagination
        pageIndex={pageIndex}
        totalPages={numPages}
        onPrevious={handlePrevious}
        onNext={handleNext}
        // Pass isMobile if you want to fix the "Next" button disable logic (see Step 3)
        isMobile={isMobile}
      />
    </div>
    </div>
  );
};

export default PdfFlipBookContainer;
